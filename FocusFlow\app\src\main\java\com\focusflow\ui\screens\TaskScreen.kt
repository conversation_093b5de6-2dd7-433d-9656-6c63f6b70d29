package com.focusflow.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.data.model.Task
import com.focusflow.ui.components.*
import com.focusflow.ui.viewmodel.TaskViewModel
import com.focusflow.ui.viewmodel.TaskFilter
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

@Composable
fun TaskScreen(
    viewModel: TaskViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val filteredTasks by viewModel.filteredTasks.collectAsStateWithLifecycle()
    val selectedFilter by viewModel.selectedFilter.collectAsStateWithLifecycle()
    val recommendedTasks by viewModel.recommendedTasks.collectAsStateWithLifecycle()
    val overdueTasks by viewModel.overdueTasks.collectAsStateWithLifecycle()
    
    var showAddTaskDialog by remember { mutableStateOf(false) }
    var showTaskBreakdownDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header with statistics
        TaskHeaderCard(
            statistics = uiState.taskStatistics,
            isLoading = uiState.isLoading
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // ADHD-friendly quick actions
        ADHDQuickActionsCard(
            recommendedTasks = recommendedTasks,
            overdueTasks = overdueTasks,
            onTaskClick = { task -> viewModel.completeTask(task) },
            onBreakdownClick = { task -> 
                viewModel.requestTaskBreakdown(task)
                showTaskBreakdownDialog = true
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Filter chips
        TaskFilterChips(
            selectedFilter = selectedFilter,
            onFilterSelected = { viewModel.setFilter(it) }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Tasks list
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (filteredTasks.isEmpty()) {
            EmptyTasksState(
                filter = selectedFilter,
                onAddTaskClick = { showAddTaskDialog = true }
            )
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(filteredTasks) { task ->
                    TaskItem(
                        task = task,
                        onCompleteClick = { viewModel.completeTask(task) },
                        onEditClick = { /* TODO: Implement edit */ },
                        onDeleteClick = { viewModel.deleteTask(task) },
                        onBreakdownClick = { 
                            viewModel.requestTaskBreakdown(task)
                            showTaskBreakdownDialog = true
                        },
                        isProcrastinated = viewModel.detectProcrastination(task)
                    )
                }
            }
        }
    }
    
    // Floating Action Button
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.BottomEnd
    ) {
        FloatingActionButton(
            onClick = { showAddTaskDialog = true },
            modifier = Modifier.padding(16.dp),
            backgroundColor = MaterialTheme.colors.primary
        ) {
            Icon(Icons.Default.Add, contentDescription = "Add Task")
        }
    }
    
    // Add Task Dialog
    if (showAddTaskDialog) {
        AddTaskDialog(
            onDismiss = { showAddTaskDialog = false },
            onAddTask = { title, description, dueDate, priority, category, duration ->
                viewModel.createTask(title, description, dueDate, priority, category, duration)
                showAddTaskDialog = false
            },
            availableCategories = uiState.availableCategories
        )
    }
    
    // Task Breakdown Dialog
    if (showTaskBreakdownDialog && uiState.taskBeingBrokenDown != null) {
        TaskBreakdownDialog(
            task = uiState.taskBeingBrokenDown!!,
            suggestedSubtasks = uiState.suggestedSubtasks,
            isLoading = uiState.isBreakingDownTask,
            onAccept = { 
                viewModel.acceptTaskBreakdown()
                showTaskBreakdownDialog = false
            },
            onReject = { 
                viewModel.rejectTaskBreakdown()
                showTaskBreakdownDialog = false
            },
            onDismiss = { 
                viewModel.rejectTaskBreakdown()
                showTaskBreakdownDialog = false
            }
        )
    }
    
    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // Show snackbar or handle error
            viewModel.clearError()
        }
    }
    
    // Success message
    uiState.lastAction?.let { action ->
        LaunchedEffect(action) {
            // Show success message
            viewModel.clearLastAction()
        }
    }
}

@Composable
fun TaskHeaderCard(
    statistics: com.focusflow.data.repository.TaskStatistics?,
    isLoading: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "📝 Task Management",
                style = MaterialTheme.typography.h5,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            if (isLoading) {
                CircularProgressIndicator(modifier = Modifier.size(24.dp))
            } else if (statistics != null) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "Active",
                        value = statistics.incompleteCount.toString(),
                        color = Color(0xFFFF9800)
                    )
                    StatisticItem(
                        label = "Completed",
                        value = statistics.completedThisWeek.toString(),
                        color = Color(0xFF4CAF50)
                    )
                    StatisticItem(
                        label = "Overdue",
                        value = statistics.overdueCount.toString(),
                        color = Color(0xFFF44336)
                    )
                }
            }
        }
    }
}

@Composable
fun StatisticItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
    }
}

@Composable
fun ADHDQuickActionsCard(
    recommendedTasks: List<Task>,
    overdueTasks: List<Task>,
    onTaskClick: (Task) -> Unit,
    onBreakdownClick: (Task) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(0xFFF3E5F5)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "🧠 ADHD-Friendly Quick Actions",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Quick win tasks (short duration, easy to complete)
            if (recommendedTasks.isNotEmpty()) {
                Text(
                    text = "⚡ Quick Wins (15 min or less)",
                    style = MaterialTheme.typography.subtitle2,
                    fontWeight = FontWeight.Medium
                )
                
                recommendedTasks.take(2).forEach { task ->
                    QuickTaskItem(
                        task = task,
                        onClick = { onTaskClick(task) }
                    )
                }
            }
            
            // Overdue tasks that need attention
            if (overdueTasks.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "⚠️ Needs Attention",
                    style = MaterialTheme.typography.subtitle2,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFFF44336)
                )
                
                overdueTasks.take(1).forEach { task ->
                    QuickTaskItem(
                        task = task,
                        onClick = { onBreakdownClick(task) },
                        actionText = "Break Down"
                    )
                }
            }
        }
    }
}

@Composable
fun QuickTaskItem(
    task: Task,
    onClick: () -> Unit,
    actionText: String = "Complete"
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = task.title,
                style = MaterialTheme.typography.body2,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = "${task.estimatedDuration ?: 15} min",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
        
        TextButton(onClick = onClick) {
            Text(actionText)
        }
    }
}
