package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.DashboardWidget;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class DashboardWidgetDao_Impl implements DashboardWidgetDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<DashboardWidget> __insertionAdapterOfDashboardWidget;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<DashboardWidget> __deletionAdapterOfDashboardWidget;

  private final EntityDeletionOrUpdateAdapter<DashboardWidget> __updateAdapterOfDashboardWidget;

  private final SharedSQLiteStatement __preparedStmtOfUpdateWidgetPosition;

  private final SharedSQLiteStatement __preparedStmtOfUpdateWidgetVisibility;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLastRefresh;

  private final SharedSQLiteStatement __preparedStmtOfUpdateWidgetConfiguration;

  public DashboardWidgetDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfDashboardWidget = new EntityInsertionAdapter<DashboardWidget>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `dashboard_widgets` (`id`,`widgetType`,`displayName`,`position`,`isVisible`,`isEnabled`,`size`,`configuration`,`refreshInterval`,`lastUpdated`,`dataSource`,`customTitle`,`colorScheme`,`customColors`,`showHeader`,`showFooter`,`headerText`,`footerText`,`iconName`,`animationEnabled`,`clickAction`,`longPressAction`,`swipeActions`,`accessibilityLabel`,`accessibilityHint`,`isCustomizable`,`requiresPermission`,`permissionType`,`dataRetentionDays`,`cacheEnabled`,`offlineSupport`,`errorFallback`,`loadingIndicator`,`updateAnimation`,`priority`,`dependencies`,`createdDate`,`modifiedDate`,`userNotes`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DashboardWidget entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getWidgetType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getWidgetType());
        }
        if (entity.getDisplayName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDisplayName());
        }
        statement.bindLong(4, entity.getPosition());
        final int _tmp = entity.isVisible() ? 1 : 0;
        statement.bindLong(5, _tmp);
        final int _tmp_1 = entity.isEnabled() ? 1 : 0;
        statement.bindLong(6, _tmp_1);
        if (entity.getSize() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getSize());
        }
        if (entity.getConfiguration() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getConfiguration());
        }
        statement.bindLong(9, entity.getRefreshInterval());
        final String _tmp_2 = __converters.fromLocalDateTime(entity.getLastUpdated());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        if (entity.getDataSource() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getDataSource());
        }
        if (entity.getCustomTitle() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getCustomTitle());
        }
        if (entity.getColorScheme() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getColorScheme());
        }
        if (entity.getCustomColors() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getCustomColors());
        }
        final int _tmp_3 = entity.getShowHeader() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        final int _tmp_4 = entity.getShowFooter() ? 1 : 0;
        statement.bindLong(16, _tmp_4);
        if (entity.getHeaderText() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getHeaderText());
        }
        if (entity.getFooterText() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getFooterText());
        }
        if (entity.getIconName() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getIconName());
        }
        final int _tmp_5 = entity.getAnimationEnabled() ? 1 : 0;
        statement.bindLong(20, _tmp_5);
        if (entity.getClickAction() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getClickAction());
        }
        if (entity.getLongPressAction() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getLongPressAction());
        }
        if (entity.getSwipeActions() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getSwipeActions());
        }
        if (entity.getAccessibilityLabel() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getAccessibilityLabel());
        }
        if (entity.getAccessibilityHint() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getAccessibilityHint());
        }
        final int _tmp_6 = entity.isCustomizable() ? 1 : 0;
        statement.bindLong(26, _tmp_6);
        final int _tmp_7 = entity.getRequiresPermission() ? 1 : 0;
        statement.bindLong(27, _tmp_7);
        if (entity.getPermissionType() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getPermissionType());
        }
        statement.bindLong(29, entity.getDataRetentionDays());
        final int _tmp_8 = entity.getCacheEnabled() ? 1 : 0;
        statement.bindLong(30, _tmp_8);
        final int _tmp_9 = entity.getOfflineSupport() ? 1 : 0;
        statement.bindLong(31, _tmp_9);
        if (entity.getErrorFallback() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getErrorFallback());
        }
        if (entity.getLoadingIndicator() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getLoadingIndicator());
        }
        if (entity.getUpdateAnimation() == null) {
          statement.bindNull(34);
        } else {
          statement.bindString(34, entity.getUpdateAnimation());
        }
        statement.bindLong(35, entity.getPriority());
        if (entity.getDependencies() == null) {
          statement.bindNull(36);
        } else {
          statement.bindString(36, entity.getDependencies());
        }
        final String _tmp_10 = __converters.fromLocalDateTime(entity.getCreatedDate());
        if (_tmp_10 == null) {
          statement.bindNull(37);
        } else {
          statement.bindString(37, _tmp_10);
        }
        final String _tmp_11 = __converters.fromLocalDateTime(entity.getModifiedDate());
        if (_tmp_11 == null) {
          statement.bindNull(38);
        } else {
          statement.bindString(38, _tmp_11);
        }
        if (entity.getUserNotes() == null) {
          statement.bindNull(39);
        } else {
          statement.bindString(39, entity.getUserNotes());
        }
      }
    };
    this.__deletionAdapterOfDashboardWidget = new EntityDeletionOrUpdateAdapter<DashboardWidget>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `dashboard_widgets` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DashboardWidget entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfDashboardWidget = new EntityDeletionOrUpdateAdapter<DashboardWidget>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `dashboard_widgets` SET `id` = ?,`widgetType` = ?,`displayName` = ?,`position` = ?,`isVisible` = ?,`isEnabled` = ?,`size` = ?,`configuration` = ?,`refreshInterval` = ?,`lastUpdated` = ?,`dataSource` = ?,`customTitle` = ?,`colorScheme` = ?,`customColors` = ?,`showHeader` = ?,`showFooter` = ?,`headerText` = ?,`footerText` = ?,`iconName` = ?,`animationEnabled` = ?,`clickAction` = ?,`longPressAction` = ?,`swipeActions` = ?,`accessibilityLabel` = ?,`accessibilityHint` = ?,`isCustomizable` = ?,`requiresPermission` = ?,`permissionType` = ?,`dataRetentionDays` = ?,`cacheEnabled` = ?,`offlineSupport` = ?,`errorFallback` = ?,`loadingIndicator` = ?,`updateAnimation` = ?,`priority` = ?,`dependencies` = ?,`createdDate` = ?,`modifiedDate` = ?,`userNotes` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DashboardWidget entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getWidgetType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getWidgetType());
        }
        if (entity.getDisplayName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDisplayName());
        }
        statement.bindLong(4, entity.getPosition());
        final int _tmp = entity.isVisible() ? 1 : 0;
        statement.bindLong(5, _tmp);
        final int _tmp_1 = entity.isEnabled() ? 1 : 0;
        statement.bindLong(6, _tmp_1);
        if (entity.getSize() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getSize());
        }
        if (entity.getConfiguration() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getConfiguration());
        }
        statement.bindLong(9, entity.getRefreshInterval());
        final String _tmp_2 = __converters.fromLocalDateTime(entity.getLastUpdated());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        if (entity.getDataSource() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getDataSource());
        }
        if (entity.getCustomTitle() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getCustomTitle());
        }
        if (entity.getColorScheme() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getColorScheme());
        }
        if (entity.getCustomColors() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getCustomColors());
        }
        final int _tmp_3 = entity.getShowHeader() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        final int _tmp_4 = entity.getShowFooter() ? 1 : 0;
        statement.bindLong(16, _tmp_4);
        if (entity.getHeaderText() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getHeaderText());
        }
        if (entity.getFooterText() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getFooterText());
        }
        if (entity.getIconName() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getIconName());
        }
        final int _tmp_5 = entity.getAnimationEnabled() ? 1 : 0;
        statement.bindLong(20, _tmp_5);
        if (entity.getClickAction() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getClickAction());
        }
        if (entity.getLongPressAction() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getLongPressAction());
        }
        if (entity.getSwipeActions() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getSwipeActions());
        }
        if (entity.getAccessibilityLabel() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getAccessibilityLabel());
        }
        if (entity.getAccessibilityHint() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getAccessibilityHint());
        }
        final int _tmp_6 = entity.isCustomizable() ? 1 : 0;
        statement.bindLong(26, _tmp_6);
        final int _tmp_7 = entity.getRequiresPermission() ? 1 : 0;
        statement.bindLong(27, _tmp_7);
        if (entity.getPermissionType() == null) {
          statement.bindNull(28);
        } else {
          statement.bindString(28, entity.getPermissionType());
        }
        statement.bindLong(29, entity.getDataRetentionDays());
        final int _tmp_8 = entity.getCacheEnabled() ? 1 : 0;
        statement.bindLong(30, _tmp_8);
        final int _tmp_9 = entity.getOfflineSupport() ? 1 : 0;
        statement.bindLong(31, _tmp_9);
        if (entity.getErrorFallback() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getErrorFallback());
        }
        if (entity.getLoadingIndicator() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getLoadingIndicator());
        }
        if (entity.getUpdateAnimation() == null) {
          statement.bindNull(34);
        } else {
          statement.bindString(34, entity.getUpdateAnimation());
        }
        statement.bindLong(35, entity.getPriority());
        if (entity.getDependencies() == null) {
          statement.bindNull(36);
        } else {
          statement.bindString(36, entity.getDependencies());
        }
        final String _tmp_10 = __converters.fromLocalDateTime(entity.getCreatedDate());
        if (_tmp_10 == null) {
          statement.bindNull(37);
        } else {
          statement.bindString(37, _tmp_10);
        }
        final String _tmp_11 = __converters.fromLocalDateTime(entity.getModifiedDate());
        if (_tmp_11 == null) {
          statement.bindNull(38);
        } else {
          statement.bindString(38, _tmp_11);
        }
        if (entity.getUserNotes() == null) {
          statement.bindNull(39);
        } else {
          statement.bindString(39, entity.getUserNotes());
        }
        statement.bindLong(40, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateWidgetPosition = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE dashboard_widgets SET position = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateWidgetVisibility = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE dashboard_widgets SET isVisible = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLastRefresh = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE dashboard_widgets SET lastUpdated = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateWidgetConfiguration = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE dashboard_widgets SET configuration = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertWidget(final DashboardWidget widget,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfDashboardWidget.insertAndReturnId(widget);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWidget(final DashboardWidget widget,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfDashboardWidget.handle(widget);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWidget(final DashboardWidget widget,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfDashboardWidget.handle(widget);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWidgetPosition(final long id, final int position,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateWidgetPosition.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, position);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateWidgetPosition.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWidgetVisibility(final long id, final boolean isVisible,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateWidgetVisibility.acquire();
        int _argIndex = 1;
        final int _tmp = isVisible ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateWidgetVisibility.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLastRefresh(final long id, final LocalDateTime timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLastRefresh.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(timestamp);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLastRefresh.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWidgetConfiguration(final long id, final String config,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateWidgetConfiguration.acquire();
        int _argIndex = 1;
        if (config == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, config);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateWidgetConfiguration.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<DashboardWidget>> getVisibleWidgets() {
    final String _sql = "SELECT `dashboard_widgets`.`id` AS `id`, `dashboard_widgets`.`widgetType` AS `widgetType`, `dashboard_widgets`.`displayName` AS `displayName`, `dashboard_widgets`.`position` AS `position`, `dashboard_widgets`.`isVisible` AS `isVisible`, `dashboard_widgets`.`isEnabled` AS `isEnabled`, `dashboard_widgets`.`size` AS `size`, `dashboard_widgets`.`configuration` AS `configuration`, `dashboard_widgets`.`refreshInterval` AS `refreshInterval`, `dashboard_widgets`.`lastUpdated` AS `lastUpdated`, `dashboard_widgets`.`dataSource` AS `dataSource`, `dashboard_widgets`.`customTitle` AS `customTitle`, `dashboard_widgets`.`colorScheme` AS `colorScheme`, `dashboard_widgets`.`customColors` AS `customColors`, `dashboard_widgets`.`showHeader` AS `showHeader`, `dashboard_widgets`.`showFooter` AS `showFooter`, `dashboard_widgets`.`headerText` AS `headerText`, `dashboard_widgets`.`footerText` AS `footerText`, `dashboard_widgets`.`iconName` AS `iconName`, `dashboard_widgets`.`animationEnabled` AS `animationEnabled`, `dashboard_widgets`.`clickAction` AS `clickAction`, `dashboard_widgets`.`longPressAction` AS `longPressAction`, `dashboard_widgets`.`swipeActions` AS `swipeActions`, `dashboard_widgets`.`accessibilityLabel` AS `accessibilityLabel`, `dashboard_widgets`.`accessibilityHint` AS `accessibilityHint`, `dashboard_widgets`.`isCustomizable` AS `isCustomizable`, `dashboard_widgets`.`requiresPermission` AS `requiresPermission`, `dashboard_widgets`.`permissionType` AS `permissionType`, `dashboard_widgets`.`dataRetentionDays` AS `dataRetentionDays`, `dashboard_widgets`.`cacheEnabled` AS `cacheEnabled`, `dashboard_widgets`.`offlineSupport` AS `offlineSupport`, `dashboard_widgets`.`errorFallback` AS `errorFallback`, `dashboard_widgets`.`loadingIndicator` AS `loadingIndicator`, `dashboard_widgets`.`updateAnimation` AS `updateAnimation`, `dashboard_widgets`.`priority` AS `priority`, `dashboard_widgets`.`dependencies` AS `dependencies`, `dashboard_widgets`.`createdDate` AS `createdDate`, `dashboard_widgets`.`modifiedDate` AS `modifiedDate`, `dashboard_widgets`.`userNotes` AS `userNotes` FROM dashboard_widgets WHERE isVisible = 1 AND isEnabled = 1 ORDER BY position ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dashboard_widgets"}, new Callable<List<DashboardWidget>>() {
      @Override
      @NonNull
      public List<DashboardWidget> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfWidgetType = 1;
          final int _cursorIndexOfDisplayName = 2;
          final int _cursorIndexOfPosition = 3;
          final int _cursorIndexOfIsVisible = 4;
          final int _cursorIndexOfIsEnabled = 5;
          final int _cursorIndexOfSize = 6;
          final int _cursorIndexOfConfiguration = 7;
          final int _cursorIndexOfRefreshInterval = 8;
          final int _cursorIndexOfLastUpdated = 9;
          final int _cursorIndexOfDataSource = 10;
          final int _cursorIndexOfCustomTitle = 11;
          final int _cursorIndexOfColorScheme = 12;
          final int _cursorIndexOfCustomColors = 13;
          final int _cursorIndexOfShowHeader = 14;
          final int _cursorIndexOfShowFooter = 15;
          final int _cursorIndexOfHeaderText = 16;
          final int _cursorIndexOfFooterText = 17;
          final int _cursorIndexOfIconName = 18;
          final int _cursorIndexOfAnimationEnabled = 19;
          final int _cursorIndexOfClickAction = 20;
          final int _cursorIndexOfLongPressAction = 21;
          final int _cursorIndexOfSwipeActions = 22;
          final int _cursorIndexOfAccessibilityLabel = 23;
          final int _cursorIndexOfAccessibilityHint = 24;
          final int _cursorIndexOfIsCustomizable = 25;
          final int _cursorIndexOfRequiresPermission = 26;
          final int _cursorIndexOfPermissionType = 27;
          final int _cursorIndexOfDataRetentionDays = 28;
          final int _cursorIndexOfCacheEnabled = 29;
          final int _cursorIndexOfOfflineSupport = 30;
          final int _cursorIndexOfErrorFallback = 31;
          final int _cursorIndexOfLoadingIndicator = 32;
          final int _cursorIndexOfUpdateAnimation = 33;
          final int _cursorIndexOfPriority = 34;
          final int _cursorIndexOfDependencies = 35;
          final int _cursorIndexOfCreatedDate = 36;
          final int _cursorIndexOfModifiedDate = 37;
          final int _cursorIndexOfUserNotes = 38;
          final List<DashboardWidget> _result = new ArrayList<DashboardWidget>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DashboardWidget _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWidgetType;
            if (_cursor.isNull(_cursorIndexOfWidgetType)) {
              _tmpWidgetType = null;
            } else {
              _tmpWidgetType = _cursor.getString(_cursorIndexOfWidgetType);
            }
            final String _tmpDisplayName;
            if (_cursor.isNull(_cursorIndexOfDisplayName)) {
              _tmpDisplayName = null;
            } else {
              _tmpDisplayName = _cursor.getString(_cursorIndexOfDisplayName);
            }
            final int _tmpPosition;
            _tmpPosition = _cursor.getInt(_cursorIndexOfPosition);
            final boolean _tmpIsVisible;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp != 0;
            final boolean _tmpIsEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsEnabled);
            _tmpIsEnabled = _tmp_1 != 0;
            final String _tmpSize;
            if (_cursor.isNull(_cursorIndexOfSize)) {
              _tmpSize = null;
            } else {
              _tmpSize = _cursor.getString(_cursorIndexOfSize);
            }
            final String _tmpConfiguration;
            if (_cursor.isNull(_cursorIndexOfConfiguration)) {
              _tmpConfiguration = null;
            } else {
              _tmpConfiguration = _cursor.getString(_cursorIndexOfConfiguration);
            }
            final int _tmpRefreshInterval;
            _tmpRefreshInterval = _cursor.getInt(_cursorIndexOfRefreshInterval);
            final LocalDateTime _tmpLastUpdated;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastUpdated)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastUpdated);
            }
            _tmpLastUpdated = __converters.toLocalDateTime(_tmp_2);
            final String _tmpDataSource;
            if (_cursor.isNull(_cursorIndexOfDataSource)) {
              _tmpDataSource = null;
            } else {
              _tmpDataSource = _cursor.getString(_cursorIndexOfDataSource);
            }
            final String _tmpCustomTitle;
            if (_cursor.isNull(_cursorIndexOfCustomTitle)) {
              _tmpCustomTitle = null;
            } else {
              _tmpCustomTitle = _cursor.getString(_cursorIndexOfCustomTitle);
            }
            final String _tmpColorScheme;
            if (_cursor.isNull(_cursorIndexOfColorScheme)) {
              _tmpColorScheme = null;
            } else {
              _tmpColorScheme = _cursor.getString(_cursorIndexOfColorScheme);
            }
            final String _tmpCustomColors;
            if (_cursor.isNull(_cursorIndexOfCustomColors)) {
              _tmpCustomColors = null;
            } else {
              _tmpCustomColors = _cursor.getString(_cursorIndexOfCustomColors);
            }
            final boolean _tmpShowHeader;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfShowHeader);
            _tmpShowHeader = _tmp_3 != 0;
            final boolean _tmpShowFooter;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfShowFooter);
            _tmpShowFooter = _tmp_4 != 0;
            final String _tmpHeaderText;
            if (_cursor.isNull(_cursorIndexOfHeaderText)) {
              _tmpHeaderText = null;
            } else {
              _tmpHeaderText = _cursor.getString(_cursorIndexOfHeaderText);
            }
            final String _tmpFooterText;
            if (_cursor.isNull(_cursorIndexOfFooterText)) {
              _tmpFooterText = null;
            } else {
              _tmpFooterText = _cursor.getString(_cursorIndexOfFooterText);
            }
            final String _tmpIconName;
            if (_cursor.isNull(_cursorIndexOfIconName)) {
              _tmpIconName = null;
            } else {
              _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            }
            final boolean _tmpAnimationEnabled;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfAnimationEnabled);
            _tmpAnimationEnabled = _tmp_5 != 0;
            final String _tmpClickAction;
            if (_cursor.isNull(_cursorIndexOfClickAction)) {
              _tmpClickAction = null;
            } else {
              _tmpClickAction = _cursor.getString(_cursorIndexOfClickAction);
            }
            final String _tmpLongPressAction;
            if (_cursor.isNull(_cursorIndexOfLongPressAction)) {
              _tmpLongPressAction = null;
            } else {
              _tmpLongPressAction = _cursor.getString(_cursorIndexOfLongPressAction);
            }
            final String _tmpSwipeActions;
            if (_cursor.isNull(_cursorIndexOfSwipeActions)) {
              _tmpSwipeActions = null;
            } else {
              _tmpSwipeActions = _cursor.getString(_cursorIndexOfSwipeActions);
            }
            final String _tmpAccessibilityLabel;
            if (_cursor.isNull(_cursorIndexOfAccessibilityLabel)) {
              _tmpAccessibilityLabel = null;
            } else {
              _tmpAccessibilityLabel = _cursor.getString(_cursorIndexOfAccessibilityLabel);
            }
            final String _tmpAccessibilityHint;
            if (_cursor.isNull(_cursorIndexOfAccessibilityHint)) {
              _tmpAccessibilityHint = null;
            } else {
              _tmpAccessibilityHint = _cursor.getString(_cursorIndexOfAccessibilityHint);
            }
            final boolean _tmpIsCustomizable;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustomizable);
            _tmpIsCustomizable = _tmp_6 != 0;
            final boolean _tmpRequiresPermission;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfRequiresPermission);
            _tmpRequiresPermission = _tmp_7 != 0;
            final String _tmpPermissionType;
            if (_cursor.isNull(_cursorIndexOfPermissionType)) {
              _tmpPermissionType = null;
            } else {
              _tmpPermissionType = _cursor.getString(_cursorIndexOfPermissionType);
            }
            final int _tmpDataRetentionDays;
            _tmpDataRetentionDays = _cursor.getInt(_cursorIndexOfDataRetentionDays);
            final boolean _tmpCacheEnabled;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfCacheEnabled);
            _tmpCacheEnabled = _tmp_8 != 0;
            final boolean _tmpOfflineSupport;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfOfflineSupport);
            _tmpOfflineSupport = _tmp_9 != 0;
            final String _tmpErrorFallback;
            if (_cursor.isNull(_cursorIndexOfErrorFallback)) {
              _tmpErrorFallback = null;
            } else {
              _tmpErrorFallback = _cursor.getString(_cursorIndexOfErrorFallback);
            }
            final String _tmpLoadingIndicator;
            if (_cursor.isNull(_cursorIndexOfLoadingIndicator)) {
              _tmpLoadingIndicator = null;
            } else {
              _tmpLoadingIndicator = _cursor.getString(_cursorIndexOfLoadingIndicator);
            }
            final String _tmpUpdateAnimation;
            if (_cursor.isNull(_cursorIndexOfUpdateAnimation)) {
              _tmpUpdateAnimation = null;
            } else {
              _tmpUpdateAnimation = _cursor.getString(_cursorIndexOfUpdateAnimation);
            }
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpDependencies;
            if (_cursor.isNull(_cursorIndexOfDependencies)) {
              _tmpDependencies = null;
            } else {
              _tmpDependencies = _cursor.getString(_cursorIndexOfDependencies);
            }
            final LocalDateTime _tmpCreatedDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpModifiedDate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfModifiedDate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfModifiedDate);
            }
            _tmpModifiedDate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpUserNotes;
            if (_cursor.isNull(_cursorIndexOfUserNotes)) {
              _tmpUserNotes = null;
            } else {
              _tmpUserNotes = _cursor.getString(_cursorIndexOfUserNotes);
            }
            _item = new DashboardWidget(_tmpId,_tmpWidgetType,_tmpDisplayName,_tmpPosition,_tmpIsVisible,_tmpIsEnabled,_tmpSize,_tmpConfiguration,_tmpRefreshInterval,_tmpLastUpdated,_tmpDataSource,_tmpCustomTitle,_tmpColorScheme,_tmpCustomColors,_tmpShowHeader,_tmpShowFooter,_tmpHeaderText,_tmpFooterText,_tmpIconName,_tmpAnimationEnabled,_tmpClickAction,_tmpLongPressAction,_tmpSwipeActions,_tmpAccessibilityLabel,_tmpAccessibilityHint,_tmpIsCustomizable,_tmpRequiresPermission,_tmpPermissionType,_tmpDataRetentionDays,_tmpCacheEnabled,_tmpOfflineSupport,_tmpErrorFallback,_tmpLoadingIndicator,_tmpUpdateAnimation,_tmpPriority,_tmpDependencies,_tmpCreatedDate,_tmpModifiedDate,_tmpUserNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<DashboardWidget>> getAllWidgets() {
    final String _sql = "SELECT `dashboard_widgets`.`id` AS `id`, `dashboard_widgets`.`widgetType` AS `widgetType`, `dashboard_widgets`.`displayName` AS `displayName`, `dashboard_widgets`.`position` AS `position`, `dashboard_widgets`.`isVisible` AS `isVisible`, `dashboard_widgets`.`isEnabled` AS `isEnabled`, `dashboard_widgets`.`size` AS `size`, `dashboard_widgets`.`configuration` AS `configuration`, `dashboard_widgets`.`refreshInterval` AS `refreshInterval`, `dashboard_widgets`.`lastUpdated` AS `lastUpdated`, `dashboard_widgets`.`dataSource` AS `dataSource`, `dashboard_widgets`.`customTitle` AS `customTitle`, `dashboard_widgets`.`colorScheme` AS `colorScheme`, `dashboard_widgets`.`customColors` AS `customColors`, `dashboard_widgets`.`showHeader` AS `showHeader`, `dashboard_widgets`.`showFooter` AS `showFooter`, `dashboard_widgets`.`headerText` AS `headerText`, `dashboard_widgets`.`footerText` AS `footerText`, `dashboard_widgets`.`iconName` AS `iconName`, `dashboard_widgets`.`animationEnabled` AS `animationEnabled`, `dashboard_widgets`.`clickAction` AS `clickAction`, `dashboard_widgets`.`longPressAction` AS `longPressAction`, `dashboard_widgets`.`swipeActions` AS `swipeActions`, `dashboard_widgets`.`accessibilityLabel` AS `accessibilityLabel`, `dashboard_widgets`.`accessibilityHint` AS `accessibilityHint`, `dashboard_widgets`.`isCustomizable` AS `isCustomizable`, `dashboard_widgets`.`requiresPermission` AS `requiresPermission`, `dashboard_widgets`.`permissionType` AS `permissionType`, `dashboard_widgets`.`dataRetentionDays` AS `dataRetentionDays`, `dashboard_widgets`.`cacheEnabled` AS `cacheEnabled`, `dashboard_widgets`.`offlineSupport` AS `offlineSupport`, `dashboard_widgets`.`errorFallback` AS `errorFallback`, `dashboard_widgets`.`loadingIndicator` AS `loadingIndicator`, `dashboard_widgets`.`updateAnimation` AS `updateAnimation`, `dashboard_widgets`.`priority` AS `priority`, `dashboard_widgets`.`dependencies` AS `dependencies`, `dashboard_widgets`.`createdDate` AS `createdDate`, `dashboard_widgets`.`modifiedDate` AS `modifiedDate`, `dashboard_widgets`.`userNotes` AS `userNotes` FROM dashboard_widgets ORDER BY position ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dashboard_widgets"}, new Callable<List<DashboardWidget>>() {
      @Override
      @NonNull
      public List<DashboardWidget> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfWidgetType = 1;
          final int _cursorIndexOfDisplayName = 2;
          final int _cursorIndexOfPosition = 3;
          final int _cursorIndexOfIsVisible = 4;
          final int _cursorIndexOfIsEnabled = 5;
          final int _cursorIndexOfSize = 6;
          final int _cursorIndexOfConfiguration = 7;
          final int _cursorIndexOfRefreshInterval = 8;
          final int _cursorIndexOfLastUpdated = 9;
          final int _cursorIndexOfDataSource = 10;
          final int _cursorIndexOfCustomTitle = 11;
          final int _cursorIndexOfColorScheme = 12;
          final int _cursorIndexOfCustomColors = 13;
          final int _cursorIndexOfShowHeader = 14;
          final int _cursorIndexOfShowFooter = 15;
          final int _cursorIndexOfHeaderText = 16;
          final int _cursorIndexOfFooterText = 17;
          final int _cursorIndexOfIconName = 18;
          final int _cursorIndexOfAnimationEnabled = 19;
          final int _cursorIndexOfClickAction = 20;
          final int _cursorIndexOfLongPressAction = 21;
          final int _cursorIndexOfSwipeActions = 22;
          final int _cursorIndexOfAccessibilityLabel = 23;
          final int _cursorIndexOfAccessibilityHint = 24;
          final int _cursorIndexOfIsCustomizable = 25;
          final int _cursorIndexOfRequiresPermission = 26;
          final int _cursorIndexOfPermissionType = 27;
          final int _cursorIndexOfDataRetentionDays = 28;
          final int _cursorIndexOfCacheEnabled = 29;
          final int _cursorIndexOfOfflineSupport = 30;
          final int _cursorIndexOfErrorFallback = 31;
          final int _cursorIndexOfLoadingIndicator = 32;
          final int _cursorIndexOfUpdateAnimation = 33;
          final int _cursorIndexOfPriority = 34;
          final int _cursorIndexOfDependencies = 35;
          final int _cursorIndexOfCreatedDate = 36;
          final int _cursorIndexOfModifiedDate = 37;
          final int _cursorIndexOfUserNotes = 38;
          final List<DashboardWidget> _result = new ArrayList<DashboardWidget>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DashboardWidget _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWidgetType;
            if (_cursor.isNull(_cursorIndexOfWidgetType)) {
              _tmpWidgetType = null;
            } else {
              _tmpWidgetType = _cursor.getString(_cursorIndexOfWidgetType);
            }
            final String _tmpDisplayName;
            if (_cursor.isNull(_cursorIndexOfDisplayName)) {
              _tmpDisplayName = null;
            } else {
              _tmpDisplayName = _cursor.getString(_cursorIndexOfDisplayName);
            }
            final int _tmpPosition;
            _tmpPosition = _cursor.getInt(_cursorIndexOfPosition);
            final boolean _tmpIsVisible;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp != 0;
            final boolean _tmpIsEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsEnabled);
            _tmpIsEnabled = _tmp_1 != 0;
            final String _tmpSize;
            if (_cursor.isNull(_cursorIndexOfSize)) {
              _tmpSize = null;
            } else {
              _tmpSize = _cursor.getString(_cursorIndexOfSize);
            }
            final String _tmpConfiguration;
            if (_cursor.isNull(_cursorIndexOfConfiguration)) {
              _tmpConfiguration = null;
            } else {
              _tmpConfiguration = _cursor.getString(_cursorIndexOfConfiguration);
            }
            final int _tmpRefreshInterval;
            _tmpRefreshInterval = _cursor.getInt(_cursorIndexOfRefreshInterval);
            final LocalDateTime _tmpLastUpdated;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastUpdated)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastUpdated);
            }
            _tmpLastUpdated = __converters.toLocalDateTime(_tmp_2);
            final String _tmpDataSource;
            if (_cursor.isNull(_cursorIndexOfDataSource)) {
              _tmpDataSource = null;
            } else {
              _tmpDataSource = _cursor.getString(_cursorIndexOfDataSource);
            }
            final String _tmpCustomTitle;
            if (_cursor.isNull(_cursorIndexOfCustomTitle)) {
              _tmpCustomTitle = null;
            } else {
              _tmpCustomTitle = _cursor.getString(_cursorIndexOfCustomTitle);
            }
            final String _tmpColorScheme;
            if (_cursor.isNull(_cursorIndexOfColorScheme)) {
              _tmpColorScheme = null;
            } else {
              _tmpColorScheme = _cursor.getString(_cursorIndexOfColorScheme);
            }
            final String _tmpCustomColors;
            if (_cursor.isNull(_cursorIndexOfCustomColors)) {
              _tmpCustomColors = null;
            } else {
              _tmpCustomColors = _cursor.getString(_cursorIndexOfCustomColors);
            }
            final boolean _tmpShowHeader;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfShowHeader);
            _tmpShowHeader = _tmp_3 != 0;
            final boolean _tmpShowFooter;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfShowFooter);
            _tmpShowFooter = _tmp_4 != 0;
            final String _tmpHeaderText;
            if (_cursor.isNull(_cursorIndexOfHeaderText)) {
              _tmpHeaderText = null;
            } else {
              _tmpHeaderText = _cursor.getString(_cursorIndexOfHeaderText);
            }
            final String _tmpFooterText;
            if (_cursor.isNull(_cursorIndexOfFooterText)) {
              _tmpFooterText = null;
            } else {
              _tmpFooterText = _cursor.getString(_cursorIndexOfFooterText);
            }
            final String _tmpIconName;
            if (_cursor.isNull(_cursorIndexOfIconName)) {
              _tmpIconName = null;
            } else {
              _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            }
            final boolean _tmpAnimationEnabled;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfAnimationEnabled);
            _tmpAnimationEnabled = _tmp_5 != 0;
            final String _tmpClickAction;
            if (_cursor.isNull(_cursorIndexOfClickAction)) {
              _tmpClickAction = null;
            } else {
              _tmpClickAction = _cursor.getString(_cursorIndexOfClickAction);
            }
            final String _tmpLongPressAction;
            if (_cursor.isNull(_cursorIndexOfLongPressAction)) {
              _tmpLongPressAction = null;
            } else {
              _tmpLongPressAction = _cursor.getString(_cursorIndexOfLongPressAction);
            }
            final String _tmpSwipeActions;
            if (_cursor.isNull(_cursorIndexOfSwipeActions)) {
              _tmpSwipeActions = null;
            } else {
              _tmpSwipeActions = _cursor.getString(_cursorIndexOfSwipeActions);
            }
            final String _tmpAccessibilityLabel;
            if (_cursor.isNull(_cursorIndexOfAccessibilityLabel)) {
              _tmpAccessibilityLabel = null;
            } else {
              _tmpAccessibilityLabel = _cursor.getString(_cursorIndexOfAccessibilityLabel);
            }
            final String _tmpAccessibilityHint;
            if (_cursor.isNull(_cursorIndexOfAccessibilityHint)) {
              _tmpAccessibilityHint = null;
            } else {
              _tmpAccessibilityHint = _cursor.getString(_cursorIndexOfAccessibilityHint);
            }
            final boolean _tmpIsCustomizable;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustomizable);
            _tmpIsCustomizable = _tmp_6 != 0;
            final boolean _tmpRequiresPermission;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfRequiresPermission);
            _tmpRequiresPermission = _tmp_7 != 0;
            final String _tmpPermissionType;
            if (_cursor.isNull(_cursorIndexOfPermissionType)) {
              _tmpPermissionType = null;
            } else {
              _tmpPermissionType = _cursor.getString(_cursorIndexOfPermissionType);
            }
            final int _tmpDataRetentionDays;
            _tmpDataRetentionDays = _cursor.getInt(_cursorIndexOfDataRetentionDays);
            final boolean _tmpCacheEnabled;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfCacheEnabled);
            _tmpCacheEnabled = _tmp_8 != 0;
            final boolean _tmpOfflineSupport;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfOfflineSupport);
            _tmpOfflineSupport = _tmp_9 != 0;
            final String _tmpErrorFallback;
            if (_cursor.isNull(_cursorIndexOfErrorFallback)) {
              _tmpErrorFallback = null;
            } else {
              _tmpErrorFallback = _cursor.getString(_cursorIndexOfErrorFallback);
            }
            final String _tmpLoadingIndicator;
            if (_cursor.isNull(_cursorIndexOfLoadingIndicator)) {
              _tmpLoadingIndicator = null;
            } else {
              _tmpLoadingIndicator = _cursor.getString(_cursorIndexOfLoadingIndicator);
            }
            final String _tmpUpdateAnimation;
            if (_cursor.isNull(_cursorIndexOfUpdateAnimation)) {
              _tmpUpdateAnimation = null;
            } else {
              _tmpUpdateAnimation = _cursor.getString(_cursorIndexOfUpdateAnimation);
            }
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpDependencies;
            if (_cursor.isNull(_cursorIndexOfDependencies)) {
              _tmpDependencies = null;
            } else {
              _tmpDependencies = _cursor.getString(_cursorIndexOfDependencies);
            }
            final LocalDateTime _tmpCreatedDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpModifiedDate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfModifiedDate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfModifiedDate);
            }
            _tmpModifiedDate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpUserNotes;
            if (_cursor.isNull(_cursorIndexOfUserNotes)) {
              _tmpUserNotes = null;
            } else {
              _tmpUserNotes = _cursor.getString(_cursorIndexOfUserNotes);
            }
            _item = new DashboardWidget(_tmpId,_tmpWidgetType,_tmpDisplayName,_tmpPosition,_tmpIsVisible,_tmpIsEnabled,_tmpSize,_tmpConfiguration,_tmpRefreshInterval,_tmpLastUpdated,_tmpDataSource,_tmpCustomTitle,_tmpColorScheme,_tmpCustomColors,_tmpShowHeader,_tmpShowFooter,_tmpHeaderText,_tmpFooterText,_tmpIconName,_tmpAnimationEnabled,_tmpClickAction,_tmpLongPressAction,_tmpSwipeActions,_tmpAccessibilityLabel,_tmpAccessibilityHint,_tmpIsCustomizable,_tmpRequiresPermission,_tmpPermissionType,_tmpDataRetentionDays,_tmpCacheEnabled,_tmpOfflineSupport,_tmpErrorFallback,_tmpLoadingIndicator,_tmpUpdateAnimation,_tmpPriority,_tmpDependencies,_tmpCreatedDate,_tmpModifiedDate,_tmpUserNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getWidgetByType(final String widgetType,
      final Continuation<? super DashboardWidget> $completion) {
    final String _sql = "SELECT * FROM dashboard_widgets WHERE widgetType = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (widgetType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, widgetType);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<DashboardWidget>() {
      @Override
      @Nullable
      public DashboardWidget call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWidgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "widgetType");
          final int _cursorIndexOfDisplayName = CursorUtil.getColumnIndexOrThrow(_cursor, "displayName");
          final int _cursorIndexOfPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "position");
          final int _cursorIndexOfIsVisible = CursorUtil.getColumnIndexOrThrow(_cursor, "isVisible");
          final int _cursorIndexOfIsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isEnabled");
          final int _cursorIndexOfSize = CursorUtil.getColumnIndexOrThrow(_cursor, "size");
          final int _cursorIndexOfConfiguration = CursorUtil.getColumnIndexOrThrow(_cursor, "configuration");
          final int _cursorIndexOfRefreshInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "refreshInterval");
          final int _cursorIndexOfLastUpdated = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdated");
          final int _cursorIndexOfDataSource = CursorUtil.getColumnIndexOrThrow(_cursor, "dataSource");
          final int _cursorIndexOfCustomTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "customTitle");
          final int _cursorIndexOfColorScheme = CursorUtil.getColumnIndexOrThrow(_cursor, "colorScheme");
          final int _cursorIndexOfCustomColors = CursorUtil.getColumnIndexOrThrow(_cursor, "customColors");
          final int _cursorIndexOfShowHeader = CursorUtil.getColumnIndexOrThrow(_cursor, "showHeader");
          final int _cursorIndexOfShowFooter = CursorUtil.getColumnIndexOrThrow(_cursor, "showFooter");
          final int _cursorIndexOfHeaderText = CursorUtil.getColumnIndexOrThrow(_cursor, "headerText");
          final int _cursorIndexOfFooterText = CursorUtil.getColumnIndexOrThrow(_cursor, "footerText");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfAnimationEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "animationEnabled");
          final int _cursorIndexOfClickAction = CursorUtil.getColumnIndexOrThrow(_cursor, "clickAction");
          final int _cursorIndexOfLongPressAction = CursorUtil.getColumnIndexOrThrow(_cursor, "longPressAction");
          final int _cursorIndexOfSwipeActions = CursorUtil.getColumnIndexOrThrow(_cursor, "swipeActions");
          final int _cursorIndexOfAccessibilityLabel = CursorUtil.getColumnIndexOrThrow(_cursor, "accessibilityLabel");
          final int _cursorIndexOfAccessibilityHint = CursorUtil.getColumnIndexOrThrow(_cursor, "accessibilityHint");
          final int _cursorIndexOfIsCustomizable = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustomizable");
          final int _cursorIndexOfRequiresPermission = CursorUtil.getColumnIndexOrThrow(_cursor, "requiresPermission");
          final int _cursorIndexOfPermissionType = CursorUtil.getColumnIndexOrThrow(_cursor, "permissionType");
          final int _cursorIndexOfDataRetentionDays = CursorUtil.getColumnIndexOrThrow(_cursor, "dataRetentionDays");
          final int _cursorIndexOfCacheEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheEnabled");
          final int _cursorIndexOfOfflineSupport = CursorUtil.getColumnIndexOrThrow(_cursor, "offlineSupport");
          final int _cursorIndexOfErrorFallback = CursorUtil.getColumnIndexOrThrow(_cursor, "errorFallback");
          final int _cursorIndexOfLoadingIndicator = CursorUtil.getColumnIndexOrThrow(_cursor, "loadingIndicator");
          final int _cursorIndexOfUpdateAnimation = CursorUtil.getColumnIndexOrThrow(_cursor, "updateAnimation");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfDependencies = CursorUtil.getColumnIndexOrThrow(_cursor, "dependencies");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfModifiedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedDate");
          final int _cursorIndexOfUserNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "userNotes");
          final DashboardWidget _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWidgetType;
            if (_cursor.isNull(_cursorIndexOfWidgetType)) {
              _tmpWidgetType = null;
            } else {
              _tmpWidgetType = _cursor.getString(_cursorIndexOfWidgetType);
            }
            final String _tmpDisplayName;
            if (_cursor.isNull(_cursorIndexOfDisplayName)) {
              _tmpDisplayName = null;
            } else {
              _tmpDisplayName = _cursor.getString(_cursorIndexOfDisplayName);
            }
            final int _tmpPosition;
            _tmpPosition = _cursor.getInt(_cursorIndexOfPosition);
            final boolean _tmpIsVisible;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp != 0;
            final boolean _tmpIsEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsEnabled);
            _tmpIsEnabled = _tmp_1 != 0;
            final String _tmpSize;
            if (_cursor.isNull(_cursorIndexOfSize)) {
              _tmpSize = null;
            } else {
              _tmpSize = _cursor.getString(_cursorIndexOfSize);
            }
            final String _tmpConfiguration;
            if (_cursor.isNull(_cursorIndexOfConfiguration)) {
              _tmpConfiguration = null;
            } else {
              _tmpConfiguration = _cursor.getString(_cursorIndexOfConfiguration);
            }
            final int _tmpRefreshInterval;
            _tmpRefreshInterval = _cursor.getInt(_cursorIndexOfRefreshInterval);
            final LocalDateTime _tmpLastUpdated;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastUpdated)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastUpdated);
            }
            _tmpLastUpdated = __converters.toLocalDateTime(_tmp_2);
            final String _tmpDataSource;
            if (_cursor.isNull(_cursorIndexOfDataSource)) {
              _tmpDataSource = null;
            } else {
              _tmpDataSource = _cursor.getString(_cursorIndexOfDataSource);
            }
            final String _tmpCustomTitle;
            if (_cursor.isNull(_cursorIndexOfCustomTitle)) {
              _tmpCustomTitle = null;
            } else {
              _tmpCustomTitle = _cursor.getString(_cursorIndexOfCustomTitle);
            }
            final String _tmpColorScheme;
            if (_cursor.isNull(_cursorIndexOfColorScheme)) {
              _tmpColorScheme = null;
            } else {
              _tmpColorScheme = _cursor.getString(_cursorIndexOfColorScheme);
            }
            final String _tmpCustomColors;
            if (_cursor.isNull(_cursorIndexOfCustomColors)) {
              _tmpCustomColors = null;
            } else {
              _tmpCustomColors = _cursor.getString(_cursorIndexOfCustomColors);
            }
            final boolean _tmpShowHeader;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfShowHeader);
            _tmpShowHeader = _tmp_3 != 0;
            final boolean _tmpShowFooter;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfShowFooter);
            _tmpShowFooter = _tmp_4 != 0;
            final String _tmpHeaderText;
            if (_cursor.isNull(_cursorIndexOfHeaderText)) {
              _tmpHeaderText = null;
            } else {
              _tmpHeaderText = _cursor.getString(_cursorIndexOfHeaderText);
            }
            final String _tmpFooterText;
            if (_cursor.isNull(_cursorIndexOfFooterText)) {
              _tmpFooterText = null;
            } else {
              _tmpFooterText = _cursor.getString(_cursorIndexOfFooterText);
            }
            final String _tmpIconName;
            if (_cursor.isNull(_cursorIndexOfIconName)) {
              _tmpIconName = null;
            } else {
              _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            }
            final boolean _tmpAnimationEnabled;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfAnimationEnabled);
            _tmpAnimationEnabled = _tmp_5 != 0;
            final String _tmpClickAction;
            if (_cursor.isNull(_cursorIndexOfClickAction)) {
              _tmpClickAction = null;
            } else {
              _tmpClickAction = _cursor.getString(_cursorIndexOfClickAction);
            }
            final String _tmpLongPressAction;
            if (_cursor.isNull(_cursorIndexOfLongPressAction)) {
              _tmpLongPressAction = null;
            } else {
              _tmpLongPressAction = _cursor.getString(_cursorIndexOfLongPressAction);
            }
            final String _tmpSwipeActions;
            if (_cursor.isNull(_cursorIndexOfSwipeActions)) {
              _tmpSwipeActions = null;
            } else {
              _tmpSwipeActions = _cursor.getString(_cursorIndexOfSwipeActions);
            }
            final String _tmpAccessibilityLabel;
            if (_cursor.isNull(_cursorIndexOfAccessibilityLabel)) {
              _tmpAccessibilityLabel = null;
            } else {
              _tmpAccessibilityLabel = _cursor.getString(_cursorIndexOfAccessibilityLabel);
            }
            final String _tmpAccessibilityHint;
            if (_cursor.isNull(_cursorIndexOfAccessibilityHint)) {
              _tmpAccessibilityHint = null;
            } else {
              _tmpAccessibilityHint = _cursor.getString(_cursorIndexOfAccessibilityHint);
            }
            final boolean _tmpIsCustomizable;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustomizable);
            _tmpIsCustomizable = _tmp_6 != 0;
            final boolean _tmpRequiresPermission;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfRequiresPermission);
            _tmpRequiresPermission = _tmp_7 != 0;
            final String _tmpPermissionType;
            if (_cursor.isNull(_cursorIndexOfPermissionType)) {
              _tmpPermissionType = null;
            } else {
              _tmpPermissionType = _cursor.getString(_cursorIndexOfPermissionType);
            }
            final int _tmpDataRetentionDays;
            _tmpDataRetentionDays = _cursor.getInt(_cursorIndexOfDataRetentionDays);
            final boolean _tmpCacheEnabled;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfCacheEnabled);
            _tmpCacheEnabled = _tmp_8 != 0;
            final boolean _tmpOfflineSupport;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfOfflineSupport);
            _tmpOfflineSupport = _tmp_9 != 0;
            final String _tmpErrorFallback;
            if (_cursor.isNull(_cursorIndexOfErrorFallback)) {
              _tmpErrorFallback = null;
            } else {
              _tmpErrorFallback = _cursor.getString(_cursorIndexOfErrorFallback);
            }
            final String _tmpLoadingIndicator;
            if (_cursor.isNull(_cursorIndexOfLoadingIndicator)) {
              _tmpLoadingIndicator = null;
            } else {
              _tmpLoadingIndicator = _cursor.getString(_cursorIndexOfLoadingIndicator);
            }
            final String _tmpUpdateAnimation;
            if (_cursor.isNull(_cursorIndexOfUpdateAnimation)) {
              _tmpUpdateAnimation = null;
            } else {
              _tmpUpdateAnimation = _cursor.getString(_cursorIndexOfUpdateAnimation);
            }
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpDependencies;
            if (_cursor.isNull(_cursorIndexOfDependencies)) {
              _tmpDependencies = null;
            } else {
              _tmpDependencies = _cursor.getString(_cursorIndexOfDependencies);
            }
            final LocalDateTime _tmpCreatedDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpModifiedDate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfModifiedDate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfModifiedDate);
            }
            _tmpModifiedDate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpUserNotes;
            if (_cursor.isNull(_cursorIndexOfUserNotes)) {
              _tmpUserNotes = null;
            } else {
              _tmpUserNotes = _cursor.getString(_cursorIndexOfUserNotes);
            }
            _result = new DashboardWidget(_tmpId,_tmpWidgetType,_tmpDisplayName,_tmpPosition,_tmpIsVisible,_tmpIsEnabled,_tmpSize,_tmpConfiguration,_tmpRefreshInterval,_tmpLastUpdated,_tmpDataSource,_tmpCustomTitle,_tmpColorScheme,_tmpCustomColors,_tmpShowHeader,_tmpShowFooter,_tmpHeaderText,_tmpFooterText,_tmpIconName,_tmpAnimationEnabled,_tmpClickAction,_tmpLongPressAction,_tmpSwipeActions,_tmpAccessibilityLabel,_tmpAccessibilityHint,_tmpIsCustomizable,_tmpRequiresPermission,_tmpPermissionType,_tmpDataRetentionDays,_tmpCacheEnabled,_tmpOfflineSupport,_tmpErrorFallback,_tmpLoadingIndicator,_tmpUpdateAnimation,_tmpPriority,_tmpDependencies,_tmpCreatedDate,_tmpModifiedDate,_tmpUserNotes);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getWidgetById(final long id,
      final Continuation<? super DashboardWidget> $completion) {
    final String _sql = "SELECT * FROM dashboard_widgets WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<DashboardWidget>() {
      @Override
      @Nullable
      public DashboardWidget call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfWidgetType = CursorUtil.getColumnIndexOrThrow(_cursor, "widgetType");
          final int _cursorIndexOfDisplayName = CursorUtil.getColumnIndexOrThrow(_cursor, "displayName");
          final int _cursorIndexOfPosition = CursorUtil.getColumnIndexOrThrow(_cursor, "position");
          final int _cursorIndexOfIsVisible = CursorUtil.getColumnIndexOrThrow(_cursor, "isVisible");
          final int _cursorIndexOfIsEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "isEnabled");
          final int _cursorIndexOfSize = CursorUtil.getColumnIndexOrThrow(_cursor, "size");
          final int _cursorIndexOfConfiguration = CursorUtil.getColumnIndexOrThrow(_cursor, "configuration");
          final int _cursorIndexOfRefreshInterval = CursorUtil.getColumnIndexOrThrow(_cursor, "refreshInterval");
          final int _cursorIndexOfLastUpdated = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUpdated");
          final int _cursorIndexOfDataSource = CursorUtil.getColumnIndexOrThrow(_cursor, "dataSource");
          final int _cursorIndexOfCustomTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "customTitle");
          final int _cursorIndexOfColorScheme = CursorUtil.getColumnIndexOrThrow(_cursor, "colorScheme");
          final int _cursorIndexOfCustomColors = CursorUtil.getColumnIndexOrThrow(_cursor, "customColors");
          final int _cursorIndexOfShowHeader = CursorUtil.getColumnIndexOrThrow(_cursor, "showHeader");
          final int _cursorIndexOfShowFooter = CursorUtil.getColumnIndexOrThrow(_cursor, "showFooter");
          final int _cursorIndexOfHeaderText = CursorUtil.getColumnIndexOrThrow(_cursor, "headerText");
          final int _cursorIndexOfFooterText = CursorUtil.getColumnIndexOrThrow(_cursor, "footerText");
          final int _cursorIndexOfIconName = CursorUtil.getColumnIndexOrThrow(_cursor, "iconName");
          final int _cursorIndexOfAnimationEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "animationEnabled");
          final int _cursorIndexOfClickAction = CursorUtil.getColumnIndexOrThrow(_cursor, "clickAction");
          final int _cursorIndexOfLongPressAction = CursorUtil.getColumnIndexOrThrow(_cursor, "longPressAction");
          final int _cursorIndexOfSwipeActions = CursorUtil.getColumnIndexOrThrow(_cursor, "swipeActions");
          final int _cursorIndexOfAccessibilityLabel = CursorUtil.getColumnIndexOrThrow(_cursor, "accessibilityLabel");
          final int _cursorIndexOfAccessibilityHint = CursorUtil.getColumnIndexOrThrow(_cursor, "accessibilityHint");
          final int _cursorIndexOfIsCustomizable = CursorUtil.getColumnIndexOrThrow(_cursor, "isCustomizable");
          final int _cursorIndexOfRequiresPermission = CursorUtil.getColumnIndexOrThrow(_cursor, "requiresPermission");
          final int _cursorIndexOfPermissionType = CursorUtil.getColumnIndexOrThrow(_cursor, "permissionType");
          final int _cursorIndexOfDataRetentionDays = CursorUtil.getColumnIndexOrThrow(_cursor, "dataRetentionDays");
          final int _cursorIndexOfCacheEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "cacheEnabled");
          final int _cursorIndexOfOfflineSupport = CursorUtil.getColumnIndexOrThrow(_cursor, "offlineSupport");
          final int _cursorIndexOfErrorFallback = CursorUtil.getColumnIndexOrThrow(_cursor, "errorFallback");
          final int _cursorIndexOfLoadingIndicator = CursorUtil.getColumnIndexOrThrow(_cursor, "loadingIndicator");
          final int _cursorIndexOfUpdateAnimation = CursorUtil.getColumnIndexOrThrow(_cursor, "updateAnimation");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfDependencies = CursorUtil.getColumnIndexOrThrow(_cursor, "dependencies");
          final int _cursorIndexOfCreatedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "createdDate");
          final int _cursorIndexOfModifiedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "modifiedDate");
          final int _cursorIndexOfUserNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "userNotes");
          final DashboardWidget _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpWidgetType;
            if (_cursor.isNull(_cursorIndexOfWidgetType)) {
              _tmpWidgetType = null;
            } else {
              _tmpWidgetType = _cursor.getString(_cursorIndexOfWidgetType);
            }
            final String _tmpDisplayName;
            if (_cursor.isNull(_cursorIndexOfDisplayName)) {
              _tmpDisplayName = null;
            } else {
              _tmpDisplayName = _cursor.getString(_cursorIndexOfDisplayName);
            }
            final int _tmpPosition;
            _tmpPosition = _cursor.getInt(_cursorIndexOfPosition);
            final boolean _tmpIsVisible;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVisible);
            _tmpIsVisible = _tmp != 0;
            final boolean _tmpIsEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsEnabled);
            _tmpIsEnabled = _tmp_1 != 0;
            final String _tmpSize;
            if (_cursor.isNull(_cursorIndexOfSize)) {
              _tmpSize = null;
            } else {
              _tmpSize = _cursor.getString(_cursorIndexOfSize);
            }
            final String _tmpConfiguration;
            if (_cursor.isNull(_cursorIndexOfConfiguration)) {
              _tmpConfiguration = null;
            } else {
              _tmpConfiguration = _cursor.getString(_cursorIndexOfConfiguration);
            }
            final int _tmpRefreshInterval;
            _tmpRefreshInterval = _cursor.getInt(_cursorIndexOfRefreshInterval);
            final LocalDateTime _tmpLastUpdated;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastUpdated)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastUpdated);
            }
            _tmpLastUpdated = __converters.toLocalDateTime(_tmp_2);
            final String _tmpDataSource;
            if (_cursor.isNull(_cursorIndexOfDataSource)) {
              _tmpDataSource = null;
            } else {
              _tmpDataSource = _cursor.getString(_cursorIndexOfDataSource);
            }
            final String _tmpCustomTitle;
            if (_cursor.isNull(_cursorIndexOfCustomTitle)) {
              _tmpCustomTitle = null;
            } else {
              _tmpCustomTitle = _cursor.getString(_cursorIndexOfCustomTitle);
            }
            final String _tmpColorScheme;
            if (_cursor.isNull(_cursorIndexOfColorScheme)) {
              _tmpColorScheme = null;
            } else {
              _tmpColorScheme = _cursor.getString(_cursorIndexOfColorScheme);
            }
            final String _tmpCustomColors;
            if (_cursor.isNull(_cursorIndexOfCustomColors)) {
              _tmpCustomColors = null;
            } else {
              _tmpCustomColors = _cursor.getString(_cursorIndexOfCustomColors);
            }
            final boolean _tmpShowHeader;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfShowHeader);
            _tmpShowHeader = _tmp_3 != 0;
            final boolean _tmpShowFooter;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfShowFooter);
            _tmpShowFooter = _tmp_4 != 0;
            final String _tmpHeaderText;
            if (_cursor.isNull(_cursorIndexOfHeaderText)) {
              _tmpHeaderText = null;
            } else {
              _tmpHeaderText = _cursor.getString(_cursorIndexOfHeaderText);
            }
            final String _tmpFooterText;
            if (_cursor.isNull(_cursorIndexOfFooterText)) {
              _tmpFooterText = null;
            } else {
              _tmpFooterText = _cursor.getString(_cursorIndexOfFooterText);
            }
            final String _tmpIconName;
            if (_cursor.isNull(_cursorIndexOfIconName)) {
              _tmpIconName = null;
            } else {
              _tmpIconName = _cursor.getString(_cursorIndexOfIconName);
            }
            final boolean _tmpAnimationEnabled;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfAnimationEnabled);
            _tmpAnimationEnabled = _tmp_5 != 0;
            final String _tmpClickAction;
            if (_cursor.isNull(_cursorIndexOfClickAction)) {
              _tmpClickAction = null;
            } else {
              _tmpClickAction = _cursor.getString(_cursorIndexOfClickAction);
            }
            final String _tmpLongPressAction;
            if (_cursor.isNull(_cursorIndexOfLongPressAction)) {
              _tmpLongPressAction = null;
            } else {
              _tmpLongPressAction = _cursor.getString(_cursorIndexOfLongPressAction);
            }
            final String _tmpSwipeActions;
            if (_cursor.isNull(_cursorIndexOfSwipeActions)) {
              _tmpSwipeActions = null;
            } else {
              _tmpSwipeActions = _cursor.getString(_cursorIndexOfSwipeActions);
            }
            final String _tmpAccessibilityLabel;
            if (_cursor.isNull(_cursorIndexOfAccessibilityLabel)) {
              _tmpAccessibilityLabel = null;
            } else {
              _tmpAccessibilityLabel = _cursor.getString(_cursorIndexOfAccessibilityLabel);
            }
            final String _tmpAccessibilityHint;
            if (_cursor.isNull(_cursorIndexOfAccessibilityHint)) {
              _tmpAccessibilityHint = null;
            } else {
              _tmpAccessibilityHint = _cursor.getString(_cursorIndexOfAccessibilityHint);
            }
            final boolean _tmpIsCustomizable;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfIsCustomizable);
            _tmpIsCustomizable = _tmp_6 != 0;
            final boolean _tmpRequiresPermission;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfRequiresPermission);
            _tmpRequiresPermission = _tmp_7 != 0;
            final String _tmpPermissionType;
            if (_cursor.isNull(_cursorIndexOfPermissionType)) {
              _tmpPermissionType = null;
            } else {
              _tmpPermissionType = _cursor.getString(_cursorIndexOfPermissionType);
            }
            final int _tmpDataRetentionDays;
            _tmpDataRetentionDays = _cursor.getInt(_cursorIndexOfDataRetentionDays);
            final boolean _tmpCacheEnabled;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfCacheEnabled);
            _tmpCacheEnabled = _tmp_8 != 0;
            final boolean _tmpOfflineSupport;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfOfflineSupport);
            _tmpOfflineSupport = _tmp_9 != 0;
            final String _tmpErrorFallback;
            if (_cursor.isNull(_cursorIndexOfErrorFallback)) {
              _tmpErrorFallback = null;
            } else {
              _tmpErrorFallback = _cursor.getString(_cursorIndexOfErrorFallback);
            }
            final String _tmpLoadingIndicator;
            if (_cursor.isNull(_cursorIndexOfLoadingIndicator)) {
              _tmpLoadingIndicator = null;
            } else {
              _tmpLoadingIndicator = _cursor.getString(_cursorIndexOfLoadingIndicator);
            }
            final String _tmpUpdateAnimation;
            if (_cursor.isNull(_cursorIndexOfUpdateAnimation)) {
              _tmpUpdateAnimation = null;
            } else {
              _tmpUpdateAnimation = _cursor.getString(_cursorIndexOfUpdateAnimation);
            }
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final String _tmpDependencies;
            if (_cursor.isNull(_cursorIndexOfDependencies)) {
              _tmpDependencies = null;
            } else {
              _tmpDependencies = _cursor.getString(_cursorIndexOfDependencies);
            }
            final LocalDateTime _tmpCreatedDate;
            final String _tmp_10;
            if (_cursor.isNull(_cursorIndexOfCreatedDate)) {
              _tmp_10 = null;
            } else {
              _tmp_10 = _cursor.getString(_cursorIndexOfCreatedDate);
            }
            _tmpCreatedDate = __converters.toLocalDateTime(_tmp_10);
            final LocalDateTime _tmpModifiedDate;
            final String _tmp_11;
            if (_cursor.isNull(_cursorIndexOfModifiedDate)) {
              _tmp_11 = null;
            } else {
              _tmp_11 = _cursor.getString(_cursorIndexOfModifiedDate);
            }
            _tmpModifiedDate = __converters.toLocalDateTime(_tmp_11);
            final String _tmpUserNotes;
            if (_cursor.isNull(_cursorIndexOfUserNotes)) {
              _tmpUserNotes = null;
            } else {
              _tmpUserNotes = _cursor.getString(_cursorIndexOfUserNotes);
            }
            _result = new DashboardWidget(_tmpId,_tmpWidgetType,_tmpDisplayName,_tmpPosition,_tmpIsVisible,_tmpIsEnabled,_tmpSize,_tmpConfiguration,_tmpRefreshInterval,_tmpLastUpdated,_tmpDataSource,_tmpCustomTitle,_tmpColorScheme,_tmpCustomColors,_tmpShowHeader,_tmpShowFooter,_tmpHeaderText,_tmpFooterText,_tmpIconName,_tmpAnimationEnabled,_tmpClickAction,_tmpLongPressAction,_tmpSwipeActions,_tmpAccessibilityLabel,_tmpAccessibilityHint,_tmpIsCustomizable,_tmpRequiresPermission,_tmpPermissionType,_tmpDataRetentionDays,_tmpCacheEnabled,_tmpOfflineSupport,_tmpErrorFallback,_tmpLoadingIndicator,_tmpUpdateAnimation,_tmpPriority,_tmpDependencies,_tmpCreatedDate,_tmpModifiedDate,_tmpUserNotes);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getVisibleWidgetCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM dashboard_widgets WHERE isVisible = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
