C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\receiver\AlarmReceiver.kt:78: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
        notificationManager.notify(notificationId, notification)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\NotificationManager.kt:281: Error: Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with checkPermission) or explicitly handle a potential SecurityException [MissingPermission]
        notificationManager.notify(notificationId, notification)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingPermission":
   This check scans through your code and libraries and looks at the APIs
   being used, and checks this against the set of permissions required to
   access those APIs. If the code using those APIs is called at runtime, then
   the program will crash.

   Furthermore, for permissions that are revocable (with targetSdkVersion 23),
   client code must also be prepared to handle the calls throwing an exception
   if the user rejects the request for permission at runtime.

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ADHDDesignValidator.kt:134: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                issues.add("Insufficient contrast ratio: ${String.format("%.2f", contrastRatio)} < ${Standards.MIN_CONTRAST_RATIO}")
                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\viewmodel\AICoachViewModel.kt:155: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Weekly budget: $${String.format("%.2f", weeklyBudget)}
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:119: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Total Spent: $${String.format("%.2f", totalSpent)}
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:121: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Top Category: ${topCategory?.key ?: "None"} ($${String.format("%.2f", topCategory?.value ?: 0.0)})
                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:127: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "✅ Great job staying within budget! You have $${String.format("%.2f", (budget?.weeklyAmount ?: 300.0) - totalSpent)} left this week."
                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:175: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Weekly Budget: $${String.format("%.2f", currentBudget.weeklyAmount)}
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:176: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Average Spending: $${String.format("%.2f", avgSpending)}
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:238: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Total Debt: $${String.format("%.2f", totalDebt)}
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:239: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Monthly Minimums: $${String.format("%.2f", totalMinPayments)}
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:240: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            • Highest Interest Rate: ${String.format("%.1f", highestRate)}%
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:245: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            Start with: ${smallestDebt?.name} ($${String.format("%.2f", smallestDebt?.balance ?: 0.0)})
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt:251: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            Start with: ${highestRateCard?.name} (${String.format("%.1f", highestRateCard?.interestRate ?: 0.0)}% APR)
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AdvancedAnalyticsService.kt:390: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        description = "You spend significantly more on weekends (${String.format("%.2f", weekendAvg)}) compared to weekdays (${String.format("%.2f", weekdayAvg)})",
                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AdvancedAnalyticsService.kt:390: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        description = "You spend significantly more on weekends (${String.format("%.2f", weekendAvg)}) compared to weekdays (${String.format("%.2f", weekdayAvg)})",
                                                                                                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\BudgetRecommendationService.kt:191: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "spending_increase" to "Your spending in this category has increased significantly. Consider increasing your budget by ${String.format("%.0f", abs(difference))}."
                                                                                                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\BudgetRecommendationService.kt:194: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "spending_decrease" to "You've been spending less in this category. You could reduce your budget by ${String.format("%.0f", abs(difference))}."
                                                                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DashboardScreen.kt:163: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "$${String.format("%.2f", safeToSpend)}",
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DashboardScreen.kt:213: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = if (totalDebt > 0) "$${String.format("%.2f", totalDebt)}" else "No debt",
                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DashboardScreen.kt:225: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = if (nextPayment > 0) "$${String.format("%.2f", nextPayment)}" else "None due",
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:287: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = "$${String.format("%.2f", amount)}",
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:362: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.2f", creditCard.currentBalance)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:375: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.2f", creditCard.creditLimit)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:397: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "${String.format("%.1f", utilization * 100)}%",
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:431: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "Min Payment: $${String.format("%.2f", creditCard.minimumPayment)}",
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:787: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Current Balance: $${String.format("%.2f", creditCard.currentBalance)}",
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:809: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        onClick = { paymentAmount = String.format("%.2f", creditCard.minimumPayment) }
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt:814: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        onClick = { paymentAmount = String.format("%.2f", creditCard.currentBalance) }
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt:142: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.0f", recommendation.currentAmount)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt:162: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.0f", recommendation.recommendedAmount)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt:298: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "${if (isOverBudget) "+" else ""}${String.format("%.0f", variance)}",
                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt:304: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "${String.format("%.1f", abs(variancePercentage))}%",
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt:244: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "You have $${String.format("%.0f", unallocatedAmount)} unallocated",
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt:512: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Allocate your $${String.format("%.0f", unallocatedAmount)} across these categories:",
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt:547: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Remaining: $${String.format("%.2f", remaining)}",
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:123: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "$${String.format("%.0f", remaining)}",
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:142: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.0f", category.spentAmount)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:147: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.0f", category.allocatedAmount)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:169: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Over by $${String.format("%.0f", -remaining)}",
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:257: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    unallocatedAmount > 0 -> "💰 You have $${String.format("%.0f", unallocatedAmount)} to allocate"
                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:258: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    else -> "⚠️ You've over-allocated by $${String.format("%.0f", abs(unallocatedAmount))}"
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:296: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            text = "$${String.format("%.0f", amount)}",
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:335: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    "$${String.format("%.0f", currentIncome)} ${period.replaceFirstChar { it.lowercase() }}"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:474: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Available: $${String.format("%.2f", fromCategory.allocatedAmount - fromCategory.spentAmount)}",
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:667: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.2f", remaining)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:681: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.2f", category.allocatedAmount)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:698: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.2f", category.spentAmount)} spent",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:703: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "${String.format("%.1f", spentPercentage * 100)}%",
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:737: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "Over budget by $${String.format("%.2f", -remaining)}",
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt:348: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        val formattedAmount = String.format("%.2f", amount)
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt:359: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return "$category spending: $${String.format("%.2f", amount)} of $${String.format("%.2f", budget)} budget, $percentage percent used"
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt:359: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return "$category spending: $${String.format("%.2f", amount)} of $${String.format("%.2f", budget)} budget, $percentage percent used"
                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt:364: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return "$cardName: $${String.format("%.2f", balance)} balance, $utilization percent utilization"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\FocusTimerComponents.kt:92: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = String.format("%02d:%02d", minutes, seconds),
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\GamificationService.kt:334: Warning: Implicitly using the default locale is a common source of bugs: Use capitalize(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            title = achievementId.replace("_", " ").capitalize(),
                                                    ~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt:71: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "You're about to spend $${String.format("%.2f", amount)} on $category",
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt:284: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        "This purchase will put you $${String.format("%.2f", expenseAmount - remainingBudget)} over budget."
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt:286: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        "You only have $${String.format("%.2f", remainingBudget)} left in your budget."
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlSettingsCard.kt:93: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    subtitle = "Trigger impulse control for purchases over $${String.format("%.0f", uiState.spendingThreshold)}",
                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\data\repository\NotificationRepository.kt:115: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "You've successfully tracked $${String.format("%.2f", amount)} in expenses this $period!"
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt:63: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String.format("$%.2f", expense.amount)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt:179: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = String.format("$%.0f left", remaining),
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt:197: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = String.format("$%.0f of $%.0f spent", category.spentAmount, category.allocatedAmount),
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt:278: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = String.format("$%.0f", creditCard.currentBalance),
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt:301: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = String.format("%.1f%% used", utilization * 100f),
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt:306: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = String.format("$%.0f available", availableCredit),
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt:861: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    value = "$${String.format("%.2f", payoffPlan.totalInterestPaid)}",
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt:866: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    value = "$${String.format("%.2f", payoffPlan.totalPayments)}",
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt:1000: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "Interest: $${String.format("%.2f", step.interestPaid)}",
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt:1011: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "$${String.format("%.2f", step.payment)}",
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt:1017: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Balance: $${String.format("%.2f", step.remainingBalance)}",
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\PerformanceOptimizationService.kt:99: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                            description = "You spend an average of $${String.format("%.2f", avgAmount)} on $category",
                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt:180: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "${String.format("%.1f", spentPercentage * 100)}%",
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt:254: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            text = "$${String.format("%.0f", amount)}",
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt:326: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.2f", budgetCategory.spentAmount)} of $${String.format("%.2f", budgetCategory.allocatedAmount)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt:326: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "$${String.format("%.2f", budgetCategory.spentAmount)} of $${String.format("%.2f", budgetCategory.allocatedAmount)}",
                                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt:330: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        text = "${String.format("%.1f", spentPercentage * 100)}%",
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt:358: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    text = "Over budget by $${String.format("%.2f", -remaining)}",
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ThemeSettingsComponents.kt:396: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        "Current scale: ${String.format("%.1f", currentScale)}"
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt:293: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = "${if (isOverBudget) "+" else ""}${String.format("%.1f", variancePercentage)}%",
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt:331: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = "$${String.format("%.0f", actualAmount)}",
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt:337: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = "Budget: $${String.format("%.0f", budgetAmount)}",
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt:369: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                text = "$${String.format("%.0f", category.amount)} ($percentage%)",
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:69: Error: NotificationService must extend android.app.Service [Instantiatable]
            android:name=".service.NotificationService"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Instantiatable":
   Activities, services, broadcast receivers etc. registered in the manifest
   file (or for custom views, in a layout file) must be "instantiatable" by
   the system, which means that the class must be public, it must have an
   empty public constructor, and if it's an inner class, it must be a static
   inner class.

   If you use a custom AppComponentFactory to instantiate app components
   yourself, consider disabling this Lint issue in order to avoid false
   positives.

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\DataProtectionService.kt:119: Error: Call requires API level 34 (current min is 24): java.util.regex.Matcher#replaceAll [NewApi]
        masked = CREDIT_CARD_PATTERN.matcher(masked).replaceAll { matchResult ->
                                                     ~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\DataProtectionService.kt:132: Error: Call requires API level 34 (current min is 24): java.util.regex.Matcher#replaceAll [NewApi]
        masked = BANK_ACCOUNT_PATTERN.matcher(masked).replaceAll { matchResult ->
                                                      ~~~~~~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:16: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application or sdk runs on a version of Android that is more
   recent than your targetSdkVersion specifies that it has been tested with,
   various compatibility modes kick in. This ensures that your application
   continues to work, but it may look out of place. For example, if the
   targetSdkVersion is less than 14, your app may get an option button in the
   UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\NotificationManager.kt:155: Error: When scheduling exact alarms, apps should explicitly call AlarmManager#canScheduleExactAlarms or handle `SecurityException`s [ScheduleExactAlarm]
                alarmManager.setExactAndAllowWhileIdle(
                ^

   Explanation for issues of type "ScheduleExactAlarm":
   Applications looking to schedule exact alarms should ensure that the
   SCHEDULE_EXACT_ALARM permission is granted by calling the
   AlarmManager#canScheduleExactAlarms API before attempting to set an exact
   alarm. If the permission is not granted to your application, please
   consider requesting it from the user by starting the
   ACTION_REQUEST_SCHEDULE_EXACT_ALARM intent or gracefully falling back to
   another option.

   https://developer.android.com/training/scheduling/alarms#exact

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:50: Warning: Redundant label can be removed [RedundantLabel]
            android:label="@string/app_name"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantLabel":
   When an activity does not have a label attribute, it will use the one from
   the application tag. Since the application has already specified the same
   label, the label on this activity can be omitted.

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:18: Warning: Your app is currently not handling Selected Photos Access introduced in Android 14+ [SelectedPhotoAccess]
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:19: Warning: Your app is currently not handling Selected Photos Access introduced in Android 14+ [SelectedPhotoAccess]
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SelectedPhotoAccess":
   Selected Photo Access is a new ability for users to share partial access to
   their photo library when apps request access to their device storage on
   Android 14+.

   Instead of letting the system manage the selection lifecycle, we recommend
   you adapt your app to handle partial access to the photo library.

   https://developer.android.com/about/versions/14/changes/partial-photo-video-access

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\gradle\wrapper\gradle-wrapper.properties:3: Warning: A newer version of Gradle than 8.12 is available: 8.14.2 [AndroidGradlePluginVersion]
distributionUrl=https\://services.gradle.org/distributions/gradle-8.12-bin.zip
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:11: Warning: A newer version of compileSdkVersion than 34 is available: 35 [GradleDependency]
    compileSdk 34
    ~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:116: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0 [GradleDependency]
    implementation 'androidx.core:core-ktx:1.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:117: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:118: Warning: A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1 [GradleDependency]
    implementation 'androidx.activity:activity-compose:1.8.2'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:121: Warning: A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00 [GradleDependency]
    implementation platform('androidx.compose:compose-bom:2023.10.01')
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:129: Warning: A newer version of androidx.navigation:navigation-compose than 2.7.5 is available: 2.9.0 [GradleDependency]
    implementation 'androidx.navigation:navigation-compose:2.7.5'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:132: Warning: A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.1 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:133: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.1 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-runtime-compose:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:137: Warning: A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0 [GradleDependency]
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:141: Warning: A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1 [GradleDependency]
    implementation 'androidx.room:room-runtime:2.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:142: Warning: A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1 [GradleDependency]
    implementation 'androidx.room:room-ktx:2.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:143: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1 [GradleDependency]
    kapt 'androidx.room:room-compiler:2.6.1'
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:163: Warning: A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-beta01 [GradleDependency]
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:166: Warning: A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1 [GradleDependency]
    implementation 'androidx.work:work-runtime-ktx:2.9.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:167: Warning: A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0 [GradleDependency]
    implementation 'androidx.hilt:hilt-work:1.1.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:173: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0 [GradleDependency]
    implementation 'androidx.core:core-ktx:1.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:184: Warning: A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5 [GradleDependency]
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.4'
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:188: Warning: A newer version of androidx.test:core than 1.5.0 is available: 1.6.1 [GradleDependency]
    testImplementation 'androidx.test:core:1.5.0'
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:189: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
    testImplementation 'androidx.test.ext:junit:1.1.5'
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:190: Warning: A newer version of androidx.room:room-testing than 2.6.1 is available: 2.7.1 [GradleDependency]
    testImplementation 'androidx.room:room-testing:2.6.1'
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:196: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1 [GradleDependency]
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:197: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1 [GradleDependency]
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:198: Warning: A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00 [GradleDependency]
    androidTestImplementation platform('androidx.compose:compose-bom:2023.10.01')
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:200: Warning: A newer version of androidx.navigation:navigation-testing than 2.7.5 is available: 2.9.0 [GradleDependency]
    androidTestImplementation 'androidx.navigation:navigation-testing:2.7.5'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\responsive\ResponsiveLayoutManager.kt:217: Warning: Modifier factory functions should not be marked as @Composable, and should use composed instead [ComposableModifierFactory from androidx.compose.ui]
    fun getCardModifier(): Modifier {
        ~~~~~~~~~~~~~~~

   Explanation for issues of type "ComposableModifierFactory":
   Modifier factory functions that need to be aware of the composition should
   use androidx.compose.ui.composed {} in their implementation instead of
   being marked as @Composable. This allows Modifiers to be referenced in top
   level variables and constructed outside of the composition.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.ui
   Feedback: https://issuetracker.google.com/issues/new?component=612128

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\responsive\ResponsiveLayoutManager.kt:217: Warning: Modifier factory functions should be extensions on Modifier [ModifierFactoryExtensionFunction from androidx.compose.ui]
    fun getCardModifier(): Modifier {
        ~~~~~~~~~~~~~~~

   Explanation for issues of type "ModifierFactoryExtensionFunction":
   Modifier factory functions should be defined as extension functions on
   Modifier to allow modifiers to be fluently chained.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.ui
   Feedback: https://issuetracker.google.com/issues/new?component=612128

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\DecisionSupportComponents.kt:29: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier,
    ~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\FocusTimerComponents.kt:41: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlSettingsCard.kt:23: Warning: Modifier parameter should be the first optional parameter [ModifierParameter from androidx.compose.ui]
    modifier: Modifier = Modifier
    ~~~~~~~~

   Explanation for issues of type "ModifierParameter":
   The first (or only) Modifier parameter in a Composable function should
   follow the following rules:
   - Be named modifier
   - Have a type of Modifier
   - Either have no default value, or have a default value of Modifier
   - If optional, be the first optional parameter in the parameter list

   Vendor: Jetpack Compose
   Identifier: androidx.compose.ui
   Feedback: https://issuetracker.google.com/issues/new?component=612128

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt:493: Warning: Creating a MutableState object with a mutable collection type [MutableCollectionMutableState from androidx.compose.runtime]
        mutableStateOf(
        ~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt:407: Warning: Creating a MutableState object with a mutable collection type [MutableCollectionMutableState from androidx.compose.runtime]
    var selectedCategories by remember { mutableStateOf(DefaultBudgetCategories.categories.toMap().toMutableMap()) }
                                         ~~~~~~~~~~~~~~

   Explanation for issues of type "MutableCollectionMutableState":
   Writes to mutable collections inside a MutableState will not cause a
   recomposition - only writes to the MutableState itself will. In most cases
   you should either use a read-only collection (such as List or Map) and
   assign a new instance to the MutableState when your data changes, or you
   can use an snapshot-backed collection such as SnapshotStateList or
   SnapshotStateMap which will correctly cause a recomposition when their
   contents are modified.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.runtime
   Feedback: https://issuetracker.google.com/issues/new?component=612128

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:52: Warning: Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed. [DiscouragedApi]
            android:screenOrientation="unspecified"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:65: Warning: Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed. [DiscouragedApi]
            android:screenOrientation="unspecified" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle:143: Warning: This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp [KaptUsageInsteadOfKsp]
    kapt 'androidx.room:room-compiler:2.6.1'
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "KaptUsageInsteadOfKsp":
   KSP is a more efficient replacement for kapt. For libraries that support
   both, KSP should be used to improve build times.

   https://developer.android.com/studio/build/migrate-to-ksp

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt:27: Hint: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var timeRemaining by remember { mutableStateOf(10) }
                                    ~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt:469: Hint: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var secondsRemaining by remember { mutableStateOf(4) }
                                       ~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt:470: Hint: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var cycleCount by remember { mutableStateOf(0) }
                                 ~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlSettingsCard.kt:335: Hint: Prefer mutableIntStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var selectedPeriod by remember { mutableStateOf(currentPeriod) }
                                     ~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt:32: Hint: Prefer mutableFloatStateOf instead of mutableStateOf [AutoboxingStateCreation from androidx.compose.runtime]
    var animationProgress by remember { mutableStateOf(if (animateEntry) 0f else 1f) }
                                        ~~~~~~~~~~~~~~

   Explanation for issues of type "AutoboxingStateCreation":
   Calling mutableStateOf<T>() when T is either backed by a primitive type on
   the JVM or is a value class results in a state implementation that requires
   all state values to be boxed. This usually causes an additional allocation
   for each state write, and adds some additional work to auto-unbox values
   when reading the value of the state. Instead, prefer to use a specialized
   primitive state implementation for Int, Long, Float, and Double when the
   state does not need to track null values and does not override the default
   SnapshotMutationPolicy.

   Vendor: Jetpack Compose
   Identifier: androidx.compose.runtime
   Feedback: https://issuetracker.google.com/issues/new?component=612128

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:5: Warning: The resource R.color.purple_500 appears to be unused [UnusedResources]
    <color name="purple_500">#FF6200EE</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:6: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#FF3700B3</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:7: Warning: The resource R.color.teal_200 appears to be unused [UnusedResources]
    <color name="teal_200">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:8: Warning: The resource R.color.teal_700 appears to be unused [UnusedResources]
    <color name="teal_700">#FF018786</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.focus_orange appears to be unused [UnusedResources]
    <color name="focus_orange">#FFFF9800</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:19: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:21: Warning: The resource R.color.gray_50 appears to be unused [UnusedResources]
    <color name="gray_50">#FFFAFAFA</color>
           ~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:22: Warning: The resource R.color.gray_100 appears to be unused [UnusedResources]
    <color name="gray_100">#FFF5F5F5</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.gray_200 appears to be unused [UnusedResources]
    <color name="gray_200">#FFEEEEEE</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.gray_300 appears to be unused [UnusedResources]
    <color name="gray_300">#FFE0E0E0</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:25: Warning: The resource R.color.gray_400 appears to be unused [UnusedResources]
    <color name="gray_400">#FFBDBDBD</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:26: Warning: The resource R.color.gray_500 appears to be unused [UnusedResources]
    <color name="gray_500">#FF9E9E9E</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:27: Warning: The resource R.color.gray_600 appears to be unused [UnusedResources]
    <color name="gray_600">#FF757575</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:28: Warning: The resource R.color.gray_700 appears to be unused [UnusedResources]
    <color name="gray_700">#FF616161</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:29: Warning: The resource R.color.gray_800 appears to be unused [UnusedResources]
    <color name="gray_800">#FF424242</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:30: Warning: The resource R.color.gray_900 appears to be unused [UnusedResources]
    <color name="gray_900">#FF212121</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:35: Warning: The resource R.color.text_primary appears to be unused [UnusedResources]
    <color name="text_primary">#FF212121</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:36: Warning: The resource R.color.text_secondary appears to be unused [UnusedResources]
    <color name="text_secondary">#FF757575</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:37: Warning: The resource R.color.surface_light appears to be unused [UnusedResources]
    <color name="surface_light">#FFFFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:38: Warning: The resource R.color.on_surface_light appears to be unused [UnusedResources]
    <color name="on_surface_light">#FF000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:42: Warning: The resource R.color.dark_background_soft appears to be unused [UnusedResources]
    <color name="dark_background_soft">#FF1E1E1E</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:44: Warning: The resource R.color.dark_on_surface appears to be unused [UnusedResources]
    <color name="dark_on_surface">#FFFFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:45: Warning: The resource R.color.dark_text_primary appears to be unused [UnusedResources]
    <color name="dark_text_primary">#FFFFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:46: Warning: The resource R.color.dark_text_secondary appears to be unused [UnusedResources]
    <color name="dark_text_secondary">#FFBDBDBD</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:49: Warning: The resource R.color.high_contrast_primary appears to be unused [UnusedResources]
    <color name="high_contrast_primary">#FF000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:50: Warning: The resource R.color.high_contrast_accent appears to be unused [UnusedResources]
    <color name="high_contrast_accent">#FFFF0000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:51: Warning: The resource R.color.high_contrast_text appears to be unused [UnusedResources]
    <color name="high_contrast_text">#FF000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:52: Warning: The resource R.color.high_contrast_surface appears to be unused [UnusedResources]
    <color name="high_contrast_surface">#FFFFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:55: Warning: The resource R.color.high_contrast_dark_primary appears to be unused [UnusedResources]
    <color name="high_contrast_dark_primary">#FFFFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:56: Warning: The resource R.color.high_contrast_dark_accent appears to be unused [UnusedResources]
    <color name="high_contrast_dark_accent">#FFFF4444</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:57: Warning: The resource R.color.high_contrast_dark_text appears to be unused [UnusedResources]
    <color name="high_contrast_dark_text">#FFFFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:58: Warning: The resource R.color.high_contrast_dark_surface appears to be unused [UnusedResources]
    <color name="high_contrast_dark_surface">#FF000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:59: Warning: The resource R.color.high_contrast_dark_background appears to be unused [UnusedResources]
    <color name="high_contrast_dark_background">#FF000000</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:62: Warning: The resource R.color.adhd_calm_blue appears to be unused [UnusedResources]
    <color name="adhd_calm_blue">#FF81C784</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:63: Warning: The resource R.color.adhd_focus_green appears to be unused [UnusedResources]
    <color name="adhd_focus_green">#FF66BB6A</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:64: Warning: The resource R.color.adhd_warning_amber appears to be unused [UnusedResources]
    <color name="adhd_warning_amber">#FFFFB74D</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:65: Warning: The resource R.color.adhd_error_red appears to be unused [UnusedResources]
    <color name="adhd_error_red">#FFE57373</color>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml:66: Warning: The resource R.color.adhd_success_green appears to be unused [UnusedResources]
    <color name="adhd_success_green">#FF81C784</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:4: Warning: The resource R.dimen.text_size_headline appears to be unused [UnusedResources]
    <dimen name="text_size_headline">24sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:5: Warning: The resource R.dimen.text_size_title appears to be unused [UnusedResources]
    <dimen name="text_size_title">20sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:6: Warning: The resource R.dimen.text_size_body appears to be unused [UnusedResources]
    <dimen name="text_size_body">16sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:7: Warning: The resource R.dimen.text_size_caption appears to be unused [UnusedResources]
    <dimen name="text_size_caption">14sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:8: Warning: The resource R.dimen.text_size_small appears to be unused [UnusedResources]
    <dimen name="text_size_small">12sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:11: Warning: The resource R.dimen.text_size_headline_large appears to be unused [UnusedResources]
    <dimen name="text_size_headline_large">28sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:12: Warning: The resource R.dimen.text_size_title_large appears to be unused [UnusedResources]
    <dimen name="text_size_title_large">24sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:13: Warning: The resource R.dimen.text_size_body_large appears to be unused [UnusedResources]
    <dimen name="text_size_body_large">18sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:14: Warning: The resource R.dimen.text_size_caption_large appears to be unused [UnusedResources]
    <dimen name="text_size_caption_large">16sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:17: Warning: The resource R.dimen.text_size_headline_xl appears to be unused [UnusedResources]
    <dimen name="text_size_headline_xl">32sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:18: Warning: The resource R.dimen.text_size_title_xl appears to be unused [UnusedResources]
    <dimen name="text_size_title_xl">28sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:19: Warning: The resource R.dimen.text_size_body_xl appears to be unused [UnusedResources]
    <dimen name="text_size_body_xl">22sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:20: Warning: The resource R.dimen.text_size_caption_xl appears to be unused [UnusedResources]
    <dimen name="text_size_caption_xl">20sp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:23: Warning: The resource R.dimen.spacing_xs appears to be unused [UnusedResources]
    <dimen name="spacing_xs">4dp</dimen>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:24: Warning: The resource R.dimen.spacing_sm appears to be unused [UnusedResources]
    <dimen name="spacing_sm">8dp</dimen>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:25: Warning: The resource R.dimen.spacing_md appears to be unused [UnusedResources]
    <dimen name="spacing_md">16dp</dimen>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:26: Warning: The resource R.dimen.spacing_lg appears to be unused [UnusedResources]
    <dimen name="spacing_lg">24dp</dimen>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:27: Warning: The resource R.dimen.spacing_xl appears to be unused [UnusedResources]
    <dimen name="spacing_xl">32dp</dimen>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:30: Warning: The resource R.dimen.touch_target_min appears to be unused [UnusedResources]
    <dimen name="touch_target_min">48dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:31: Warning: The resource R.dimen.touch_target_comfortable appears to be unused [UnusedResources]
    <dimen name="touch_target_comfortable">56dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:32: Warning: The resource R.dimen.touch_target_large appears to be unused [UnusedResources]
    <dimen name="touch_target_large">64dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:35: Warning: The resource R.dimen.card_corner_radius appears to be unused [UnusedResources]
    <dimen name="card_corner_radius">12dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:36: Warning: The resource R.dimen.card_elevation appears to be unused [UnusedResources]
    <dimen name="card_elevation">4dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:37: Warning: The resource R.dimen.button_corner_radius appears to be unused [UnusedResources]
    <dimen name="button_corner_radius">8dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:40: Warning: The resource R.dimen.focus_border_width appears to be unused [UnusedResources]
    <dimen name="focus_border_width">3dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml:41: Warning: The resource R.dimen.focus_corner_radius appears to be unused [UnusedResources]
    <dimen name="focus_corner_radius">8dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\drawable\ic_launcher_foreground.xml:2: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml:3: Warning: The resource R.string.welcome_title appears to be unused [UnusedResources]
    <string name="welcome_title">Welcome to FocusFlow</string>
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.welcome_subtitle appears to be unused [UnusedResources]
    <string name="welcome_subtitle">Your ADHD-friendly companion for better focus and productivity</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.get_started appears to be unused [UnusedResources]
    <string name="get_started">Get Started</string>
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.notification_channel_name appears to be unused [UnusedResources]
    <string name="notification_channel_name">FocusFlow Notifications</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.notification_channel_description appears to be unused [UnusedResources]
    <string name="notification_channel_description">Notifications for tasks, reminders and focus sessions</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.default_notification_title appears to be unused [UnusedResources]
    <string name="default_notification_title">FocusFlow Reminder</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.default_notification_message appears to be unused [UnusedResources]
    <string name="default_notification_message">Time to check your tasks!</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt:397: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                                    Color(android.graphics.Color.parseColor(color)),
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt:431: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
                                    Color(android.graphics.Color.parseColor(color)),
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:71: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
        Color(android.graphics.Color.parseColor(category.categoryColor))
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt:581: Warning: Use the KTX extension function String.toColorInt instead? [UseKtx]
        Color(android.graphics.Color.parseColor(category.categoryColor))
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\SecurityManager.kt:153: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
                encryptedPrefs.edit().putString(key, value).apply()
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\SecurityManager.kt:276: Warning: Use the KTX extension function SharedPreferences.edit instead? [UseKtx]
                encryptedPrefs.edit().clear().apply()
                ~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseKtx":
   The Android KTX libraries decorates the Android platform SDK as well as
   various libraries with more convenient extension functions available from
   Kotlin, allowing you to use default parameters, named parameters, and
   more.

   Available options:

   **remove-defaults** (default is true):
   Whether to skip arguments that match the defaults provided by the extension.

   Extensions often provide default values for some of the parameters. For example:
   ```kotlin
   fun Path.readLines(charset: Charset = Charsets.UTF_8): List<String> { return Files.readAllLines(this, charset) }
   ```
   This lint check will by default automatically omit parameters that match the default, so if your code was calling

   ```kotlin
   Files.readAllLines(file, Charset.UTF_8)
   ```
   lint would replace this with
   ```kotlin
   file.readLines()
   ```
   rather than

   ```kotlin
   file.readLines(Charset.UTF_8
   ```
   You can turn this behavior off using this option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="remove-defaults" value="true" />
       </issue>
   </lint>
   ```

   **require-present** (default is true):
   Whether to only offer extensions already available.

   This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UseKtx">
           <option name="require-present" value="true" />
       </issue>
   </lint>
   ```

6 errors, 200 warnings, 5 hints
