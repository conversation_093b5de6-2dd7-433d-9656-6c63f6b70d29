<lint-module
    format="1"
    dir="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app"
    name=":app"
    type="APP"
    maven="FocusFlow:app:unspecified"
    agpVersion="8.10.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-34\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-34">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
