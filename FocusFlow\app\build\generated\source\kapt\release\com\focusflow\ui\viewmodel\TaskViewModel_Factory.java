package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.TaskRepository;
import com.focusflow.service.GamificationService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class TaskViewModel_Factory implements Factory<TaskViewModel> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<GamificationService> gamificationServiceProvider;

  public TaskViewModel_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.gamificationServiceProvider = gamificationServiceProvider;
  }

  @Override
  public TaskViewModel get() {
    return newInstance(taskRepositoryProvider.get(), gamificationServiceProvider.get());
  }

  public static TaskViewModel_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    return new TaskViewModel_Factory(taskRepositoryProvider, gamificationServiceProvider);
  }

  public static TaskViewModel newInstance(TaskRepository taskRepository,
      GamificationService gamificationService) {
    return new TaskViewModel(taskRepository, gamificationService);
  }
}
