package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.WishlistItem
import com.focusflow.data.model.BudgetCategory
import com.focusflow.data.repository.WishlistRepository
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.service.PurchaseDelayService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class ImpulseControlViewModel @Inject constructor(
    private val wishlistRepository: WishlistRepository,
    private val purchaseDelayService: PurchaseDelayService,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val budgetCategoryRepository: BudgetCategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(ImpulseControlUiState())
    val uiState: StateFlow<ImpulseControlUiState> = _uiState.asStateFlow()

    val activeWishlistItems = wishlistRepository.getAllActiveWishlistItems()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val activeDelayItems = wishlistRepository.getActiveDelayItems()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    init {
        loadDelayStatistics()
        checkExpiredDelays()
        loadImpulseControlSettings()
    }

    private fun loadImpulseControlSettings() {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                _uiState.value = _uiState.value.copy(
                    impulseControlEnabled = preferences?.impulseControlEnabled ?: true,
                    spendingThreshold = preferences?.spendingThreshold ?: 50.0,
                    coolingOffPeriodSeconds = preferences?.coolingOffPeriodSeconds ?: 10,
                    enableReflectionQuestions = preferences?.enableReflectionQuestions ?: true,
                    enableBudgetWarnings = preferences?.enableBudgetWarnings ?: true,
                    enableWishlistSuggestions = preferences?.enableWishlistSuggestions ?: true,
                    budgetPeriod = preferences?.budgetPeriod ?: "weekly"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load impulse control settings: ${e.message}"
                )
            }
        }
    }

    fun addItemToWishlist(
        itemName: String,
        estimatedPrice: Double,
        category: String,
        description: String? = null,
        merchant: String? = null,
        delayPeriodHours: Int = 24,
        priority: String = "medium"
    ) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val itemId = purchaseDelayService.addItemToDelayList(
                    itemName = itemName,
                    estimatedPrice = estimatedPrice,
                    category = category,
                    description = description,
                    merchant = merchant,
                    delayPeriodHours = delayPeriodHours,
                    priority = priority
                )
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    lastAddedItemId = itemId
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to add item to wishlist: ${e.message}"
                )
            }
        }
    }

    fun getRecommendedDelayPeriod(amount: Double, category: String) {
        viewModelScope.launch {
            try {
                val recommendation = purchaseDelayService.getRecommendedDelayPeriod(amount, category)
                _uiState.value = _uiState.value.copy(
                    recommendedDelayPeriod = recommendation.hours,
                    delayRecommendationReason = recommendation.reason
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to get delay recommendation: ${e.message}"
                )
            }
        }
    }

    fun extendDelay(itemId: Long, additionalHours: Int) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.extendDelay(itemId, additionalHours)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to extend delay period"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to extend delay: ${e.message}"
                )
            }
        }
    }

    fun removeFromDelayList(itemId: Long) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.removeFromDelayList(itemId)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to remove item from delay list"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to remove item: ${e.message}"
                )
            }
        }
    }

    fun markAsPurchased(itemId: Long, actualPrice: Double, reflectionNotes: String? = null) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.markAsPurchased(itemId, actualPrice, reflectionNotes)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to mark item as purchased"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to mark as purchased: ${e.message}"
                )
            }
        }
    }

    fun addReflection(itemId: Long, stillWanted: Boolean, notes: String?) {
        viewModelScope.launch {
            try {
                val success = purchaseDelayService.addReflection(itemId, stillWanted, notes)
                if (!success) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to save reflection"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to save reflection: ${e.message}"
                )
            }
        }
    }

    fun startBreathingExercise() {
        _uiState.value = _uiState.value.copy(
            isBreathingExerciseActive = true
        )
    }

    fun completeBreathingExercise() {
        _uiState.value = _uiState.value.copy(
            isBreathingExerciseActive = false,
            breathingExerciseCompleted = true
        )
    }

    fun dismissBreathingExercise() {
        _uiState.value = _uiState.value.copy(
            isBreathingExerciseActive = false,
            breathingExerciseCompleted = false
        )
    }

    private fun loadDelayStatistics() {
        viewModelScope.launch {
            try {
                val statistics = purchaseDelayService.getDelayStatistics()
                _uiState.value = _uiState.value.copy(
                    delayStatistics = statistics
                )
            } catch (e: Exception) {
                // Don't show error for statistics loading
            }
        }
    }

    private fun checkExpiredDelays() {
        viewModelScope.launch {
            try {
                purchaseDelayService.processExpiredDelays()
            } catch (e: Exception) {
                // Log error but don't show to user
            }
        }
    }

    fun getDelayPeriodOptions() = purchaseDelayService.getDelayPeriodOptions()

    fun calculateRemainingDelayTime(item: WishlistItem): Long {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val endTime = item.delayEndTime
        
        return if (endTime > now) {
            val duration = endTime.toInstant(TimeZone.currentSystemDefault()) - 
                          now.toInstant(TimeZone.currentSystemDefault())
            duration.inWholeHours
        } else {
            0L
        }
    }

    fun shouldShowImpulseControl(amount: Double, category: String): Boolean {
        val currentState = _uiState.value
        if (!currentState.impulseControlEnabled) return false

        // Check spending threshold
        if (amount >= currentState.spendingThreshold) return true

        // Always trigger for high-risk categories regardless of amount
        val highRiskCategories = listOf("Shopping", "Entertainment", "Dining Out", "Online Shopping", "Impulse")
        if (category in highRiskCategories) return true

        return false
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun updateSpendingThreshold(threshold: Double) {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val updatedPrefs = preferences?.copy(spendingThreshold = threshold)
                if (updatedPrefs != null) {
                    userPreferencesRepository.updateUserPreferences(updatedPrefs)
                    _uiState.value = _uiState.value.copy(spendingThreshold = threshold)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = "Failed to update spending threshold: ${e.message}")
            }
        }
    }

    fun updateCoolingOffPeriod(seconds: Int) {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val updatedPrefs = preferences?.copy(coolingOffPeriodSeconds = seconds)
                if (updatedPrefs != null) {
                    userPreferencesRepository.updateUserPreferences(updatedPrefs)
                    _uiState.value = _uiState.value.copy(coolingOffPeriodSeconds = seconds)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = "Failed to update cooling-off period: ${e.message}")
            }
        }
    }

    fun toggleImpulseControl(enabled: Boolean) {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val updatedPrefs = preferences?.copy(impulseControlEnabled = enabled)
                if (updatedPrefs != null) {
                    userPreferencesRepository.updateUserPreferences(updatedPrefs)
                    _uiState.value = _uiState.value.copy(impulseControlEnabled = enabled)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(error = "Failed to toggle impulse control: ${e.message}")
            }
        }
    }

    fun updateSpendingThreshold(threshold: Double) {
        _uiState.value = _uiState.value.copy(spendingThreshold = threshold)
    }

    fun updateCoolingOffPeriod(seconds: Int) {
        _uiState.value = _uiState.value.copy(coolingOffPeriodSeconds = seconds)
    }

    fun toggleImpulseControl(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(impulseControlEnabled = enabled)
    }

    fun toggleReflectionQuestions(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(enableReflectionQuestions = enabled)
    }

    fun toggleBudgetWarnings(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(enableBudgetWarnings = enabled)
    }

    fun toggleWishlistSuggestions(enabled: Boolean) {
        _uiState.value = _uiState.value.copy(enableWishlistSuggestions = enabled)
    }

    fun getBudgetImpactPreview(amount: Double, category: String): BudgetImpactPreview {
        return runBlocking {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val period = preferences?.budgetPeriod ?: "weekly"

                // Get budget categories for current period
                val budgetCategories = budgetCategoryRepository.getBudgetCategoriesByPeriod(period).first()

                // Find matching category or closest match
                val targetCategory = budgetCategories.find { it.name.equals(category, ignoreCase = true) }
                    ?: budgetCategories.find { it.name.contains(category, ignoreCase = true) }

                if (targetCategory != null) {
                    val remainingAfterPurchase = targetCategory.allocatedAmount - targetCategory.spentAmount - amount
                    val wouldExceedBudget = remainingAfterPurchase < 0

                    // Find alternative categories with available funds
                    val alternativeCategories = budgetCategories.filter {
                        it.id != targetCategory.id &&
                        (it.allocatedAmount - it.spentAmount) >= amount
                    }.take(3)

                    // Calculate total budget impact
                    val totalAllocated = budgetCategories.sumOf { it.allocatedAmount }
                    val totalSpent = budgetCategories.sumOf { it.spentAmount } + amount
                    val totalRemaining = totalAllocated - totalSpent

                    BudgetImpactPreview(
                        targetCategory = targetCategory,
                        purchaseAmount = amount,
                        wouldExceedBudget = wouldExceedBudget,
                        remainingAfterPurchase = remainingAfterPurchase,
                        alternativeCategories = alternativeCategories,
                        totalBudgetImpact = TotalBudgetImpact(
                            totalAllocated = totalAllocated,
                            totalSpent = totalSpent,
                            totalRemaining = totalRemaining
                        )
                    )
                } else {
                    // No matching category found - create warning
                    BudgetImpactPreview(
                        targetCategory = null,
                        purchaseAmount = amount,
                        wouldExceedBudget = true,
                        remainingAfterPurchase = -amount,
                        alternativeCategories = budgetCategories.filter {
                            (it.allocatedAmount - it.spentAmount) >= amount
                        }.take(3),
                        totalBudgetImpact = TotalBudgetImpact(
                            totalAllocated = budgetCategories.sumOf { it.allocatedAmount },
                            totalSpent = budgetCategories.sumOf { it.spentAmount } + amount,
                            totalRemaining = budgetCategories.sumOf { it.allocatedAmount - it.spentAmount } - amount
                        )
                    )
                }
            } catch (e: Exception) {
                // Fallback to simple preview
                BudgetImpactPreview(
                    targetCategory = null,
                    purchaseAmount = amount,
                    wouldExceedBudget = amount > 50.0,
                    remainingAfterPurchase = 0.0,
                    alternativeCategories = emptyList(),
                    totalBudgetImpact = TotalBudgetImpact(0.0, amount, -amount)
                )
            }
        }
    }
}

data class ImpulseControlUiState(
    val isLoading: Boolean = false,
    val lastAddedItemId: Long? = null,
    val recommendedDelayPeriod: Int? = null,
    val delayRecommendationReason: String? = null,
    val isBreathingExerciseActive: Boolean = false,
    val breathingExerciseCompleted: Boolean = false,
    val delayStatistics: com.focusflow.service.DelayStatistics? = null,
    val error: String? = null,
    // Settings properties
    val impulseControlEnabled: Boolean = true,
    val spendingThreshold: Double = 50.0,
    val coolingOffPeriodSeconds: Int = 10,
    val enableReflectionQuestions: Boolean = true,
    val enableBudgetWarnings: Boolean = true,
    val enableWishlistSuggestions: Boolean = true
)

data class BudgetImpactPreview(
    val targetCategory: BudgetCategory?,
    val purchaseAmount: Double,
    val wouldExceedBudget: Boolean,
    val remainingAfterPurchase: Double,
    val alternativeCategories: List<BudgetCategory>,
    val totalBudgetImpact: TotalBudgetImpact
)

data class TotalBudgetImpact(
    val totalAllocated: Double,
    val totalSpent: Double,
    val totalRemaining: Double
)
