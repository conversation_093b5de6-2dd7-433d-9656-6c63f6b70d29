package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.SpendingReflection;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SpendingReflectionDao_Impl implements SpendingReflectionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SpendingReflection> __insertionAdapterOfSpendingReflection;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<SpendingReflection> __deletionAdapterOfSpendingReflection;

  private final EntityDeletionOrUpdateAdapter<SpendingReflection> __updateAdapterOfSpendingReflection;

  private final SharedSQLiteStatement __preparedStmtOfDeleteReflectionsOlderThan;

  public SpendingReflectionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSpendingReflection = new EntityInsertionAdapter<SpendingReflection>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `spending_reflections` (`id`,`expenseId`,`wishlistItemId`,`amount`,`category`,`itemDescription`,`reflectionDate`,`emotionalState`,`triggerReason`,`needVsWant`,`satisfactionLevel`,`regretLevel`,`wouldBuyAgain`,`alternativeConsidered`,`delayHelpful`,`notes`,`mindfulnessScore`,`budgetImpact`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SpendingReflection entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getExpenseId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindLong(2, entity.getExpenseId());
        }
        if (entity.getWishlistItemId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, entity.getWishlistItemId());
        }
        statement.bindDouble(4, entity.getAmount());
        if (entity.getCategory() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getCategory());
        }
        if (entity.getItemDescription() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getItemDescription());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getReflectionDate());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        if (entity.getEmotionalState() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getEmotionalState());
        }
        if (entity.getTriggerReason() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getTriggerReason());
        }
        if (entity.getNeedVsWant() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getNeedVsWant());
        }
        if (entity.getSatisfactionLevel() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getSatisfactionLevel());
        }
        if (entity.getRegretLevel() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getRegretLevel());
        }
        final Integer _tmp_1 = entity.getWouldBuyAgain() == null ? null : (entity.getWouldBuyAgain() ? 1 : 0);
        if (_tmp_1 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_1);
        }
        if (entity.getAlternativeConsidered() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getAlternativeConsidered());
        }
        final Integer _tmp_2 = entity.getDelayHelpful() == null ? null : (entity.getDelayHelpful() ? 1 : 0);
        if (_tmp_2 == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, _tmp_2);
        }
        if (entity.getNotes() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getNotes());
        }
        if (entity.getMindfulnessScore() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getMindfulnessScore());
        }
        if (entity.getBudgetImpact() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getBudgetImpact());
        }
      }
    };
    this.__deletionAdapterOfSpendingReflection = new EntityDeletionOrUpdateAdapter<SpendingReflection>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `spending_reflections` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SpendingReflection entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfSpendingReflection = new EntityDeletionOrUpdateAdapter<SpendingReflection>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `spending_reflections` SET `id` = ?,`expenseId` = ?,`wishlistItemId` = ?,`amount` = ?,`category` = ?,`itemDescription` = ?,`reflectionDate` = ?,`emotionalState` = ?,`triggerReason` = ?,`needVsWant` = ?,`satisfactionLevel` = ?,`regretLevel` = ?,`wouldBuyAgain` = ?,`alternativeConsidered` = ?,`delayHelpful` = ?,`notes` = ?,`mindfulnessScore` = ?,`budgetImpact` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SpendingReflection entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getExpenseId() == null) {
          statement.bindNull(2);
        } else {
          statement.bindLong(2, entity.getExpenseId());
        }
        if (entity.getWishlistItemId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindLong(3, entity.getWishlistItemId());
        }
        statement.bindDouble(4, entity.getAmount());
        if (entity.getCategory() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getCategory());
        }
        if (entity.getItemDescription() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getItemDescription());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getReflectionDate());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        if (entity.getEmotionalState() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getEmotionalState());
        }
        if (entity.getTriggerReason() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getTriggerReason());
        }
        if (entity.getNeedVsWant() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getNeedVsWant());
        }
        if (entity.getSatisfactionLevel() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getSatisfactionLevel());
        }
        if (entity.getRegretLevel() == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, entity.getRegretLevel());
        }
        final Integer _tmp_1 = entity.getWouldBuyAgain() == null ? null : (entity.getWouldBuyAgain() ? 1 : 0);
        if (_tmp_1 == null) {
          statement.bindNull(13);
        } else {
          statement.bindLong(13, _tmp_1);
        }
        if (entity.getAlternativeConsidered() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getAlternativeConsidered());
        }
        final Integer _tmp_2 = entity.getDelayHelpful() == null ? null : (entity.getDelayHelpful() ? 1 : 0);
        if (_tmp_2 == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, _tmp_2);
        }
        if (entity.getNotes() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getNotes());
        }
        if (entity.getMindfulnessScore() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getMindfulnessScore());
        }
        if (entity.getBudgetImpact() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getBudgetImpact());
        }
        statement.bindLong(19, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteReflectionsOlderThan = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM spending_reflections WHERE reflectionDate < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertReflection(final SpendingReflection reflection,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfSpendingReflection.insertAndReturnId(reflection);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteReflection(final SpendingReflection reflection,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfSpendingReflection.handle(reflection);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateReflection(final SpendingReflection reflection,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSpendingReflection.handle(reflection);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteReflectionsOlderThan(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteReflectionsOlderThan.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteReflectionsOlderThan.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SpendingReflection>> getAllReflections() {
    final String _sql = "SELECT `spending_reflections`.`id` AS `id`, `spending_reflections`.`expenseId` AS `expenseId`, `spending_reflections`.`wishlistItemId` AS `wishlistItemId`, `spending_reflections`.`amount` AS `amount`, `spending_reflections`.`category` AS `category`, `spending_reflections`.`itemDescription` AS `itemDescription`, `spending_reflections`.`reflectionDate` AS `reflectionDate`, `spending_reflections`.`emotionalState` AS `emotionalState`, `spending_reflections`.`triggerReason` AS `triggerReason`, `spending_reflections`.`needVsWant` AS `needVsWant`, `spending_reflections`.`satisfactionLevel` AS `satisfactionLevel`, `spending_reflections`.`regretLevel` AS `regretLevel`, `spending_reflections`.`wouldBuyAgain` AS `wouldBuyAgain`, `spending_reflections`.`alternativeConsidered` AS `alternativeConsidered`, `spending_reflections`.`delayHelpful` AS `delayHelpful`, `spending_reflections`.`notes` AS `notes`, `spending_reflections`.`mindfulnessScore` AS `mindfulnessScore`, `spending_reflections`.`budgetImpact` AS `budgetImpact` FROM spending_reflections ORDER BY reflectionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_reflections"}, new Callable<List<SpendingReflection>>() {
      @Override
      @NonNull
      public List<SpendingReflection> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfExpenseId = 1;
          final int _cursorIndexOfWishlistItemId = 2;
          final int _cursorIndexOfAmount = 3;
          final int _cursorIndexOfCategory = 4;
          final int _cursorIndexOfItemDescription = 5;
          final int _cursorIndexOfReflectionDate = 6;
          final int _cursorIndexOfEmotionalState = 7;
          final int _cursorIndexOfTriggerReason = 8;
          final int _cursorIndexOfNeedVsWant = 9;
          final int _cursorIndexOfSatisfactionLevel = 10;
          final int _cursorIndexOfRegretLevel = 11;
          final int _cursorIndexOfWouldBuyAgain = 12;
          final int _cursorIndexOfAlternativeConsidered = 13;
          final int _cursorIndexOfDelayHelpful = 14;
          final int _cursorIndexOfNotes = 15;
          final int _cursorIndexOfMindfulnessScore = 16;
          final int _cursorIndexOfBudgetImpact = 17;
          final List<SpendingReflection> _result = new ArrayList<SpendingReflection>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingReflection _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _item = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingReflection>> getReflectionsByCategory(final String category) {
    final String _sql = "SELECT * FROM spending_reflections WHERE category = ? ORDER BY reflectionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_reflections"}, new Callable<List<SpendingReflection>>() {
      @Override
      @NonNull
      public List<SpendingReflection> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfExpenseId = CursorUtil.getColumnIndexOrThrow(_cursor, "expenseId");
          final int _cursorIndexOfWishlistItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "wishlistItemId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItemDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "itemDescription");
          final int _cursorIndexOfReflectionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionDate");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfTriggerReason = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerReason");
          final int _cursorIndexOfNeedVsWant = CursorUtil.getColumnIndexOrThrow(_cursor, "needVsWant");
          final int _cursorIndexOfSatisfactionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "satisfactionLevel");
          final int _cursorIndexOfRegretLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "regretLevel");
          final int _cursorIndexOfWouldBuyAgain = CursorUtil.getColumnIndexOrThrow(_cursor, "wouldBuyAgain");
          final int _cursorIndexOfAlternativeConsidered = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeConsidered");
          final int _cursorIndexOfDelayHelpful = CursorUtil.getColumnIndexOrThrow(_cursor, "delayHelpful");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfMindfulnessScore = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulnessScore");
          final int _cursorIndexOfBudgetImpact = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetImpact");
          final List<SpendingReflection> _result = new ArrayList<SpendingReflection>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingReflection _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _item = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getReflectionByExpenseId(final long expenseId,
      final Continuation<? super SpendingReflection> $completion) {
    final String _sql = "SELECT * FROM spending_reflections WHERE expenseId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, expenseId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SpendingReflection>() {
      @Override
      @Nullable
      public SpendingReflection call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfExpenseId = CursorUtil.getColumnIndexOrThrow(_cursor, "expenseId");
          final int _cursorIndexOfWishlistItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "wishlistItemId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItemDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "itemDescription");
          final int _cursorIndexOfReflectionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionDate");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfTriggerReason = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerReason");
          final int _cursorIndexOfNeedVsWant = CursorUtil.getColumnIndexOrThrow(_cursor, "needVsWant");
          final int _cursorIndexOfSatisfactionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "satisfactionLevel");
          final int _cursorIndexOfRegretLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "regretLevel");
          final int _cursorIndexOfWouldBuyAgain = CursorUtil.getColumnIndexOrThrow(_cursor, "wouldBuyAgain");
          final int _cursorIndexOfAlternativeConsidered = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeConsidered");
          final int _cursorIndexOfDelayHelpful = CursorUtil.getColumnIndexOrThrow(_cursor, "delayHelpful");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfMindfulnessScore = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulnessScore");
          final int _cursorIndexOfBudgetImpact = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetImpact");
          final SpendingReflection _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _result = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getReflectionByWishlistItemId(final long wishlistItemId,
      final Continuation<? super SpendingReflection> $completion) {
    final String _sql = "SELECT * FROM spending_reflections WHERE wishlistItemId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, wishlistItemId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SpendingReflection>() {
      @Override
      @Nullable
      public SpendingReflection call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfExpenseId = CursorUtil.getColumnIndexOrThrow(_cursor, "expenseId");
          final int _cursorIndexOfWishlistItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "wishlistItemId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItemDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "itemDescription");
          final int _cursorIndexOfReflectionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionDate");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfTriggerReason = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerReason");
          final int _cursorIndexOfNeedVsWant = CursorUtil.getColumnIndexOrThrow(_cursor, "needVsWant");
          final int _cursorIndexOfSatisfactionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "satisfactionLevel");
          final int _cursorIndexOfRegretLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "regretLevel");
          final int _cursorIndexOfWouldBuyAgain = CursorUtil.getColumnIndexOrThrow(_cursor, "wouldBuyAgain");
          final int _cursorIndexOfAlternativeConsidered = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeConsidered");
          final int _cursorIndexOfDelayHelpful = CursorUtil.getColumnIndexOrThrow(_cursor, "delayHelpful");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfMindfulnessScore = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulnessScore");
          final int _cursorIndexOfBudgetImpact = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetImpact");
          final SpendingReflection _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _result = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SpendingReflection>> getReflectionsByDateRange(final LocalDateTime startDate,
      final LocalDateTime endDate) {
    final String _sql = "SELECT * FROM spending_reflections WHERE reflectionDate BETWEEN ? AND ? ORDER BY reflectionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDateTime(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    _argIndex = 2;
    final String _tmp_1 = __converters.fromLocalDateTime(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp_1);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_reflections"}, new Callable<List<SpendingReflection>>() {
      @Override
      @NonNull
      public List<SpendingReflection> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfExpenseId = CursorUtil.getColumnIndexOrThrow(_cursor, "expenseId");
          final int _cursorIndexOfWishlistItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "wishlistItemId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItemDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "itemDescription");
          final int _cursorIndexOfReflectionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionDate");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfTriggerReason = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerReason");
          final int _cursorIndexOfNeedVsWant = CursorUtil.getColumnIndexOrThrow(_cursor, "needVsWant");
          final int _cursorIndexOfSatisfactionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "satisfactionLevel");
          final int _cursorIndexOfRegretLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "regretLevel");
          final int _cursorIndexOfWouldBuyAgain = CursorUtil.getColumnIndexOrThrow(_cursor, "wouldBuyAgain");
          final int _cursorIndexOfAlternativeConsidered = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeConsidered");
          final int _cursorIndexOfDelayHelpful = CursorUtil.getColumnIndexOrThrow(_cursor, "delayHelpful");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfMindfulnessScore = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulnessScore");
          final int _cursorIndexOfBudgetImpact = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetImpact");
          final List<SpendingReflection> _result = new ArrayList<SpendingReflection>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingReflection _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp_2);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_4 == null ? null : _tmp_4 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _item = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingReflection>> getReflectionsByEmotionalState(
      final String emotionalState) {
    final String _sql = "SELECT * FROM spending_reflections WHERE emotionalState = ? ORDER BY reflectionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (emotionalState == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, emotionalState);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_reflections"}, new Callable<List<SpendingReflection>>() {
      @Override
      @NonNull
      public List<SpendingReflection> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfExpenseId = CursorUtil.getColumnIndexOrThrow(_cursor, "expenseId");
          final int _cursorIndexOfWishlistItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "wishlistItemId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItemDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "itemDescription");
          final int _cursorIndexOfReflectionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionDate");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfTriggerReason = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerReason");
          final int _cursorIndexOfNeedVsWant = CursorUtil.getColumnIndexOrThrow(_cursor, "needVsWant");
          final int _cursorIndexOfSatisfactionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "satisfactionLevel");
          final int _cursorIndexOfRegretLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "regretLevel");
          final int _cursorIndexOfWouldBuyAgain = CursorUtil.getColumnIndexOrThrow(_cursor, "wouldBuyAgain");
          final int _cursorIndexOfAlternativeConsidered = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeConsidered");
          final int _cursorIndexOfDelayHelpful = CursorUtil.getColumnIndexOrThrow(_cursor, "delayHelpful");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfMindfulnessScore = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulnessScore");
          final int _cursorIndexOfBudgetImpact = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetImpact");
          final List<SpendingReflection> _result = new ArrayList<SpendingReflection>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingReflection _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _item = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingReflection>> getHighRegretReflections(final int minRegretLevel) {
    final String _sql = "SELECT * FROM spending_reflections WHERE regretLevel >= ? ORDER BY regretLevel DESC, reflectionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, minRegretLevel);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_reflections"}, new Callable<List<SpendingReflection>>() {
      @Override
      @NonNull
      public List<SpendingReflection> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfExpenseId = CursorUtil.getColumnIndexOrThrow(_cursor, "expenseId");
          final int _cursorIndexOfWishlistItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "wishlistItemId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItemDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "itemDescription");
          final int _cursorIndexOfReflectionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionDate");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfTriggerReason = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerReason");
          final int _cursorIndexOfNeedVsWant = CursorUtil.getColumnIndexOrThrow(_cursor, "needVsWant");
          final int _cursorIndexOfSatisfactionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "satisfactionLevel");
          final int _cursorIndexOfRegretLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "regretLevel");
          final int _cursorIndexOfWouldBuyAgain = CursorUtil.getColumnIndexOrThrow(_cursor, "wouldBuyAgain");
          final int _cursorIndexOfAlternativeConsidered = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeConsidered");
          final int _cursorIndexOfDelayHelpful = CursorUtil.getColumnIndexOrThrow(_cursor, "delayHelpful");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfMindfulnessScore = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulnessScore");
          final int _cursorIndexOfBudgetImpact = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetImpact");
          final List<SpendingReflection> _result = new ArrayList<SpendingReflection>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingReflection _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _item = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingReflection>> getHighSatisfactionReflections(
      final int minSatisfactionLevel) {
    final String _sql = "SELECT * FROM spending_reflections WHERE satisfactionLevel >= ? ORDER BY satisfactionLevel DESC, reflectionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, minSatisfactionLevel);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_reflections"}, new Callable<List<SpendingReflection>>() {
      @Override
      @NonNull
      public List<SpendingReflection> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfExpenseId = CursorUtil.getColumnIndexOrThrow(_cursor, "expenseId");
          final int _cursorIndexOfWishlistItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "wishlistItemId");
          final int _cursorIndexOfAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "amount");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfItemDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "itemDescription");
          final int _cursorIndexOfReflectionDate = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionDate");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfTriggerReason = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerReason");
          final int _cursorIndexOfNeedVsWant = CursorUtil.getColumnIndexOrThrow(_cursor, "needVsWant");
          final int _cursorIndexOfSatisfactionLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "satisfactionLevel");
          final int _cursorIndexOfRegretLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "regretLevel");
          final int _cursorIndexOfWouldBuyAgain = CursorUtil.getColumnIndexOrThrow(_cursor, "wouldBuyAgain");
          final int _cursorIndexOfAlternativeConsidered = CursorUtil.getColumnIndexOrThrow(_cursor, "alternativeConsidered");
          final int _cursorIndexOfDelayHelpful = CursorUtil.getColumnIndexOrThrow(_cursor, "delayHelpful");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfMindfulnessScore = CursorUtil.getColumnIndexOrThrow(_cursor, "mindfulnessScore");
          final int _cursorIndexOfBudgetImpact = CursorUtil.getColumnIndexOrThrow(_cursor, "budgetImpact");
          final List<SpendingReflection> _result = new ArrayList<SpendingReflection>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingReflection _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _item = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingReflection>> getReflectionsWhereDelayHelped() {
    final String _sql = "SELECT `spending_reflections`.`id` AS `id`, `spending_reflections`.`expenseId` AS `expenseId`, `spending_reflections`.`wishlistItemId` AS `wishlistItemId`, `spending_reflections`.`amount` AS `amount`, `spending_reflections`.`category` AS `category`, `spending_reflections`.`itemDescription` AS `itemDescription`, `spending_reflections`.`reflectionDate` AS `reflectionDate`, `spending_reflections`.`emotionalState` AS `emotionalState`, `spending_reflections`.`triggerReason` AS `triggerReason`, `spending_reflections`.`needVsWant` AS `needVsWant`, `spending_reflections`.`satisfactionLevel` AS `satisfactionLevel`, `spending_reflections`.`regretLevel` AS `regretLevel`, `spending_reflections`.`wouldBuyAgain` AS `wouldBuyAgain`, `spending_reflections`.`alternativeConsidered` AS `alternativeConsidered`, `spending_reflections`.`delayHelpful` AS `delayHelpful`, `spending_reflections`.`notes` AS `notes`, `spending_reflections`.`mindfulnessScore` AS `mindfulnessScore`, `spending_reflections`.`budgetImpact` AS `budgetImpact` FROM spending_reflections WHERE delayHelpful = 1 ORDER BY reflectionDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_reflections"}, new Callable<List<SpendingReflection>>() {
      @Override
      @NonNull
      public List<SpendingReflection> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfExpenseId = 1;
          final int _cursorIndexOfWishlistItemId = 2;
          final int _cursorIndexOfAmount = 3;
          final int _cursorIndexOfCategory = 4;
          final int _cursorIndexOfItemDescription = 5;
          final int _cursorIndexOfReflectionDate = 6;
          final int _cursorIndexOfEmotionalState = 7;
          final int _cursorIndexOfTriggerReason = 8;
          final int _cursorIndexOfNeedVsWant = 9;
          final int _cursorIndexOfSatisfactionLevel = 10;
          final int _cursorIndexOfRegretLevel = 11;
          final int _cursorIndexOfWouldBuyAgain = 12;
          final int _cursorIndexOfAlternativeConsidered = 13;
          final int _cursorIndexOfDelayHelpful = 14;
          final int _cursorIndexOfNotes = 15;
          final int _cursorIndexOfMindfulnessScore = 16;
          final int _cursorIndexOfBudgetImpact = 17;
          final List<SpendingReflection> _result = new ArrayList<SpendingReflection>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingReflection _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final Long _tmpExpenseId;
            if (_cursor.isNull(_cursorIndexOfExpenseId)) {
              _tmpExpenseId = null;
            } else {
              _tmpExpenseId = _cursor.getLong(_cursorIndexOfExpenseId);
            }
            final Long _tmpWishlistItemId;
            if (_cursor.isNull(_cursorIndexOfWishlistItemId)) {
              _tmpWishlistItemId = null;
            } else {
              _tmpWishlistItemId = _cursor.getLong(_cursorIndexOfWishlistItemId);
            }
            final double _tmpAmount;
            _tmpAmount = _cursor.getDouble(_cursorIndexOfAmount);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpItemDescription;
            if (_cursor.isNull(_cursorIndexOfItemDescription)) {
              _tmpItemDescription = null;
            } else {
              _tmpItemDescription = _cursor.getString(_cursorIndexOfItemDescription);
            }
            final LocalDateTime _tmpReflectionDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfReflectionDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfReflectionDate);
            }
            _tmpReflectionDate = __converters.toLocalDateTime(_tmp);
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final String _tmpTriggerReason;
            if (_cursor.isNull(_cursorIndexOfTriggerReason)) {
              _tmpTriggerReason = null;
            } else {
              _tmpTriggerReason = _cursor.getString(_cursorIndexOfTriggerReason);
            }
            final String _tmpNeedVsWant;
            if (_cursor.isNull(_cursorIndexOfNeedVsWant)) {
              _tmpNeedVsWant = null;
            } else {
              _tmpNeedVsWant = _cursor.getString(_cursorIndexOfNeedVsWant);
            }
            final Integer _tmpSatisfactionLevel;
            if (_cursor.isNull(_cursorIndexOfSatisfactionLevel)) {
              _tmpSatisfactionLevel = null;
            } else {
              _tmpSatisfactionLevel = _cursor.getInt(_cursorIndexOfSatisfactionLevel);
            }
            final Integer _tmpRegretLevel;
            if (_cursor.isNull(_cursorIndexOfRegretLevel)) {
              _tmpRegretLevel = null;
            } else {
              _tmpRegretLevel = _cursor.getInt(_cursorIndexOfRegretLevel);
            }
            final Boolean _tmpWouldBuyAgain;
            final Integer _tmp_1;
            if (_cursor.isNull(_cursorIndexOfWouldBuyAgain)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(_cursorIndexOfWouldBuyAgain);
            }
            _tmpWouldBuyAgain = _tmp_1 == null ? null : _tmp_1 != 0;
            final String _tmpAlternativeConsidered;
            if (_cursor.isNull(_cursorIndexOfAlternativeConsidered)) {
              _tmpAlternativeConsidered = null;
            } else {
              _tmpAlternativeConsidered = _cursor.getString(_cursorIndexOfAlternativeConsidered);
            }
            final Boolean _tmpDelayHelpful;
            final Integer _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayHelpful)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getInt(_cursorIndexOfDelayHelpful);
            }
            _tmpDelayHelpful = _tmp_2 == null ? null : _tmp_2 != 0;
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final Integer _tmpMindfulnessScore;
            if (_cursor.isNull(_cursorIndexOfMindfulnessScore)) {
              _tmpMindfulnessScore = null;
            } else {
              _tmpMindfulnessScore = _cursor.getInt(_cursorIndexOfMindfulnessScore);
            }
            final String _tmpBudgetImpact;
            if (_cursor.isNull(_cursorIndexOfBudgetImpact)) {
              _tmpBudgetImpact = null;
            } else {
              _tmpBudgetImpact = _cursor.getString(_cursorIndexOfBudgetImpact);
            }
            _item = new SpendingReflection(_tmpId,_tmpExpenseId,_tmpWishlistItemId,_tmpAmount,_tmpCategory,_tmpItemDescription,_tmpReflectionDate,_tmpEmotionalState,_tmpTriggerReason,_tmpNeedVsWant,_tmpSatisfactionLevel,_tmpRegretLevel,_tmpWouldBuyAgain,_tmpAlternativeConsidered,_tmpDelayHelpful,_tmpNotes,_tmpMindfulnessScore,_tmpBudgetImpact);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAverageSatisfactionLevel(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(satisfactionLevel) FROM spending_reflections WHERE satisfactionLevel IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageRegretLevel(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(regretLevel) FROM spending_reflections WHERE regretLevel IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageMindfulnessScore(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(mindfulnessScore) FROM spending_reflections WHERE mindfulnessScore IS NOT NULL";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDelayHelpfulCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM spending_reflections WHERE delayHelpful = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getWouldNotBuyAgainCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM spending_reflections WHERE wouldBuyAgain = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
