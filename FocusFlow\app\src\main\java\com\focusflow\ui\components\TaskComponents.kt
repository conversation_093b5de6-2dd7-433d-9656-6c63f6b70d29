package com.focusflow.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.focusflow.data.model.Task
import com.focusflow.ui.viewmodel.TaskFilter
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

@Composable
fun TaskItem(
    task: Task,
    onCompleteClick: () -> Unit,
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit,
    onBreakdownClick: () -> Unit,
    isProcrastinated: Boolean = false,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onEditClick() },
        elevation = if (isProcrastinated) 6.dp else 2.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = when {
            task.isCompleted -> Color(0xFFE8F5E8)
            isProcrastinated -> Color(0xFFFFEBEE)
            task.priority == "high" -> Color(0xFFFFF3E0)
            else -> MaterialTheme.colors.surface
        }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Priority indicator
                        Text(
                            text = when (task.priority) {
                                "high" -> "🔴"
                                "medium" -> "🟡"
                                "low" -> "🟢"
                                else -> "⚪"
                            },
                            style = MaterialTheme.typography.body2
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = task.title,
                            style = MaterialTheme.typography.h6,
                            fontWeight = FontWeight.Medium,
                            textDecoration = if (task.isCompleted) TextDecoration.LineThrough else null,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    if (!task.description.isNullOrBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = task.description,
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Task metadata
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // Duration
                        task.estimatedDuration?.let { duration ->
                            TaskMetadataChip(
                                icon = Icons.Default.Schedule,
                                text = "${duration}m",
                                color = Color(0xFF2196F3)
                            )
                        }
                        
                        // Category
                        task.category?.let { category ->
                            TaskMetadataChip(
                                icon = Icons.Default.Category,
                                text = category,
                                color = Color(0xFF9C27B0)
                            )
                        }
                        
                        // Due date
                        task.dueDate?.let { dueDate ->
                            val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                            val isOverdue = dueDate < now && !task.isCompleted
                            TaskMetadataChip(
                                icon = Icons.Default.Event,
                                text = formatDueDate(dueDate),
                                color = if (isOverdue) Color(0xFFF44336) else Color(0xFF4CAF50)
                            )
                        }
                    }
                    
                    // Procrastination warning
                    if (isProcrastinated && !task.isCompleted) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.Warning,
                                contentDescription = null,
                                tint = Color(0xFFFF9800),
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "This task might benefit from breaking down",
                                style = MaterialTheme.typography.caption,
                                color = Color(0xFFFF9800)
                            )
                        }
                    }
                }
                
                // Action buttons
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    if (!task.isCompleted) {
                        IconButton(onClick = onCompleteClick) {
                            Icon(
                                Icons.Default.CheckCircle,
                                contentDescription = "Complete",
                                tint = Color(0xFF4CAF50)
                            )
                        }
                        
                        if (isProcrastinated || (task.estimatedDuration ?: 0) > 30) {
                            IconButton(onClick = onBreakdownClick) {
                                Icon(
                                    Icons.Default.CallSplit,
                                    contentDescription = "Break Down",
                                    tint = Color(0xFF2196F3)
                                )
                            }
                        }
                    }
                    
                    IconButton(onClick = onDeleteClick) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = Color(0xFFF44336)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TaskMetadataChip(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    color: Color
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .background(
                color.copy(alpha = 0.1f),
                RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Icon(
            icon,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(12.dp)
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.caption,
            color = color
        )
    }
}

@Composable
fun TaskFilterChips(
    selectedFilter: TaskFilter,
    onFilterSelected: (TaskFilter) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(TaskFilter.values()) { filter ->
            FilterChip(
                selected = selectedFilter == filter,
                onClick = { onFilterSelected(filter) },
                modifier = Modifier.height(32.dp)
            ) {
                Text(
                    text = when (filter) {
                        TaskFilter.ALL -> "All"
                        TaskFilter.INCOMPLETE -> "Active"
                        TaskFilter.COMPLETED -> "Done"
                        TaskFilter.TODAY -> "Today"
                        TaskFilter.OVERDUE -> "Overdue"
                        TaskFilter.HIGH_PRIORITY -> "🔴 High"
                        TaskFilter.MEDIUM_PRIORITY -> "🟡 Medium"
                        TaskFilter.LOW_PRIORITY -> "🟢 Low"
                    },
                    style = MaterialTheme.typography.caption
                )
            }
        }
    }
}

@Composable
fun EmptyTasksState(
    filter: TaskFilter,
    onAddTaskClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "📝",
            style = MaterialTheme.typography.h2
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = when (filter) {
                TaskFilter.ALL -> "No tasks yet"
                TaskFilter.INCOMPLETE -> "All caught up!"
                TaskFilter.COMPLETED -> "No completed tasks"
                TaskFilter.TODAY -> "Nothing due today"
                TaskFilter.OVERDUE -> "No overdue tasks"
                else -> "No tasks in this category"
            },
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = when (filter) {
                TaskFilter.ALL -> "Create your first task to get started"
                TaskFilter.INCOMPLETE -> "Great job staying on top of things!"
                TaskFilter.COMPLETED -> "Complete some tasks to see them here"
                TaskFilter.TODAY -> "Enjoy your free time!"
                TaskFilter.OVERDUE -> "You're all caught up!"
                else -> "Tasks matching this filter will appear here"
            },
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
        
        if (filter == TaskFilter.ALL || filter == TaskFilter.INCOMPLETE) {
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = onAddTaskClick,
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.primary
                )
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Add Your First Task")
            }
        }
    }
}

@Composable
fun AddTaskDialog(
    onDismiss: () -> Unit,
    onAddTask: (String, String?, LocalDateTime?, String, String?, Int?) -> Unit,
    availableCategories: List<String>
) {
    var title by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var selectedPriority by remember { mutableStateOf("medium") }
    var selectedCategory by remember { mutableStateOf("") }
    var estimatedDuration by remember { mutableStateOf("") }
    var showDatePicker by remember { mutableStateOf(false) }
    var selectedDate by remember { mutableStateOf<LocalDateTime?>(null) }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "✨ Add New Task",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Title
                OutlinedTextField(
                    value = title,
                    onValueChange = { title = it },
                    label = { Text("Task Title") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Description
                OutlinedTextField(
                    value = description,
                    onValueChange = { description = it },
                    label = { Text("Description (optional)") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Priority selection
                Text(
                    text = "Priority",
                    style = MaterialTheme.typography.subtitle2,
                    fontWeight = FontWeight.Medium
                )
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("high" to "🔴 Urgent", "medium" to "🟡 Important", "low" to "🟢 Later").forEach { (priority, label) ->
                        FilterChip(
                            selected = selectedPriority == priority,
                            onClick = { selectedPriority = priority }
                        ) {
                            Text(label, style = MaterialTheme.typography.caption)
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // Duration
                OutlinedTextField(
                    value = estimatedDuration,
                    onValueChange = { estimatedDuration = it },
                    label = { Text("Duration (minutes)") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Button(
                        onClick = {
                            if (title.isNotBlank()) {
                                onAddTask(
                                    title,
                                    description.takeIf { it.isNotBlank() },
                                    selectedDate,
                                    selectedPriority,
                                    selectedCategory.takeIf { it.isNotBlank() },
                                    estimatedDuration.toIntOrNull()
                                )
                            }
                        },
                        enabled = title.isNotBlank()
                    ) {
                        Text("Add Task")
                    }
                }
            }
        }
    }
}

@Composable
fun TaskBreakdownDialog(
    task: Task,
    suggestedSubtasks: List<String>,
    isLoading: Boolean,
    onAccept: () -> Unit,
    onReject: () -> Unit,
    onDismiss: () -> Unit
) {
    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Text(
                    text = "🧩 Break Down Task",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "\"${task.title}\"",
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colors.primary
                )

                Spacer(modifier = Modifier.height(16.dp))

                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxWidth(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator()
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "AI is breaking down your task...",
                                style = MaterialTheme.typography.body2,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                            )
                        }
                    }
                } else if (suggestedSubtasks.isNotEmpty()) {
                    Text(
                        text = "Suggested subtasks:",
                        style = MaterialTheme.typography.subtitle2,
                        fontWeight = FontWeight.Medium
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    suggestedSubtasks.forEachIndexed { index, subtask ->
                        Row(
                            modifier = Modifier.padding(vertical = 4.dp)
                        ) {
                            Text(
                                text = "${index + 1}.",
                                style = MaterialTheme.typography.body2,
                                color = MaterialTheme.colors.primary
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = subtask,
                                style = MaterialTheme.typography.body2
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(onClick = onReject) {
                            Text("No Thanks")
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        Button(onClick = onAccept) {
                            Text("Create Subtasks")
                        }
                    }
                }
            }
        }
    }
}

private fun formatDueDate(dueDate: LocalDateTime): String {
    val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
    val today = now.date
    val dueDay = dueDate.date

    return when {
        dueDay == today -> "Today"
        dueDay == today.plus(1, kotlinx.datetime.DateTimeUnit.DAY) -> "Tomorrow"
        dueDay == today.minus(1, kotlinx.datetime.DateTimeUnit.DAY) -> "Yesterday"
        else -> "${dueDay.month.name.take(3)} ${dueDay.dayOfMonth}"
    }
}
