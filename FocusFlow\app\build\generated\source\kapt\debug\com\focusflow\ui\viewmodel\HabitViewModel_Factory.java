package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.HabitRepository;
import com.focusflow.service.GamificationService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitViewModel_Factory implements Factory<HabitViewModel> {
  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<GamificationService> gamificationServiceProvider;

  public HabitViewModel_Factory(Provider<HabitRepository> habitRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.gamificationServiceProvider = gamificationServiceProvider;
  }

  @Override
  public HabitViewModel get() {
    return newInstance(habitRepositoryProvider.get(), gamificationServiceProvider.get());
  }

  public static HabitViewModel_Factory create(Provider<HabitRepository> habitRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider) {
    return new HabitViewModel_Factory(habitRepositoryProvider, gamificationServiceProvider);
  }

  public static HabitViewModel newInstance(HabitRepository habitRepository,
      GamificationService gamificationService) {
    return new HabitViewModel(habitRepository, gamificationService);
  }
}
