package com.focusflow.service;

import com.focusflow.data.repository.WishlistRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PurchaseDelayService_Factory implements Factory<PurchaseDelayService> {
  private final Provider<WishlistRepository> wishlistRepositoryProvider;

  private final Provider<NotificationService> notificationServiceProvider;

  public PurchaseDelayService_Factory(Provider<WishlistRepository> wishlistRepositoryProvider,
      Provider<NotificationService> notificationServiceProvider) {
    this.wishlistRepositoryProvider = wishlistRepositoryProvider;
    this.notificationServiceProvider = notificationServiceProvider;
  }

  @Override
  public PurchaseDelayService get() {
    return newInstance(wishlistRepositoryProvider.get(), notificationServiceProvider.get());
  }

  public static PurchaseDelayService_Factory create(
      Provider<WishlistRepository> wishlistRepositoryProvider,
      Provider<NotificationService> notificationServiceProvider) {
    return new PurchaseDelayService_Factory(wishlistRepositoryProvider, notificationServiceProvider);
  }

  public static PurchaseDelayService newInstance(WishlistRepository wishlistRepository,
      NotificationService notificationService) {
    return new PurchaseDelayService(wishlistRepository, notificationService);
  }
}
