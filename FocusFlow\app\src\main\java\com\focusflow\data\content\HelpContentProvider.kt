package com.focusflow.data.content

import com.focusflow.data.model.*
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * Comprehensive help content for FocusFlow
 * All content is ADHD-friendly with clear structure and actionable guidance
 */
object HelpContentProvider {
    
    fun getDefaultArticles(): List<HelpArticle> {
        return listOf(
            // Getting Started Articles
            createGettingStartedArticle(),
            createSafeToSpendGuide(),
            createFirstBudgetGuide(),
            
            // Budgeting Articles
            createEnvelopeBudgetingGuide(),
            createBudgetOptimizationGuide(),
            createZeroBasedBudgetingGuide(),
            
            // Spending Articles
            createExpenseTrackingGuide(),
            createImpulseControlGuide(),
            createSpendingAnalysisGuide(),
            
            // Debt Management Articles
            createDebtPayoffStrategiesGuide(),
            createCreditCardManagementGuide(),
            createDebtProgressTrackingGuide(),
            
            // Focus & Productivity Articles
            createTaskManagementGuide(),
            createFocusModeGuide(),
            createProcrastinationHelpGuide(),
            
            // Habits & Gamification Articles
            createHabitTrackingGuide(),
            createVirtualPetGuide(),
            createAchievementSystemGuide(),
            
            // Privacy & Security Articles
            createPrivacyGuide(),
            createSecurityFeaturesGuide(),
            createDataControlGuide(),
            
            // Troubleshooting Articles
            createCommonIssuesGuide(),
            createPerformanceGuide(),
            createAccessibilityGuide()
        )
    }
    
    private fun createGettingStartedArticle(): HelpArticle {
        return HelpArticle(
            id = "getting_started_welcome",
            categoryId = "getting_started",
            title = "Welcome to FocusFlow",
            subtitle = "Your ADHD-friendly personal finance companion",
            content = """
# Welcome to FocusFlow! 🎉

FocusFlow is designed specifically for ADHD minds to make personal finance manageable, engaging, and stress-free.

## What Makes FocusFlow Different?

### 🧠 **ADHD-Optimized Design**
- **Visual hierarchy** that guides your attention naturally
- **Reduced cognitive load** with clean, uncluttered interfaces
- **Immediate feedback** to keep you engaged
- **Positive reinforcement** instead of shame-based messaging

### 💰 **Smart Financial Tools**
- **Safe-to-Spend Calculator** - Know exactly what you can spend
- **Visual Envelope Budgeting** - See your money at a glance
- **Impulse Control Tools** - Pause and reflect before spending
- **Debt Payoff Planner** - Clear path to financial freedom

### 🎯 **Focus & Productivity Features**
- **Task Breakdown** - Complex financial tasks made simple
- **Focus Mode** - Distraction-free financial planning
- **Habit Tracking** - Build healthy money habits
- **Gamification** - Your virtual pet thrives when you do!

## Getting Started in 5 Minutes

### Step 1: Set Up Your Safe-to-Spend
The Safe-to-Spend calculator is your financial north star. It shows exactly how much you can spend without stress.

1. Tap the **Safe-to-Spend** widget on your dashboard
2. Enter your monthly income
3. Add your fixed expenses (rent, utilities, minimum debt payments)
4. Set aside money for savings goals
5. The remaining amount is safe to spend!

### Step 2: Create Your First Budget
Our envelope budgeting system makes budgeting visual and intuitive.

1. Go to the **Budget** tab
2. Tap **Create New Budget**
3. Choose from preset categories or create your own
4. Allocate money to each "envelope"
5. Watch your progress in real-time!

### Step 3: Start Tracking Expenses
Quick expense entry keeps you on track without the hassle.

1. Tap the **+** button anywhere in the app
2. Enter the amount and select a category
3. Add a quick description (optional)
4. Tap **Save** - done in seconds!

### Step 4: Meet Your Virtual Pet
Your financial companion that grows as you build healthy habits.

1. Visit the **Pet** section
2. Choose your pet's appearance
3. Complete financial tasks to keep them happy
4. Unlock new accessories and features!

## ADHD-Specific Tips for Success

### 🎯 **Start Small**
- Focus on one feature at a time
- Don't try to set up everything at once
- Celebrate small wins along the way

### 🔄 **Build Routines**
- Check your Safe-to-Spend daily
- Log expenses right after spending
- Review your budget weekly, not daily

### 🎨 **Use Visual Cues**
- Customize category colors that make sense to you
- Use the progress bars to see your success
- Let your virtual pet remind you of your goals

### ⚡ **Reduce Friction**
- Use quick-add buttons for common expenses
- Set up automatic categorization
- Enable notifications for important reminders

## Need Help?

- **Search** this help system for specific questions
- **Browse categories** for comprehensive guides
- **Contact support** if you're stuck
- **Join our community** for peer support

Remember: Personal finance is a journey, not a destination. FocusFlow is here to support you every step of the way! 🌟
            """.trimIndent(),
            contentType = HelpContentType.QUICK_START,
            difficulty = DifficultyLevel.BEGINNER,
            estimatedReadTime = 5,
            order = 1,
            tags = listOf("welcome", "getting started", "overview", "adhd", "basics"),
            isFeatured = true,
            lastUpdated = getCurrentTimestamp(),
            adhdFriendlyTips = listOf(
                "Take breaks while reading - this guide will still be here when you return",
                "Don't feel pressured to complete everything at once",
                "Bookmark sections you want to revisit later",
                "Start with just the Safe-to-Spend calculator if you're feeling overwhelmed"
            ),
            troubleshootingSteps = listOf(
                TroubleshootingStep(
                    problem = "I feel overwhelmed by all the features",
                    solution = "Start with just the Safe-to-Spend calculator. Master one feature before moving to the next.",
                    isCommon = true,
                    adhdSpecific = true
                ),
                TroubleshootingStep(
                    problem = "I can't find where to start",
                    solution = "Follow the 4-step getting started guide above. Each step builds on the previous one.",
                    isCommon = true
                )
            ),
            marketingHighlights = listOf(
                "ADHD-optimized design reduces cognitive load",
                "Visual envelope budgeting makes money management intuitive",
                "Gamification with virtual pet increases engagement",
                "5-minute setup gets you started immediately"
            )
        )
    }
    
    private fun createSafeToSpendGuide(): HelpArticle {
        return HelpArticle(
            id = "safe_to_spend_guide",
            categoryId = "getting_started",
            title = "Master the Safe-to-Spend Calculator",
            subtitle = "Your anxiety-free spending guide",
            content = """
# Safe-to-Spend Calculator: Your Financial North Star 🧭

The Safe-to-Spend calculator is FocusFlow's signature feature. It eliminates the guesswork and anxiety around spending decisions by showing you exactly how much money you can spend without compromising your financial goals.

## Why Safe-to-Spend Works for ADHD

### 🧠 **Reduces Decision Fatigue**
Instead of complex mental math every time you want to buy something, you have one clear number to reference.

### ⚡ **Instant Clarity**
No more "Can I afford this?" anxiety. The answer is right there on your dashboard.

### 🎯 **Prevents Overspending**
By accounting for all your obligations first, you can spend the remaining amount guilt-free.

## How It Works

### The Formula
**Safe-to-Spend = Income - Fixed Expenses - Savings Goals - Debt Payments**

### Step-by-Step Setup

#### 1. Add Your Income
- Include all regular income sources
- Use your take-home (after-tax) amount
- If income varies, use a conservative estimate

#### 2. List Fixed Expenses
- Rent/mortgage
- Utilities
- Insurance
- Phone bill
- Subscriptions
- Minimum debt payments

#### 3. Set Savings Goals
- Emergency fund contributions
- Retirement savings
- Specific goals (vacation, car, etc.)
- Start small - even $25/month helps!

#### 4. Account for Debt Payments
- Minimum payments (already included in fixed expenses)
- Extra payments toward debt
- Credit card payments above minimums

### Your Safe-to-Spend Number
The remaining amount is yours to spend on variable expenses like:
- Groceries
- Entertainment
- Dining out
- Shopping
- Hobbies

## ADHD-Specific Tips

### 🎨 **Visual Cues**
- **Green**: You're well within your safe spending limit
- **Yellow**: Getting close to your limit - time to slow down
- **Red**: You've exceeded your safe amount - pause and reassess

### 📱 **Quick Access**
- The Safe-to-Spend widget is prominently displayed on your dashboard
- Check it before any non-essential purchase
- Use it as your spending "permission slip"

### 🔄 **Regular Updates**
- Update when your income changes
- Adjust when you pay off debt (more money to spend!)
- Revise savings goals as needed

### ⚡ **Impulse Control Integration**
- The app will show your Safe-to-Spend amount during purchase confirmations
- Use the "cooling off" period to check if a purchase fits your budget
- Add items to your wishlist instead of buying immediately

## Common Scenarios

### "I Got Paid - Can I Spend More?"
Your Safe-to-Spend automatically updates when you log income. Any increase in your safe amount is yours to enjoy!

### "I Have an Unexpected Expense"
1. Check if it's truly necessary
2. See if you can reduce spending in other categories
3. Consider if it's worth adjusting your savings goal temporarily
4. Update your Safe-to-Spend calculation if needed

### "My Safe-to-Spend is Negative"
This means your expenses exceed your income. Don't panic! This is valuable information:
1. Review your fixed expenses - can any be reduced?
2. Look for ways to increase income
3. Temporarily reduce savings goals if necessary
4. Consider debt consolidation options

## Advanced Features

### 🎯 **Goal Integration**
Link your Safe-to-Spend to specific financial goals. The app will automatically adjust your safe amount based on your goal progress.

### 📊 **Spending Patterns**
Track how your actual spending compares to your safe amount over time. This helps you refine your budget and understand your habits.

### 🤖 **AI Insights**
Get personalized suggestions for optimizing your Safe-to-Spend calculation based on your spending patterns and financial goals.

## Troubleshooting

### "My Safe-to-Spend Changes Too Often"
- Use average amounts for variable income
- Set up automatic updates for regular expenses
- Review and adjust monthly, not daily

### "I Don't Trust the Number"
- Start with a more conservative calculation
- Track your actual spending for a few weeks
- Gradually increase confidence as you see the system work

### "It's Too Restrictive"
- Review if your savings goals are too aggressive
- Check if you've included expenses that aren't truly fixed
- Remember: this is about sustainable spending, not deprivation

Remember: The Safe-to-Spend calculator is a tool to reduce anxiety and increase confidence in your financial decisions. Trust the process, and adjust as you learn more about your spending patterns! 💪
            """.trimIndent(),
            contentType = HelpContentType.HOW_TO_GUIDE,
            difficulty = DifficultyLevel.BEGINNER,
            estimatedReadTime = 8,
            order = 2,
            tags = listOf("safe-to-spend", "budgeting", "anxiety", "spending", "calculator"),
            isFeatured = true,
            lastUpdated = getCurrentTimestamp(),
            adhdFriendlyTips = listOf(
                "Start with round numbers to make calculations easier",
                "Use the visual color coding instead of memorizing exact amounts",
                "Set up automatic updates to reduce maintenance burden",
                "Trust the system - it's designed to give you permission to spend, not restrict you"
            ),
            troubleshootingSteps = listOf(
                TroubleshootingStep(
                    problem = "My safe-to-spend amount seems too low",
                    solution = "Review your fixed expenses and savings goals. You might be too conservative. Start with smaller savings goals and increase over time.",
                    isCommon = true,
                    adhdSpecific = true
                ),
                TroubleshootingStep(
                    problem = "I keep forgetting to check before spending",
                    solution = "Enable spending confirmation dialogs in settings. This will automatically show your safe-to-spend amount before purchases.",
                    isCommon = true,
                    adhdSpecific = true
                )
            ),
            marketingHighlights = listOf(
                "Eliminates spending anxiety with one clear number",
                "Automatically updates based on your financial situation",
                "Visual color coding for instant decision making",
                "Integrates with impulse control tools for ADHD support"
            )
        )
    }
    
    private fun getCurrentTimestamp(): String {
        return Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).toString()
    }
    
    // Additional article creation methods would continue here...
    private fun createFirstBudgetGuide(): HelpArticle = TODO()
    private fun createEnvelopeBudgetingGuide(): HelpArticle = TODO()
    private fun createBudgetOptimizationGuide(): HelpArticle = TODO()
    private fun createZeroBasedBudgetingGuide(): HelpArticle = TODO()
    private fun createExpenseTrackingGuide(): HelpArticle = TODO()
    private fun createImpulseControlGuide(): HelpArticle {
        return HelpArticle(
            id = "impulse_control_guide",
            categoryId = "spending",
            title = "Master Your Impulse Spending",
            subtitle = "ADHD-friendly tools to pause, reflect, and make mindful purchases",
            content = """
# Impulse Control Tools: Your Spending Superpower 🛡️

ADHD brains are wired for immediate gratification, making impulse spending a common challenge. FocusFlow's impulse control tools work WITH your ADHD brain to help you make thoughtful spending decisions.

## Understanding ADHD and Impulse Spending

### Why ADHD Brains Struggle with Impulse Control
- **Dopamine seeking**: Shopping provides instant gratification
- **Executive function challenges**: Difficulty weighing long-term consequences
- **Emotional regulation**: Spending as a coping mechanism
- **Hyperfocus**: Getting "locked in" on wanting something

### The FocusFlow Approach
Instead of fighting your ADHD traits, we work with them:
- **Gentle pauses** instead of harsh restrictions
- **Reflection questions** to engage your thinking brain
- **Visual impact previews** to see consequences clearly
- **Alternative actions** to satisfy the impulse differently

## Core Impulse Control Features

### 🛑 **Spending Confirmation Dialogs**
When you're about to make a purchase, FocusFlow gently intervenes:

**What Happens:**
1. You enter a purchase amount
2. A friendly dialog appears with reflection questions
3. You see the impact on your budget visually
4. You choose to proceed, wait, or save for later

**Sample Questions:**
- "Is this purchase aligned with your current financial goals?"
- "Will you still want this item in 24 hours?"
- "Is there a less expensive alternative that meets your needs?"
- "How will this purchase affect your Safe-to-Spend amount?"

### ⏰ **Cooling-Off Periods**
Built-in delays that give your rational brain time to catch up:

**How It Works:**
- Purchases over your set threshold trigger a 10-second countdown
- During this time, you see your budget impact
- You can choose to proceed or add the item to your wishlist
- The delay increases for larger purchases

**ADHD Benefits:**
- Interrupts the impulse-to-action pathway
- Provides time for executive function to engage
- Reduces regret from hasty decisions

### 📝 **Wishlist Feature**
Transform impulse purchases into future planning:

**Instead of buying immediately:**
1. Add the item to your wishlist
2. Set a "reconsider date" (24 hours, 1 week, 1 month)
3. Get a gentle reminder to revisit the item
4. Often, you'll find the desire has passed!

**Wishlist Benefits:**
- Satisfies the immediate urge to "capture" the item
- Allows time for rational evaluation
- Helps distinguish between wants and needs
- Creates a gift list for others

### 💡 **Budget Impact Preview**
See exactly how a purchase affects your finances:

**Visual Indicators:**
- **Green**: Purchase fits comfortably in your budget
- **Yellow**: Purchase is possible but will limit other spending
- **Red**: Purchase exceeds your Safe-to-Spend amount

**Information Shown:**
- Remaining Safe-to-Spend after purchase
- Days until next income
- Impact on savings goals
- Alternative spending suggestions

## Setting Up Impulse Control

### 1. Configure Your Thresholds
**Recommended Starting Points:**
- **Small purchases**: $25 - Quick confirmation dialog
- **Medium purchases**: $50 - 10-second cooling-off period
- **Large purchases**: $100+ - Extended reflection questions

**Customization Tips:**
- Start with lower thresholds and adjust up
- Consider your typical impulse purchase amounts
- Different thresholds for different categories

### 2. Choose Your Reflection Questions
**For ADHD Brains, Effective Questions:**
- Focus on immediate, concrete consequences
- Use visual language ("picture yourself...")
- Connect to your specific goals
- Avoid shame-based language

### 3. Set Up Your Wishlist Categories
**Organize by:**
- **Immediate** (reconsider in 24 hours)
- **Short-term** (reconsider in 1 week)
- **Long-term** (reconsider in 1 month)
- **Someday/Maybe** (no specific timeline)

## ADHD-Specific Strategies

### 🎯 **The STOP Technique**
When you feel an impulse to buy:
- **S**top what you're doing
- **T**ake a deep breath
- **O**bserve your feelings and motivations
- **P**roceed mindfully with FocusFlow's tools

### 🔄 **Replacement Behaviors**
Instead of shopping, try:
- Adding items to your wishlist
- Taking a photo of the item to "capture" it
- Calling a friend or family member
- Going for a walk or doing jumping jacks
- Checking your financial goals for motivation

### 🎨 **Environmental Design**
- Remove shopping apps from your phone's home screen
- Enable FocusFlow's purchase confirmations
- Set up automatic savings transfers
- Create visual reminders of your goals

### ⚡ **Energy Management**
Impulse control is harder when you're:
- Tired or stressed
- Hungry (low blood sugar affects decision-making)
- Emotionally dysregulated
- In hyperfocus mode

**Prevention Strategies:**
- Shop when you're well-rested and fed
- Use FocusFlow's mood tracking to identify patterns
- Set spending limits during vulnerable times
- Have a support person you can text before big purchases

## Advanced Techniques

### 🤖 **AI-Powered Insights**
FocusFlow's AI learns your patterns and provides personalized suggestions:
- Identifies your common impulse triggers
- Suggests optimal cooling-off periods for you
- Recommends alternative activities
- Celebrates your successful impulse control

### 📊 **Pattern Recognition**
Track your impulse control success:
- See how often you use the cooling-off period
- Monitor wishlist items you never purchased
- Celebrate money saved through mindful decisions
- Identify your strongest impulse control times

### 🎮 **Gamification Elements**
- Earn points for using impulse control tools
- Unlock achievements for consecutive days without impulse purchases
- Watch your virtual pet thrive when you make mindful decisions
- Compete with friends (optional) for impulse control streaks

## Troubleshooting Common Challenges

### "I Keep Overriding the Controls"
- Lower your thresholds temporarily
- Add more reflection questions
- Increase cooling-off periods
- Consider having a trusted person hold your cards during vulnerable times

### "The Delays Are Annoying"
- Remember: the annoyance is the point - it's breaking the impulse pattern
- Start with shorter delays and gradually increase
- Focus on the money you're saving, not the inconvenience
- Celebrate each time you successfully use the tools

### "I Feel Restricted"
- Reframe: these tools give you MORE freedom by preventing regret
- Focus on your goals and what you're working toward
- Remember: you can still buy things, just more mindfully
- Use the wishlist to maintain a sense of possibility

Remember: Impulse control is a skill that improves with practice. Be patient with yourself as you build these new habits. Every time you pause and reflect, you're strengthening your financial decision-making muscles! 💪
            """.trimIndent(),
            contentType = HelpContentType.HOW_TO_GUIDE,
            difficulty = DifficultyLevel.INTERMEDIATE,
            estimatedReadTime = 12,
            order = 3,
            tags = listOf("impulse control", "adhd", "spending", "mindful", "tools"),
            isFeatured = true,
            lastUpdated = getCurrentTimestamp(),
            adhdFriendlyTips = listOf(
                "Start with small thresholds and work your way up",
                "Use the wishlist feature to satisfy the immediate urge to 'capture' items",
                "Practice the STOP technique during low-stakes situations first",
                "Remember that impulse control gets easier with practice"
            ),
            troubleshootingSteps = listOf(
                TroubleshootingStep(
                    problem = "I keep bypassing the impulse control features",
                    solution = "Lower your thresholds and add more reflection questions. Consider the 'why' behind your bypassing - are you stressed, tired, or emotionally dysregulated?",
                    isCommon = true,
                    adhdSpecific = true
                ),
                TroubleshootingStep(
                    problem = "The cooling-off periods feel too long",
                    solution = "Start with 5-second delays and gradually increase. Remember, the slight annoyance is helping break the impulse pattern.",
                    isCommon = true,
                    adhdSpecific = true
                )
            ),
            marketingHighlights = listOf(
                "ADHD-specific impulse control tools that work with your brain",
                "Gentle pauses and reflection questions instead of harsh restrictions",
                "Wishlist feature transforms impulses into future planning",
                "Visual budget impact preview for immediate consequence awareness"
            )
        )
    }
    private fun createSpendingAnalysisGuide(): HelpArticle = TODO()
    private fun createDebtPayoffStrategiesGuide(): HelpArticle = TODO()
    private fun createCreditCardManagementGuide(): HelpArticle = TODO()
    private fun createDebtProgressTrackingGuide(): HelpArticle = TODO()
    private fun createTaskManagementGuide(): HelpArticle = TODO()
    private fun createFocusModeGuide(): HelpArticle = TODO()
    private fun createProcrastinationHelpGuide(): HelpArticle = TODO()
    private fun createHabitTrackingGuide(): HelpArticle = TODO()
    private fun createVirtualPetGuide(): HelpArticle = TODO()
    private fun createAchievementSystemGuide(): HelpArticle = TODO()
    private fun createPrivacyGuide(): HelpArticle = TODO()
    private fun createSecurityFeaturesGuide(): HelpArticle = TODO()
    private fun createDataControlGuide(): HelpArticle = TODO()
    private fun createCommonIssuesGuide(): HelpArticle = TODO()
    private fun createPerformanceGuide(): HelpArticle = TODO()
    private fun createAccessibilityGuide(): HelpArticle = TODO()

    fun getDefaultTutorials(): List<HelpTutorial> {
        return listOf(
            createGettingStartedTutorial(),
            createSafeToSpendTutorial(),
            createBudgetSetupTutorial(),
            createExpenseTrackingTutorial(),
            createImpulseControlTutorial()
        )
    }

    private fun createGettingStartedTutorial(): HelpTutorial {
        return HelpTutorial(
            id = "tutorial_getting_started",
            articleId = "getting_started_welcome",
            title = "Welcome to FocusFlow",
            description = "A quick tour of your new ADHD-friendly finance companion",
            estimatedDuration = 5,
            isInteractive = true,
            steps = listOf(
                TutorialStep(
                    id = "step_welcome",
                    title = "Welcome to FocusFlow!",
                    description = "Let's take a quick tour of your new ADHD-friendly personal finance app.",
                    instruction = "Tap 'Next' to begin the tour",
                    adhdTip = "This tutorial is designed to be bite-sized and easy to follow. Take your time!"
                ),
                TutorialStep(
                    id = "step_dashboard",
                    title = "Your Dashboard",
                    description = "This is your financial command center, designed to show the most important information at a glance.",
                    instruction = "Notice the Safe-to-Spend widget at the top - this is your spending guide",
                    targetElement = "safe_to_spend_widget",
                    adhdTip = "The dashboard uses visual hierarchy to help your ADHD brain focus on what matters most"
                ),
                TutorialStep(
                    id = "step_navigation",
                    title = "Easy Navigation",
                    description = "The bottom navigation makes it easy to move between main features.",
                    instruction = "Look at the navigation bar at the bottom of the screen",
                    targetElement = "bottom_navigation",
                    adhdTip = "Icons and labels help you remember what each section does"
                ),
                TutorialStep(
                    id = "step_complete",
                    title = "You're Ready!",
                    description = "You've completed the basic tour. Ready to set up your first budget?",
                    instruction = "Tap 'Complete' to finish the tutorial",
                    adhdTip = "Remember: start small and build one habit at a time. You've got this!"
                )
            ),
            completionReward = "Welcome Badge"
        )
    }

    private fun createSafeToSpendTutorial(): HelpTutorial {
        return HelpTutorial(
            id = "tutorial_safe_to_spend",
            articleId = "safe_to_spend_guide",
            title = "Set Up Safe-to-Spend",
            description = "Learn to use your anxiety-free spending guide",
            estimatedDuration = 8,
            isInteractive = true,
            prerequisites = listOf("Complete Welcome Tutorial"),
            steps = listOf(
                TutorialStep(
                    id = "step_concept",
                    title = "What is Safe-to-Spend?",
                    description = "Safe-to-Spend shows exactly how much you can spend without compromising your financial goals.",
                    instruction = "Tap the Safe-to-Spend widget to open the calculator",
                    targetElement = "safe_to_spend_widget",
                    adhdTip = "This eliminates the mental math and decision fatigue that ADHD brains struggle with"
                ),
                TutorialStep(
                    id = "step_income",
                    title = "Add Your Income",
                    description = "Start by entering your monthly take-home income.",
                    instruction = "Enter your monthly income in the income field",
                    targetElement = "income_input",
                    adhdTip = "Use your take-home amount (after taxes) for accuracy"
                ),
                TutorialStep(
                    id = "step_result",
                    title = "Your Safe Amount",
                    description = "The remaining amount is safe to spend on variable expenses like groceries and entertainment.",
                    instruction = "Look at your calculated Safe-to-Spend amount",
                    targetElement = "safe_to_spend_result",
                    adhdTip = "This number gives you permission to spend guilt-free within your means"
                )
            ),
            completionReward = "Budget Master Badge"
        )
    }

    // Additional tutorial creation methods
    private fun createBudgetSetupTutorial(): HelpTutorial = TODO()
    private fun createExpenseTrackingTutorial(): HelpTutorial = TODO()
    private fun createImpulseControlTutorial(): HelpTutorial = TODO()
}
