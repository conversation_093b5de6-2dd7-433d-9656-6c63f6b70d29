package com.focusflow.data.model;

/**
 * Help system configuration
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0018\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BE\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\tH\u00c6\u0003J\t\u0010 \u001a\u00020\u000bH\u00c6\u0003J\t\u0010!\u001a\u00020\rH\u00c6\u0003JU\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010#\u001a\u00020\r2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014\u00a8\u0006("}, d2 = {"Lcom/focusflow/data/model/HelpSystemConfig;", "", "version", "", "lastContentUpdate", "supportedLanguages", "", "offlineContentVersion", "adhdOptimizations", "Lcom/focusflow/data/model/ADHDOptimizations;", "searchConfig", "Lcom/focusflow/data/model/SearchConfig;", "analyticsEnabled", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lcom/focusflow/data/model/ADHDOptimizations;Lcom/focusflow/data/model/SearchConfig;Z)V", "getAdhdOptimizations", "()Lcom/focusflow/data/model/ADHDOptimizations;", "getAnalyticsEnabled", "()Z", "getLastContentUpdate", "()Ljava/lang/String;", "getOfflineContentVersion", "getSearchConfig", "()Lcom/focusflow/data/model/SearchConfig;", "getSupportedLanguages", "()Ljava/util/List;", "getVersion", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class HelpSystemConfig {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String version = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String lastContentUpdate = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> supportedLanguages = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String offlineContentVersion = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.model.ADHDOptimizations adhdOptimizations = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.model.SearchConfig searchConfig = null;
    private final boolean analyticsEnabled = false;
    
    public HelpSystemConfig(@org.jetbrains.annotations.NotNull
    java.lang.String version, @org.jetbrains.annotations.NotNull
    java.lang.String lastContentUpdate, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> supportedLanguages, @org.jetbrains.annotations.NotNull
    java.lang.String offlineContentVersion, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.ADHDOptimizations adhdOptimizations, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SearchConfig searchConfig, boolean analyticsEnabled) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getVersion() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getLastContentUpdate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getSupportedLanguages() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getOfflineContentVersion() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.ADHDOptimizations getAdhdOptimizations() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.SearchConfig getSearchConfig() {
        return null;
    }
    
    public final boolean getAnalyticsEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.ADHDOptimizations component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.SearchConfig component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpSystemConfig copy(@org.jetbrains.annotations.NotNull
    java.lang.String version, @org.jetbrains.annotations.NotNull
    java.lang.String lastContentUpdate, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> supportedLanguages, @org.jetbrains.annotations.NotNull
    java.lang.String offlineContentVersion, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.ADHDOptimizations adhdOptimizations, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.SearchConfig searchConfig, boolean analyticsEnabled) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}