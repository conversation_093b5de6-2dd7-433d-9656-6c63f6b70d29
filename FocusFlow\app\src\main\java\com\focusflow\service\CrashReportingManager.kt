package com.focusflow.service

import android.content.Context
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Centralized crash reporting and error tracking service
 * Stub implementation for production readiness testing (Firebase removed)
 */
@Singleton
class CrashReportingManager @Inject constructor(
    @ApplicationContext private val context: Context
) {

    companion object {
        private const val TAG = "CrashReportingManager"
    }

    private val customKeys = mutableMapOf<String, Any>()

    init {
        initializeCrashlytics()
    }

    private fun initializeCrashlytics() {
        // Stub implementation - log to console for now
        Log.d(TAG, "CrashReportingManager initialized (stub implementation)")

        // Set custom keys for better debugging
        setCustomKey("app_version", getAppVersion())
        setCustomKey("build_type", if (isDebugBuild()) "debug" else "release")
    }
    
    /**
     * Set user identifier for crash reports (use anonymized ID for privacy)
     */
    fun setUserId(userId: String) {
        Log.d(TAG, "setUserId: $userId")
        customKeys["user_id"] = userId
    }

    /**
     * Set custom key-value pairs for crash context
     */
    fun setCustomKey(key: String, value: String) {
        Log.d(TAG, "setCustomKey: $key = $value")
        customKeys[key] = value
    }

    fun setCustomKey(key: String, value: Boolean) {
        Log.d(TAG, "setCustomKey: $key = $value")
        customKeys[key] = value
    }

    fun setCustomKey(key: String, value: Int) {
        Log.d(TAG, "setCustomKey: $key = $value")
        customKeys[key] = value
    }

    fun setCustomKey(key: String, value: Double) {
        Log.d(TAG, "setCustomKey: $key = $value")
        customKeys[key] = value
    }

    /**
     * Log non-fatal exceptions for monitoring
     */
    fun logException(throwable: Throwable) {
        Log.e(TAG, "Exception logged", throwable)
    }

    /**
     * Log custom messages for debugging
     */
    fun log(message: String) {
        Log.d(TAG, "Log: $message")
    }
    
    /**
     * Log ADHD-specific user actions for better UX insights
     */
    fun logUserAction(action: String, context: String? = null) {
        val logMessage = if (context != null) {
            "User Action: $action | Context: $context"
        } else {
            "User Action: $action"
        }
        Log.d(TAG, logMessage)
    }

    /**
     * Log financial operations for debugging (without sensitive data)
     */
    fun logFinancialOperation(operation: String, category: String? = null, success: Boolean = true) {
        setCustomKey("last_financial_operation", operation)
        category?.let { setCustomKey("last_operation_category", it) }
        setCustomKey("last_operation_success", success)

        val logMessage = "Financial Operation: $operation | Success: $success"
        Log.d(TAG, logMessage)
    }

    /**
     * Log ADHD-specific events for better understanding of user patterns
     */
    fun logADHDEvent(event: String, details: Map<String, String> = emptyMap()) {
        Log.d(TAG, "ADHD Event: $event")
        details.forEach { (key, value) ->
            setCustomKey("adhd_$key", value)
        }
    }
    
    /**
     * Log performance issues specific to ADHD users
     */
    fun logPerformanceIssue(issue: String, duration: Long? = null) {
        Log.w(TAG, "Performance Issue: $issue")
        duration?.let { setCustomKey("performance_duration_ms", it.toInt()) }
    }

    /**
     * Log UI interaction issues that might affect ADHD users
     */
    fun logUIIssue(screen: String, issue: String, severity: String = "medium") {
        setCustomKey("ui_issue_screen", screen)
        setCustomKey("ui_issue_severity", severity)
        Log.w(TAG, "UI Issue on $screen: $issue")
    }

    /**
     * Force a crash for testing (debug builds only)
     */
    fun testCrash() {
        if (isDebugBuild()) {
            throw RuntimeException("Test crash for CrashReportingManager")
        }
    }

    /**
     * Send any pending crash reports
     */
    fun sendUnsentReports() {
        Log.d(TAG, "sendUnsentReports called (stub implementation)")
    }

    /**
     * Check if there are unsent crash reports
     */
    fun checkForUnsentReports(callback: (Boolean) -> Unit) {
        Log.d(TAG, "checkForUnsentReports called (stub implementation)")
        callback(false) // No unsent reports in stub implementation
    }
    
    private fun isDebugBuild(): Boolean {
        return try {
            val buildConfigClass = Class.forName("${context.packageName}.BuildConfig")
            val debugField = buildConfigClass.getField("DEBUG")
            debugField.getBoolean(null)
        } catch (e: Exception) {
            false
        }
    }
    
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "unknown"
        } catch (e: Exception) {
            "unknown"
        }
    }
}

/**
 * Extension functions for easier crash reporting integration
 */
fun Throwable.reportToCrashlytics(crashReportingManager: CrashReportingManager) {
    crashReportingManager.logException(this)
}

/**
 * ADHD-specific crash reporting events
 */
object ADHDCrashEvents {
    const val TASK_BREAKDOWN_FAILED = "task_breakdown_failed"
    const val FOCUS_SESSION_INTERRUPTED = "focus_session_interrupted"
    const val IMPULSE_CONTROL_TRIGGERED = "impulse_control_triggered"
    const val BUDGET_CALCULATION_ERROR = "budget_calculation_error"
    const val AI_SERVICE_TIMEOUT = "ai_service_timeout"
    const val NOTIFICATION_DELIVERY_FAILED = "notification_delivery_failed"
    const val DATA_SYNC_FAILED = "data_sync_failed"
    const val GAMIFICATION_ERROR = "gamification_error"
    const val HABIT_TRACKING_ERROR = "habit_tracking_error"
    const val PROCRASTINATION_DETECTION_ERROR = "procrastination_detection_error"
}

/**
 * Performance monitoring events specific to ADHD user experience
 */
object ADHDPerformanceEvents {
    const val SLOW_SCREEN_LOAD = "slow_screen_load"
    const val DELAYED_USER_INPUT_RESPONSE = "delayed_user_input_response"
    const val MEMORY_PRESSURE_DETECTED = "memory_pressure_detected"
    const val DATABASE_QUERY_SLOW = "database_query_slow"
    const val AI_RESPONSE_TIMEOUT = "ai_response_timeout"
    const val ANIMATION_FRAME_DROP = "animation_frame_drop"
    const val BACKGROUND_TASK_DELAYED = "background_task_delayed"
}

/**
 * UI interaction events that are important for ADHD users
 */
object ADHDUIEvents {
    const val NAVIGATION_CONFUSION = "navigation_confusion"
    const val FORM_ABANDONMENT = "form_abandonment"
    const val REPEATED_BUTTON_TAPS = "repeated_button_taps"
    const val ACCIDENTAL_BACK_NAVIGATION = "accidental_back_navigation"
    const val FOCUS_LOSS_DURING_TASK = "focus_loss_during_task"
    const val OVERWHELMING_INFORMATION_DISPLAY = "overwhelming_information_display"
    const val INSUFFICIENT_VISUAL_FEEDBACK = "insufficient_visual_feedback"
}
