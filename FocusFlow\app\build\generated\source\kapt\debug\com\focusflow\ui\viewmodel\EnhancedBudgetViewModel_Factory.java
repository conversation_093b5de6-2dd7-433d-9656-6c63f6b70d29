package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.BudgetRecommendationRepository;
import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.service.BudgetRecommendationService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class EnhancedBudgetViewModel_Factory implements Factory<EnhancedBudgetViewModel> {
  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  private final Provider<BudgetRecommendationRepository> budgetRecommendationRepositoryProvider;

  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<BudgetRecommendationService> budgetRecommendationServiceProvider;

  public EnhancedBudgetViewModel_Factory(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<BudgetRecommendationRepository> budgetRecommendationRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<BudgetRecommendationService> budgetRecommendationServiceProvider) {
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
    this.budgetRecommendationRepositoryProvider = budgetRecommendationRepositoryProvider;
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.budgetRecommendationServiceProvider = budgetRecommendationServiceProvider;
  }

  @Override
  public EnhancedBudgetViewModel get() {
    return newInstance(budgetCategoryRepositoryProvider.get(), budgetRecommendationRepositoryProvider.get(), expenseRepositoryProvider.get(), userPreferencesRepositoryProvider.get(), budgetRecommendationServiceProvider.get());
  }

  public static EnhancedBudgetViewModel_Factory create(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<BudgetRecommendationRepository> budgetRecommendationRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<BudgetRecommendationService> budgetRecommendationServiceProvider) {
    return new EnhancedBudgetViewModel_Factory(budgetCategoryRepositoryProvider, budgetRecommendationRepositoryProvider, expenseRepositoryProvider, userPreferencesRepositoryProvider, budgetRecommendationServiceProvider);
  }

  public static EnhancedBudgetViewModel newInstance(
      BudgetCategoryRepository budgetCategoryRepository,
      BudgetRecommendationRepository budgetRecommendationRepository,
      ExpenseRepository expenseRepository, UserPreferencesRepository userPreferencesRepository,
      BudgetRecommendationService budgetRecommendationService) {
    return new EnhancedBudgetViewModel(budgetCategoryRepository, budgetRecommendationRepository, expenseRepository, userPreferencesRepository, budgetRecommendationService);
  }
}
