package com.focusflow.service;

/**
 * Security constants for ADHD-friendly financial app
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/focusflow/service/SecurityConstants;", "", "()V", "BIOMETRIC_KEY", "", "ENCRYPTION_KEY", "MAX_INPUT_LENGTH", "", "MAX_LOGIN_ATTEMPTS", "SESSION_TIMEOUT_MINUTES", "SESSION_TOKEN_KEY", "TOKEN_LENGTH", "USER_PIN_KEY", "app_debug"})
public final class SecurityConstants {
    public static final int MAX_INPUT_LENGTH = 1000;
    public static final int SESSION_TIMEOUT_MINUTES = 30;
    public static final int MAX_LOGIN_ATTEMPTS = 5;
    public static final int TOKEN_LENGTH = 32;
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String USER_PIN_KEY = "user_pin_hash";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BIOMETRIC_KEY = "biometric_enabled";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ENCRYPTION_KEY = "app_encryption_key";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String SESSION_TOKEN_KEY = "session_token";
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.SecurityConstants INSTANCE = null;
    
    private SecurityConstants() {
        super();
    }
}