package com.focusflow.data.repository;

import com.focusflow.data.dao.HabitLogDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitRepository_Factory implements Factory<HabitRepository> {
  private final Provider<HabitLogDao> habitLogDaoProvider;

  public HabitRepository_Factory(Provider<HabitLogDao> habitLogDaoProvider) {
    this.habitLogDaoProvider = habitLogDaoProvider;
  }

  @Override
  public HabitRepository get() {
    return newInstance(habitLogDaoProvider.get());
  }

  public static HabitRepository_Factory create(Provider<HabitLogDao> habitLogDaoProvider) {
    return new HabitRepository_Factory(habitLogDaoProvider);
  }

  public static HabitRepository newInstance(HabitLogDao habitLogDao) {
    return new HabitRepository(habitLogDao);
  }
}
