package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0012\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001BU\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0003\u00a2\u0006\u0002\u0010\u000fJ\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\nH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\fH\u00c6\u0003J\u000f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0003H\u00c6\u0003J]\u0010\u001f\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0003H\u00c6\u0001J\u0013\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020\nH\u00d6\u0001J\t\u0010$\u001a\u00020\u000eH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0011R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011\u00a8\u0006%"}, d2 = {"Lcom/focusflow/data/model/HelpSearchResult;", "", "articles", "", "Lcom/focusflow/data/model/HelpArticle;", "faqs", "Lcom/focusflow/data/model/HelpFAQ;", "tutorials", "Lcom/focusflow/data/model/HelpTutorial;", "totalResults", "", "searchTime", "", "suggestions", "", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;IJLjava/util/List;)V", "getArticles", "()Ljava/util/List;", "getFaqs", "getSearchTime", "()J", "getSuggestions", "getTotalResults", "()I", "getTutorials", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class HelpSearchResult {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpArticle> articles = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpFAQ> faqs = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpTutorial> tutorials = null;
    private final int totalResults = 0;
    private final long searchTime = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> suggestions = null;
    
    public HelpSearchResult(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> articles, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFAQ> faqs, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpTutorial> tutorials, int totalResults, long searchTime, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> suggestions) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpArticle> getArticles() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpFAQ> getFaqs() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpTutorial> getTutorials() {
        return null;
    }
    
    public final int getTotalResults() {
        return 0;
    }
    
    public final long getSearchTime() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getSuggestions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpArticle> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpFAQ> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpTutorial> component3() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final long component5() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpSearchResult copy(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> articles, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFAQ> faqs, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpTutorial> tutorials, int totalResults, long searchTime, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> suggestions) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}