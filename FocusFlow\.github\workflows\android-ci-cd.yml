name: FocusFlow Android CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]
  workflow_dispatch:
    inputs:
      skip_deployment:
        description: 'Skip deployment steps'
        required: false
        default: 'false'
        type: boolean

env:
  JAVA_VERSION: 11

jobs:
  validate-secrets:
    name: Validate Required Secrets
    runs-on: ubuntu-latest
    outputs:
      has-release-secrets: ${{ steps.check-secrets.outputs.has-release-secrets }}
      has-deployment-secrets: ${{ steps.check-secrets.outputs.has-deployment-secrets }}
      can-build-release: ${{ steps.check-secrets.outputs.can-build-release }}
      can-deploy: ${{ steps.check-secrets.outputs.can-deploy }}

    steps:
    - name: Check required secrets
      id: check-secrets
      run: |
        echo "Validating repository secrets for CI/CD pipeline..."

        # Check release build secrets
        HAS_KEYSTORE="${{ secrets.KEYSTORE_BASE64 != '' }}"
        HAS_KEY_ALIAS="${{ secrets.SIGNING_KEY_ALIAS != '' }}"
        HAS_KEY_PASSWORD="${{ secrets.SIGNING_KEY_PASSWORD != '' }}"
        HAS_STORE_PASSWORD="${{ secrets.SIGNING_STORE_PASSWORD != '' }}"

        # Check deployment secrets
        HAS_PLAY_SERVICE_ACCOUNT="${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON != '' }}"

        # Determine capabilities
        if [[ "$HAS_KEYSTORE" == "true" && "$HAS_KEY_ALIAS" == "true" && "$HAS_KEY_PASSWORD" == "true" && "$HAS_STORE_PASSWORD" == "true" ]]; then
          CAN_BUILD_RELEASE="true"
          HAS_RELEASE_SECRETS="true"
          echo "✅ All release build secrets are available"
        else
          CAN_BUILD_RELEASE="false"
          HAS_RELEASE_SECRETS="false"
          echo "⚠️  Release build secrets missing - will skip release builds"
          echo "   Missing secrets:"
          [[ "$HAS_KEYSTORE" != "true" ]] && echo "   - KEYSTORE_BASE64"
          [[ "$HAS_KEY_ALIAS" != "true" ]] && echo "   - SIGNING_KEY_ALIAS"
          [[ "$HAS_KEY_PASSWORD" != "true" ]] && echo "   - SIGNING_KEY_PASSWORD"
          [[ "$HAS_STORE_PASSWORD" != "true" ]] && echo "   - SIGNING_STORE_PASSWORD"
        fi

        if [[ "$HAS_PLAY_SERVICE_ACCOUNT" == "true" ]]; then
          HAS_DEPLOYMENT_SECRETS="true"
          echo "✅ Google Play deployment secrets are available"
        else
          HAS_DEPLOYMENT_SECRETS="false"
          echo "⚠️  Google Play deployment secrets missing - will skip deployment"
          echo "   Missing secrets:"
          echo "   - GOOGLE_PLAY_SERVICE_ACCOUNT_JSON"
        fi

        if [[ "$CAN_BUILD_RELEASE" == "true" && "$HAS_DEPLOYMENT_SECRETS" == "true" ]]; then
          CAN_DEPLOY="true"
          echo "✅ Full CI/CD pipeline available"
        else
          CAN_DEPLOY="false"
          echo "ℹ️  Limited CI/CD pipeline - debug builds and tests will run"
        fi

        # Set outputs
        echo "has-release-secrets=$HAS_RELEASE_SECRETS" >> $GITHUB_OUTPUT
        echo "has-deployment-secrets=$HAS_DEPLOYMENT_SECRETS" >> $GITHUB_OUTPUT
        echo "can-build-release=$CAN_BUILD_RELEASE" >> $GITHUB_OUTPUT
        echo "can-deploy=$CAN_DEPLOY" >> $GITHUB_OUTPUT

        # Create setup instructions if secrets are missing
        if [[ "$CAN_DEPLOY" != "true" ]]; then
          echo ""
          echo "📋 Setup Instructions:"
          echo "   1. See .github/SETUP_GUIDE.md for complete setup instructions"
          echo "   2. Generate Android keystore and add signing secrets"
          echo "   3. Create Google Play Console service account"
          echo "   4. Add all secrets to repository settings"
          echo ""
        fi

  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: validate-secrets
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Run unit tests
      run: ./gradlew test --continue
      
    - name: Generate test report
      uses: dorny/test-reporter@v1
      if: success() || failure()
      with:
        name: Unit Test Results
        path: '**/build/test-results/test/TEST-*.xml'
        reporter: java-junit
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: '**/build/test-results/test/TEST-*.xml'

  lint:
    name: Code Quality & Lint
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Run lint
      run: ./gradlew lint
      
    - name: Upload lint results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: lint-results
        path: '**/build/reports/lint-results-*.html'

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  build-debug:
    name: Build Debug APK
    runs-on: ubuntu-latest
    needs: [test, lint]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Build debug APK
      run: ./gradlew assembleDebug
      
    - name: Upload debug APK
      uses: actions/upload-artifact@v3
      with:
        name: debug-apk
        path: app/build/outputs/apk/debug/*.apk

  ui-tests:
    name: UI Tests
    runs-on: macos-latest
    needs: [build-debug]
    
    strategy:
      matrix:
        api-level: [24, 28, 33]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: AVD cache
      uses: actions/cache@v3
      id: avd-cache
      with:
        path: |
          ~/.android/avd/*
          ~/.android/adb*
        key: avd-${{ matrix.api-level }}
        
    - name: Create AVD and generate snapshot for caching
      if: steps.avd-cache.outputs.cache-hit != 'true'
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        force-avd-creation: false
        emulator-options: -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: false
        script: echo "Generated AVD snapshot for caching."
        
    - name: Run UI tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: ${{ matrix.api-level }}
        force-avd-creation: false
        emulator-options: -no-snapshot-save -no-window -gpu swiftshader_indirect -noaudio -no-boot-anim -camera-back none
        disable-animations: true
        script: ./gradlew connectedCheck --continue
        
    - name: Upload UI test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: ui-test-results-api-${{ matrix.api-level }}
        path: '**/build/reports/androidTests/connected/**'

  build-release:
    name: Build Release
    runs-on: ubuntu-latest
    needs: [validate-secrets, test, lint, security-scan]
    if: github.event_name == 'release' && needs.validate-secrets.outputs.can-build-release == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v3
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Validate release secrets
      run: |
        echo "Validating release build secrets..."
        if [[ -z "${{ secrets.KEYSTORE_BASE64 }}" ]]; then
          echo "❌ KEYSTORE_BASE64 secret is missing"
          echo "Please add the base64 encoded keystore to repository secrets"
          exit 1
        fi
        if [[ -z "${{ secrets.SIGNING_KEY_ALIAS }}" ]]; then
          echo "❌ SIGNING_KEY_ALIAS secret is missing"
          exit 1
        fi
        if [[ -z "${{ secrets.SIGNING_KEY_PASSWORD }}" ]]; then
          echo "❌ SIGNING_KEY_PASSWORD secret is missing"
          exit 1
        fi
        if [[ -z "${{ secrets.SIGNING_STORE_PASSWORD }}" ]]; then
          echo "❌ SIGNING_STORE_PASSWORD secret is missing"
          exit 1
        fi
        echo "✅ All release secrets are available"

    - name: Decode keystore
      env:
        ENCODED_STRING: ${{ secrets.KEYSTORE_BASE64 }}
      run: |
        echo "Decoding keystore..."
        if [[ -z "$ENCODED_STRING" ]]; then
          echo "❌ KEYSTORE_BASE64 is empty"
          exit 1
        fi
        echo $ENCODED_STRING | base64 -di > app/keystore.jks
        if [[ ! -f app/keystore.jks ]]; then
          echo "❌ Failed to create keystore file"
          exit 1
        fi
        echo "✅ Keystore decoded successfully"
        
    - name: Build release AAB
      env:
        SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
        SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
        SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
      run: ./gradlew bundleRelease
      
    - name: Build release APK
      env:
        SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
        SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
        SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
      run: ./gradlew assembleRelease
      
    - name: Upload release AAB
      uses: actions/upload-artifact@v3
      with:
        name: release-aab
        path: app/build/outputs/bundle/release/*.aab
        
    - name: Upload release APK
      uses: actions/upload-artifact@v3
      with:
        name: release-apk
        path: app/build/outputs/apk/release/*.apk

  deploy-internal:
    name: Deploy to Internal Testing
    runs-on: ubuntu-latest
    needs: [validate-secrets, build-release, ui-tests]
    if: github.event_name == 'release' && contains(github.event.release.tag_name, 'alpha') && needs.validate-secrets.outputs.can-deploy == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Validate deployment secrets
      run: |
        echo "Validating Google Play Console deployment secrets..."
        if [[ -z "${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}" ]]; then
          echo "❌ GOOGLE_PLAY_SERVICE_ACCOUNT_JSON secret is missing"
          echo "Please add the service account JSON to repository secrets"
          echo "See .github/SETUP_GUIDE.md for instructions"
          exit 1
        fi
        echo "✅ Google Play Console secrets are available"

    - name: Download release AAB
      uses: actions/download-artifact@v3
      with:
        name: release-aab
        path: app/build/outputs/bundle/release/

    - name: Validate AAB file
      run: |
        if [[ ! -f app/build/outputs/bundle/release/*.aab ]]; then
          echo "❌ Release AAB file not found"
          echo "Build may have failed or artifact was not uploaded"
          exit 1
        fi
        echo "✅ Release AAB file found"

    - name: Deploy to Play Console Internal Testing
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}
        packageName: com.focusflow
        releaseFiles: app/build/outputs/bundle/release/*.aab
        track: internal
        status: completed

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [validate-secrets, build-release, ui-tests]
    if: github.event_name == 'release' && !contains(github.event.release.tag_name, 'alpha') && !contains(github.event.release.tag_name, 'beta') && needs.validate-secrets.outputs.can-deploy == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download release AAB
      uses: actions/download-artifact@v3
      with:
        name: release-aab
        path: app/build/outputs/bundle/release/
        
    - name: Deploy to Play Console Production
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}
        packageName: com.focusflow
        releaseFiles: app/build/outputs/bundle/release/*.aab
        track: production
        status: completed

  setup-instructions:
    name: Setup Instructions
    runs-on: ubuntu-latest
    needs: [validate-secrets]
    if: always() && (needs.validate-secrets.outputs.can-build-release != 'true' || needs.validate-secrets.outputs.can-deploy != 'true')

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Display setup instructions
      run: |
        echo "🔧 CI/CD Pipeline Setup Required"
        echo "=================================="
        echo ""
        echo "The FocusFlow CI/CD pipeline requires additional setup to enable full functionality."
        echo ""
        echo "Current Status:"
        echo "- Release builds: ${{ needs.validate-secrets.outputs.can-build-release == 'true' && '✅ Available' || '❌ Missing secrets' }}"
        echo "- Google Play deployment: ${{ needs.validate-secrets.outputs.can-deploy == 'true' && '✅ Available' || '❌ Missing secrets' }}"
        echo ""
        echo "📋 Required Actions:"
        echo ""
        if [[ "${{ needs.validate-secrets.outputs.can-build-release }}" != "true" ]]; then
          echo "1. Generate Android keystore for app signing:"
          echo "   - Follow instructions in .github/SETUP_GUIDE.md"
          echo "   - Add KEYSTORE_BASE64, SIGNING_KEY_ALIAS, SIGNING_KEY_PASSWORD, SIGNING_STORE_PASSWORD secrets"
          echo ""
        fi
        if [[ "${{ needs.validate-secrets.outputs.can-deploy }}" != "true" ]]; then
          echo "2. Set up Google Play Console service account:"
          echo "   - Create service account in Google Cloud Console"
          echo "   - Grant permissions in Google Play Console"
          echo "   - Add GOOGLE_PLAY_SERVICE_ACCOUNT_JSON secret"
          echo ""
        fi
        echo "📖 Complete setup guide: .github/SETUP_GUIDE.md"
        echo "🔒 Security documentation: .github/SECRETS_DOCUMENTATION.md"
        echo ""
        echo "💡 Current capabilities:"
        echo "- ✅ Debug builds and testing (always available)"
        echo "- ✅ Code quality checks and security scans"
        echo "- ✅ Unit and UI tests"
        if [[ "${{ needs.validate-secrets.outputs.can-build-release }}" == "true" ]]; then
          echo "- ✅ Release builds"
        else
          echo "- ❌ Release builds (missing keystore secrets)"
        fi
        if [[ "${{ needs.validate-secrets.outputs.can-deploy }}" == "true" ]]; then
          echo "- ✅ Automated deployment"
        else
          echo "- ❌ Automated deployment (missing Play Console secrets)"
        fi

  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [validate-secrets, test, lint, security-scan, build-debug, ui-tests, build-release, deploy-production, deploy-internal, setup-instructions]
    if: always()

    steps:
    - name: Determine workflow status
      id: status
      run: |
        echo "Analyzing workflow results..."

        # Check critical job results
        VALIDATION_RESULT="${{ needs.validate-secrets.result }}"
        TEST_RESULT="${{ needs.test.result }}"
        LINT_RESULT="${{ needs.lint.result }}"
        SECURITY_RESULT="${{ needs.security-scan.result }}"
        BUILD_DEBUG_RESULT="${{ needs.build-debug.result }}"
        UI_TEST_RESULT="${{ needs.ui-tests.result }}"
        BUILD_RELEASE_RESULT="${{ needs.build-release.result }}"
        DEPLOY_PROD_RESULT="${{ needs.deploy-production.result }}"
        DEPLOY_INTERNAL_RESULT="${{ needs.deploy-internal.result }}"

        # Determine overall status
        CRITICAL_FAILURE=false
        DEPLOYMENT_SUCCESS=false
        SETUP_NEEDED=false

        # Check for critical failures
        if [[ "$TEST_RESULT" == "failure" || "$LINT_RESULT" == "failure" || "$SECURITY_RESULT" == "failure" ]]; then
          CRITICAL_FAILURE=true
        fi

        # Check for successful deployment
        if [[ "$DEPLOY_PROD_RESULT" == "success" || "$DEPLOY_INTERNAL_RESULT" == "success" ]]; then
          DEPLOYMENT_SUCCESS=true
        fi

        # Check if setup is needed
        if [[ "${{ needs.validate-secrets.outputs.can-deploy }}" != "true" ]]; then
          SETUP_NEEDED=true
        fi

        echo "critical-failure=$CRITICAL_FAILURE" >> $GITHUB_OUTPUT
        echo "deployment-success=$DEPLOYMENT_SUCCESS" >> $GITHUB_OUTPUT
        echo "setup-needed=$SETUP_NEEDED" >> $GITHUB_OUTPUT

        # Create summary
        echo "## 🔍 Workflow Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Job | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Secret Validation | $([[ "$VALIDATION_RESULT" == "success" ]] && echo "✅" || echo "❌") $VALIDATION_RESULT |" >> $GITHUB_STEP_SUMMARY
        echo "| Unit Tests | $([[ "$TEST_RESULT" == "success" ]] && echo "✅" || echo "❌") $TEST_RESULT |" >> $GITHUB_STEP_SUMMARY
        echo "| Code Quality | $([[ "$LINT_RESULT" == "success" ]] && echo "✅" || echo "❌") $LINT_RESULT |" >> $GITHUB_STEP_SUMMARY
        echo "| Security Scan | $([[ "$SECURITY_RESULT" == "success" ]] && echo "✅" || echo "❌") $SECURITY_RESULT |" >> $GITHUB_STEP_SUMMARY
        echo "| Debug Build | $([[ "$BUILD_DEBUG_RESULT" == "success" ]] && echo "✅" || echo "❌") $BUILD_DEBUG_RESULT |" >> $GITHUB_STEP_SUMMARY
        echo "| UI Tests | $([[ "$UI_TEST_RESULT" == "success" ]] && echo "✅" || echo "❌") $UI_TEST_RESULT |" >> $GITHUB_STEP_SUMMARY

        if [[ "$BUILD_RELEASE_RESULT" != "skipped" ]]; then
          echo "| Release Build | $([[ "$BUILD_RELEASE_RESULT" == "success" ]] && echo "✅" || echo "❌") $BUILD_RELEASE_RESULT |" >> $GITHUB_STEP_SUMMARY
        fi

        if [[ "$DEPLOY_PROD_RESULT" != "skipped" ]]; then
          echo "| Production Deploy | $([[ "$DEPLOY_PROD_RESULT" == "success" ]] && echo "✅" || echo "❌") $DEPLOY_PROD_RESULT |" >> $GITHUB_STEP_SUMMARY
        fi

        if [[ "$DEPLOY_INTERNAL_RESULT" != "skipped" ]]; then
          echo "| Internal Deploy | $([[ "$DEPLOY_INTERNAL_RESULT" == "success" ]] && echo "✅" || echo "❌") $DEPLOY_INTERNAL_RESULT |" >> $GITHUB_STEP_SUMMARY
        fi

    - name: Notify on critical failure
      if: steps.status.outputs.critical-failure == 'true'
      run: |
        echo "🚨 Critical CI/CD Failure Detected!"
        echo "=================================="
        echo ""
        echo "One or more critical jobs failed:"
        echo "- Tests: ${{ needs.test.result }}"
        echo "- Code Quality: ${{ needs.lint.result }}"
        echo "- Security: ${{ needs.security-scan.result }}"
        echo ""
        echo "🔧 Action Required:"
        echo "- Review failed job logs"
        echo "- Fix identified issues"
        echo "- Re-run workflow after fixes"
        echo ""
        echo "📋 Troubleshooting:"
        echo "- Check .github/README.md for common issues"
        echo "- Review workflow logs for specific errors"
        echo "- Contact team if assistance needed"

    - name: Notify on deployment success
      if: steps.status.outputs.deployment-success == 'true'
      run: |
        echo "🎉 FocusFlow Deployment Successful!"
        echo "=================================="
        echo ""
        echo "✅ Release has been deployed successfully"
        echo "📱 App is now available to users"
        echo "📊 Monitor app performance and user feedback"
        echo ""
        echo "🔗 Next Steps:"
        echo "- Monitor Google Play Console for metrics"
        echo "- Check crash reporting for any issues"
        echo "- Review user feedback and ratings"
        echo "- Plan next release based on feedback"

    - name: Notify on setup needed
      if: steps.status.outputs.setup-needed == 'true'
      run: |
        echo "🔧 CI/CD Setup Required"
        echo "======================"
        echo ""
        echo "The pipeline is running with limited functionality."
        echo "Complete setup is required for full CI/CD capabilities."
        echo ""
        echo "📋 Setup Status:"
        echo "- Release builds: ${{ needs.validate-secrets.outputs.can-build-release == 'true' && '✅ Available' || '❌ Setup needed' }}"
        echo "- Deployment: ${{ needs.validate-secrets.outputs.can-deploy == 'true' && '✅ Available' || '❌ Setup needed' }}"
        echo ""
        echo "📖 Setup Instructions:"
        echo "- Complete guide: .github/SETUP_GUIDE.md"
        echo "- Security docs: .github/SECRETS_DOCUMENTATION.md"
        echo "- Validation script: .github/scripts/validate-secrets.sh"

    - name: Create workflow summary
      if: always()
      run: |
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## 📋 Next Steps" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if [[ "${{ steps.status.outputs.critical-failure }}" == "true" ]]; then
          echo "🚨 **Critical issues detected** - Fix failed jobs before proceeding" >> $GITHUB_STEP_SUMMARY
        elif [[ "${{ steps.status.outputs.deployment-success }}" == "true" ]]; then
          echo "🎉 **Deployment successful** - Monitor app performance and user feedback" >> $GITHUB_STEP_SUMMARY
        elif [[ "${{ steps.status.outputs.setup-needed }}" == "true" ]]; then
          echo "🔧 **Setup required** - Complete CI/CD configuration for full functionality" >> $GITHUB_STEP_SUMMARY
        else
          echo "✅ **Build successful** - Ready for release when secrets are configured" >> $GITHUB_STEP_SUMMARY
        fi

        echo "" >> $GITHUB_STEP_SUMMARY
        echo "📚 **Resources:**" >> $GITHUB_STEP_SUMMARY
        echo "- [Setup Guide](.github/SETUP_GUIDE.md)" >> $GITHUB_STEP_SUMMARY
        echo "- [Security Documentation](.github/SECRETS_DOCUMENTATION.md)" >> $GITHUB_STEP_SUMMARY
        echo "- [CI/CD Overview](.github/README.md)" >> $GITHUB_STEP_SUMMARY
