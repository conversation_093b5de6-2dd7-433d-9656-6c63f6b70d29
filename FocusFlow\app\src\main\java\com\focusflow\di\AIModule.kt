package com.focusflow.di

import com.focusflow.BuildConfig
import com.focusflow.service.AIService
import com.focusflow.service.ClaudeAIService
import com.focusflow.service.MockAIService
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit
import javax.inject.Qualifier
import javax.inject.Singleton

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MockAI

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RealAI

@Module
@InstallIn(SingletonComponent::class)
abstract class AIModule {

    companion object {
        @Provides
        @Singleton
        fun provideOkHttpClient(): OkHttpClient {
            val loggingInterceptor = HttpLoggingInterceptor().apply {
                level = if (BuildConfig.DEBUG) {
                    HttpLoggingInterceptor.Level.BODY
                } else {
                    HttpLoggingInterceptor.Level.NONE
                }
            }

            return OkHttpClient.Builder()
                .addInterceptor(loggingInterceptor)
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build()
        }
    }

    @Binds
    @Singleton
    @MockAI
    abstract fun bindMockAIService(
        mockAIService: MockAIService
    ): AIService

    @Binds
    @Singleton
    @RealAI
    abstract fun bindRealAIService(
        claudeAIService: ClaudeAIService
    ): AIService

    @Binds
    @Singleton
    abstract fun bindAIService(
        // Use MockAI for development, RealAI for production
        // Change this annotation to switch between implementations
        @MockAI aiService: AIService
    ): AIService
}
