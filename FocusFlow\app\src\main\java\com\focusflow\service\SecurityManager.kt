package com.focusflow.service

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import dagger.hilt.android.qualifiers.ApplicationContext
import java.security.KeyStore
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.inject.Inject
import javax.inject.Singleton
import android.util.Base64
import java.nio.charset.StandardCharsets

/**
 * Comprehensive security manager for FocusFlow
 * Handles data encryption, secure storage, and security validation
 * Designed with ADHD user privacy and data protection in mind
 */
@Singleton
class SecurityManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val crashReportingManager: CrashReportingManager
) {
    
    companion object {
        private const val KEYSTORE_ALIAS = "FocusFlowMasterKey"
        private const val ENCRYPTED_PREFS_NAME = "focus_flow_secure_prefs"
        private const val AES_TRANSFORMATION = "AES/GCM/NoPadding"
        private const val GCM_IV_LENGTH = 12
        private const val GCM_TAG_LENGTH = 16
    }
    
    private val keyStore: KeyStore by lazy {
        KeyStore.getInstance("AndroidKeyStore").apply {
            load(null)
        }
    }
    
    private val masterKey: MasterKey by lazy {
        MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
    }
    
    private val encryptedSharedPreferences by lazy {
        EncryptedSharedPreferences.create(
            context,
            ENCRYPTED_PREFS_NAME,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    init {
        initializeSecurity()
    }
    
    private fun initializeSecurity() {
        try {
            // Generate master key if it doesn't exist
            if (!keyStore.containsAlias(KEYSTORE_ALIAS)) {
                generateMasterKey()
            }
            
            // Log security initialization (without sensitive data)
            crashReportingManager.log("Security manager initialized successfully")
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    private fun generateMasterKey() {
        try {
            val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, "AndroidKeyStore")
            val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                KEYSTORE_ALIAS,
                KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
            )
                .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                .setUserAuthenticationRequired(false) // For background operations
                .setRandomizedEncryptionRequired(true)
                .build()
            
            keyGenerator.init(keyGenParameterSpec)
            keyGenerator.generateKey()
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            throw SecurityException("Failed to generate master key", e)
        }
    }
    
    /**
     * Encrypt sensitive data using AES-GCM encryption
     */
    fun encryptData(plaintext: String): EncryptedData? {
        return try {
            val secretKey = keyStore.getKey(KEYSTORE_ALIAS, null) as SecretKey
            val cipher = Cipher.getInstance(AES_TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val iv = cipher.iv
            val encryptedBytes = cipher.doFinal(plaintext.toByteArray(StandardCharsets.UTF_8))
            
            EncryptedData(
                encryptedData = Base64.encodeToString(encryptedBytes, Base64.DEFAULT),
                iv = Base64.encodeToString(iv, Base64.DEFAULT)
            )
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            null
        }
    }
    
    /**
     * Decrypt sensitive data
     */
    fun decryptData(encryptedData: EncryptedData): String? {
        return try {
            val secretKey = keyStore.getKey(KEYSTORE_ALIAS, null) as SecretKey
            val cipher = Cipher.getInstance(AES_TRANSFORMATION)
            
            val iv = Base64.decode(encryptedData.iv, Base64.DEFAULT)
            val gcmSpec = GCMParameterSpec(GCM_TAG_LENGTH * 8, iv)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec)
            
            val encryptedBytes = Base64.decode(encryptedData.encryptedData, Base64.DEFAULT)
            val decryptedBytes = cipher.doFinal(encryptedBytes)
            
            String(decryptedBytes, StandardCharsets.UTF_8)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            null
        }
    }
    
    /**
     * Store sensitive data securely in encrypted shared preferences
     */
    fun storeSecureData(key: String, value: String): Boolean {
        return try {
            encryptedSharedPreferences.edit()
                .putString(key, value)
                .apply()
            true
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            false
        }
    }
    
    /**
     * Retrieve sensitive data from encrypted shared preferences
     */
    fun getSecureData(key: String, defaultValue: String? = null): String? {
        return try {
            encryptedSharedPreferences.getString(key, defaultValue)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            defaultValue
        }
    }
    
    /**
     * Remove sensitive data from secure storage
     */
    fun removeSecureData(key: String): Boolean {
        return try {
            encryptedSharedPreferences.edit()
                .remove(key)
                .apply()
            true
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            false
        }
    }
    
    /**
     * Clear all secure data (for user logout or data reset)
     */
    fun clearAllSecureData(): Boolean {
        return try {
            encryptedSharedPreferences.edit()
                .clear()
                .apply()
            true
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            false
        }
    }
    
    /**
     * Generate a secure random token for session management
     */
    fun generateSecureToken(length: Int = 32): String {
        return try {
            val secureRandom = SecureRandom()
            val tokenBytes = ByteArray(length)
            secureRandom.nextBytes(tokenBytes)
            Base64.encodeToString(tokenBytes, Base64.URL_SAFE or Base64.NO_WRAP)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            ""
        }
    }
    
    /**
     * Validate input to prevent injection attacks
     */
    fun validateInput(input: String, maxLength: Int = 1000): ValidationResult {
        return try {
            when {
                input.length > maxLength -> ValidationResult.Invalid("Input too long")
                input.contains(Regex("[<>\"'&]")) -> ValidationResult.Invalid("Invalid characters detected")
                input.contains(Regex("(?i)(script|javascript|vbscript|onload|onerror)")) -> ValidationResult.Invalid("Potentially malicious content")
                else -> ValidationResult.Valid
            }
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            ValidationResult.Invalid("Validation error")
        }
    }
    
    /**
     * Sanitize user input for safe storage and display
     */
    fun sanitizeInput(input: String): String {
        return try {
            input.replace(Regex("[<>\"'&]"), "")
                .replace(Regex("(?i)(script|javascript|vbscript|onload|onerror)"), "")
                .trim()
                .take(1000) // Limit length
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            ""
        }
    }
    
    /**
     * Hash sensitive data for comparison without storing plaintext
     */
    fun hashData(data: String, salt: String = ""): String {
        return try {
            val combined = data + salt
            val digest = java.security.MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(combined.toByteArray(StandardCharsets.UTF_8))
            Base64.encodeToString(hashBytes, Base64.DEFAULT)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            ""
        }
    }
    
    /**
     * Check if the app is running in a secure environment
     */
    fun isSecureEnvironment(): SecurityEnvironmentCheck {
        return try {
            val checks = mutableListOf<String>()
            
            // Check if device is rooted (basic check)
            if (isDeviceRooted()) {
                checks.add("Device appears to be rooted")
            }
            
            // Check if debugging is enabled
            if (isDebuggingEnabled()) {
                checks.add("Debugging is enabled")
            }
            
            // Check if running in emulator
            if (isEmulator()) {
                checks.add("Running in emulator")
            }
            
            SecurityEnvironmentCheck(
                isSecure = checks.isEmpty(),
                warnings = checks
            )
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            SecurityEnvironmentCheck(false, listOf("Security check failed"))
        }
    }
    
    private fun isDeviceRooted(): Boolean {
        return try {
            val paths = arrayOf(
                "/system/app/Superuser.apk",
                "/sbin/su",
                "/system/bin/su",
                "/system/xbin/su",
                "/data/local/xbin/su",
                "/data/local/bin/su",
                "/system/sd/xbin/su",
                "/system/bin/failsafe/su",
                "/data/local/su"
            )
            paths.any { java.io.File(it).exists() }
        } catch (e: Exception) {
            false
        }
    }
    
    private fun isDebuggingEnabled(): Boolean {
        return try {
            val buildConfigClass = Class.forName("${context.packageName}.BuildConfig")
            val debugField = buildConfigClass.getField("DEBUG")
            debugField.getBoolean(null)
        } catch (e: Exception) {
            false
        }
    }
    
    private fun isEmulator(): Boolean {
        return try {
            android.os.Build.FINGERPRINT.startsWith("generic") ||
            android.os.Build.FINGERPRINT.startsWith("unknown") ||
            android.os.Build.MODEL.contains("google_sdk") ||
            android.os.Build.MODEL.contains("Emulator") ||
            android.os.Build.MODEL.contains("Android SDK built for x86") ||
            android.os.Build.MANUFACTURER.contains("Genymotion") ||
            android.os.Build.BRAND.startsWith("generic") && android.os.Build.DEVICE.startsWith("generic")
        } catch (e: Exception) {
            false
        }
    }
}

/**
 * Data class for encrypted data storage
 */
data class EncryptedData(
    val encryptedData: String,
    val iv: String
)

/**
 * Validation result for input validation
 */
sealed class ValidationResult {
    object Valid : ValidationResult()
    data class Invalid(val reason: String) : ValidationResult()
}

/**
 * Security environment check result
 */
data class SecurityEnvironmentCheck(
    val isSecure: Boolean,
    val warnings: List<String>
)

/**
 * Security constants for ADHD-friendly financial app
 */
object SecurityConstants {
    const val MAX_INPUT_LENGTH = 1000
    const val SESSION_TIMEOUT_MINUTES = 30
    const val MAX_LOGIN_ATTEMPTS = 5
    const val TOKEN_LENGTH = 32
    
    // Sensitive data keys
    const val USER_PIN_KEY = "user_pin_hash"
    const val BIOMETRIC_KEY = "biometric_enabled"
    const val ENCRYPTION_KEY = "app_encryption_key"
    const val SESSION_TOKEN_KEY = "session_token"
}
