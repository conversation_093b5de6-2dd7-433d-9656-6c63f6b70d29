package com.focusflow.ui.components.help;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000h\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\u001a\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001aF\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\n2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\r2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a0\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00132\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\r2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001aH\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0019\u001a\u00020\u001a2\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00172\u0010\b\u0002\u0010\u001c\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\r2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001aF\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u001e\u001a\u00020\u00172\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010 2\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00010\r2\b\b\u0002\u0010\"\u001a\u00020\u00172\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a(\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\r2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\u0010\u0010\'\u001a\u00020\u001a2\u0006\u0010(\u001a\u00020\u0017H\u0002\u001a\u0015\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020,H\u0002\u00a2\u0006\u0002\u0010-\u001a\u0010\u0010.\u001a\u00020\u00172\u0006\u0010+\u001a\u00020,H\u0002\u001a\u0010\u0010/\u001a\u00020\u001a2\u0006\u0010(\u001a\u00020\u0017H\u0002\u00a8\u00060"}, d2 = {"DifficultyIndicator", "", "difficulty", "Lcom/focusflow/data/model/DifficultyLevel;", "modifier", "Landroidx/compose/ui/Modifier;", "HelpArticleCard", "article", "Lcom/focusflow/data/model/HelpArticle;", "isCompleted", "", "isBookmarked", "onArticleClick", "Lkotlin/Function0;", "onBookmarkClick", "HelpCategoryCard", "category", "Lcom/focusflow/data/model/HelpCategory;", "articleCount", "", "onClick", "HelpEmptyState", "title", "", "description", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "actionText", "onActionClick", "HelpSearchBar", "query", "onQueryChange", "Lkotlin/Function1;", "onSearch", "placeholder", "QuickTipCard", "tip", "Lcom/focusflow/data/model/HelpQuickTip;", "onDismiss", "getCategoryIcon", "iconName", "getContentTypeColor", "Landroidx/compose/ui/graphics/Color;", "contentType", "Lcom/focusflow/data/model/HelpContentType;", "(Lcom/focusflow/data/model/HelpContentType;)J", "getContentTypeLabel", "getTipIcon", "app_debug"})
public final class HelpComponentsKt {
    
    /**
     * ADHD-friendly help system UI components
     * Designed for cognitive load reduction and clear visual hierarchy
     */
    @androidx.compose.runtime.Composable
    public static final void HelpCategoryCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpCategory category, int articleCount, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void HelpArticleCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpArticle article, boolean isCompleted, boolean isBookmarked, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onArticleClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onBookmarkClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void DifficultyIndicator(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.DifficultyLevel difficulty, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void HelpSearchBar(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onQueryChange, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSearch, @org.jetbrains.annotations.NotNull
    java.lang.String placeholder, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void QuickTipCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpQuickTip tip, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void HelpEmptyState(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.graphics.vector.ImageVector icon, @org.jetbrains.annotations.Nullable
    java.lang.String actionText, @org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function0<kotlin.Unit> onActionClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    private static final androidx.compose.ui.graphics.vector.ImageVector getCategoryIcon(java.lang.String iconName) {
        return null;
    }
    
    private static final androidx.compose.ui.graphics.vector.ImageVector getTipIcon(java.lang.String iconName) {
        return null;
    }
    
    private static final long getContentTypeColor(com.focusflow.data.model.HelpContentType contentType) {
        return 0L;
    }
    
    private static final java.lang.String getContentTypeLabel(com.focusflow.data.model.HelpContentType contentType) {
        return null;
    }
}