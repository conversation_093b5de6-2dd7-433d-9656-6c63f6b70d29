package com.focusflow.service;

/**
 * Performance monitoring events specific to ADHD user experience
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/focusflow/service/ADHDPerformanceEvents;", "", "()V", "AI_RESPONSE_TIMEOUT", "", "ANIMATION_FRAME_DROP", "BACKGROUND_TASK_DELAYED", "DATABASE_QUERY_SLOW", "DELAYED_USER_INPUT_RESPONSE", "MEMORY_PRESSURE_DETECTED", "SLOW_SCREEN_LOAD", "app_debug"})
public final class ADHDPerformanceEvents {
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String SLOW_SCREEN_LOAD = "slow_screen_load";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String DELAYED_USER_INPUT_RESPONSE = "delayed_user_input_response";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String MEMORY_PRESSURE_DETECTED = "memory_pressure_detected";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String DATABASE_QUERY_SLOW = "database_query_slow";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String AI_RESPONSE_TIMEOUT = "ai_response_timeout";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ANIMATION_FRAME_DROP = "animation_frame_drop";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BACKGROUND_TASK_DELAYED = "background_task_delayed";
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.ADHDPerformanceEvents INSTANCE = null;
    
    private ADHDPerformanceEvents() {
        super();
    }
}