package com.focusflow.di;

import com.focusflow.data.dao.UserPreferencesDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideUserPreferencesDaoFactory implements Factory<UserPreferencesDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvideUserPreferencesDaoFactory(
      Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public UserPreferencesDao get() {
    return provideUserPreferencesDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideUserPreferencesDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvideUserPreferencesDaoFactory(databaseProvider);
  }

  public static UserPreferencesDao provideUserPreferencesDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideUserPreferencesDao(database));
  }
}
