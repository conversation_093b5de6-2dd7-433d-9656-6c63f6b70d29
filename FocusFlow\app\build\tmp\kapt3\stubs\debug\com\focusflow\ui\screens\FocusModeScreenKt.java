package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000J\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0012\u001aT\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a \u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\bH\u0007\u001a:\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00052\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00152\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00050\u0017H\u0007\u001a \u0010\u0018\u001a\u00020\u00012\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u0088\u0001\u0010\u001c\u001a\u00020\u00012\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00030\u00172\b\u0010\u001e\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u001f\u001a\u00020\u00052\u0006\u0010 \u001a\u00020\u00052\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00152\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00152\u0012\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00152\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a6\u0010%\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a&\u0010\'\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010(\u001a\u00020\b2\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a\u0010\u0010*\u001a\u00020\u00122\u0006\u0010+\u001a\u00020\u0005H\u0002\u00a8\u0006,"}, d2 = {"ActiveFocusSession", "", "task", "Lcom/focusflow/data/model/Task;", "timeRemaining", "", "totalTime", "isBreak", "", "onPause", "Lkotlin/Function0;", "onStop", "onCompleteTask", "CircularProgressTimer", "progress", "", "DurationSelector", "label", "", "value", "onValueChange", "Lkotlin/Function1;", "options", "", "FocusModeScreen", "viewModel", "Lcom/focusflow/ui/viewmodel/FocusModeViewModel;", "onNavigateBack", "FocusModeSetup", "availableTasks", "selectedTask", "sessionDuration", "breakDuration", "onTaskSelected", "onSessionDurationChanged", "onBreakDurationChanged", "onStartSession", "PausedFocusSession", "onResume", "TaskSelectionCard", "isSelected", "onClick", "formatTime", "seconds", "app_debug"})
public final class FocusModeScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void FocusModeScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.FocusModeViewModel viewModel, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void FocusModeSetup(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Task> availableTasks, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.Task selectedTask, int sessionDuration, int breakDuration, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.Task, kotlin.Unit> onTaskSelected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onSessionDurationChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onBreakDurationChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStartSession, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TaskSelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, boolean isSelected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void DurationSelector(@org.jetbrains.annotations.NotNull
    java.lang.String label, int value, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onValueChange, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.Integer> options) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ActiveFocusSession(@org.jetbrains.annotations.Nullable
    com.focusflow.data.model.Task task, int timeRemaining, int totalTime, boolean isBreak, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onPause, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStop, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onCompleteTask) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CircularProgressTimer(float progress, int timeRemaining, boolean isBreak) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PausedFocusSession(@org.jetbrains.annotations.Nullable
    com.focusflow.data.model.Task task, int timeRemaining, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onResume, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onStop) {
    }
    
    private static final java.lang.String formatTime(int seconds) {
        return null;
    }
}