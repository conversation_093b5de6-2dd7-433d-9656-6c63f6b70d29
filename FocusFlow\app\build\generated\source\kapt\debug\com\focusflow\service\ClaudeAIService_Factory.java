package com.focusflow.service;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ClaudeAIService_Factory implements Factory<ClaudeAIService> {
  private final Provider<OkHttpClient> httpClientProvider;

  public ClaudeAIService_Factory(Provider<OkHttpClient> httpClientProvider) {
    this.httpClientProvider = httpClientProvider;
  }

  @Override
  public ClaudeAIService get() {
    return newInstance(httpClientProvider.get());
  }

  public static ClaudeAIService_Factory create(Provider<OkHttpClient> httpClientProvider) {
    return new ClaudeAIService_Factory(httpClientProvider);
  }

  public static ClaudeAIService newInstance(OkHttpClient httpClient) {
    return new ClaudeAIService(httpClient);
  }
}
