package com.focusflow.data.model;

/**
 * Marketing content extraction models
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BM\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u0012\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0003\u00a2\u0006\u0002\u0010\fJ\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\t0\u0003H\u00c6\u0003J\u000f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0003H\u00c6\u0003JY\u0010\u0018\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0003H\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001c\u001a\u00020\u001dH\u00d6\u0001J\t\u0010\u001e\u001a\u00020\u0006H\u00d6\u0001R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000eR\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000e\u00a8\u0006\u001f"}, d2 = {"Lcom/focusflow/data/model/MarketingContent;", "", "featureHighlights", "", "Lcom/focusflow/data/model/FeatureHighlight;", "userBenefits", "", "adhdSpecificBenefits", "screenshots", "Lcom/focusflow/data/model/MarketingScreenshot;", "testimonials", "Lcom/focusflow/data/model/UserTestimonial;", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V", "getAdhdSpecificBenefits", "()Ljava/util/List;", "getFeatureHighlights", "getScreenshots", "getTestimonials", "getUserBenefits", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class MarketingContent {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.FeatureHighlight> featureHighlights = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> userBenefits = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> adhdSpecificBenefits = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.MarketingScreenshot> screenshots = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.UserTestimonial> testimonials = null;
    
    public MarketingContent(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.FeatureHighlight> featureHighlights, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> userBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> adhdSpecificBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.MarketingScreenshot> screenshots, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.UserTestimonial> testimonials) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.FeatureHighlight> getFeatureHighlights() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getUserBenefits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getAdhdSpecificBenefits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.MarketingScreenshot> getScreenshots() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.UserTestimonial> getTestimonials() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.FeatureHighlight> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.MarketingScreenshot> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.UserTestimonial> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.MarketingContent copy(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.FeatureHighlight> featureHighlights, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> userBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> adhdSpecificBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.MarketingScreenshot> screenshots, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.UserTestimonial> testimonials) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}