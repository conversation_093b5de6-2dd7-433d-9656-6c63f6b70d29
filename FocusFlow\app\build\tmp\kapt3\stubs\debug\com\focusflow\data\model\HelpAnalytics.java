package com.focusflow.data.model;

/**
 * Analytics and feedback models
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0087\b\u0018\u00002\u00020\u0001B_\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u0005\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u0012\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\f\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0005H\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00030\fH\u00c6\u0003J\u000f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000e0\fH\u00c6\u0003Je\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u00052\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\fH\u00c6\u0001J\u0013\u0010%\u001a\u00020&2\b\u0010\'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020\u0005H\u00d6\u0001J\t\u0010)\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0013R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0013\u00a8\u0006*"}, d2 = {"Lcom/focusflow/data/model/HelpAnalytics;", "", "articleId", "", "viewCount", "", "completionRate", "", "averageTimeSpent", "helpfulVotes", "notHelpfulVotes", "commonExitPoints", "", "userFeedback", "Lcom/focusflow/data/model/HelpFeedback;", "(Ljava/lang/String;IFIIILjava/util/List;Ljava/util/List;)V", "getArticleId", "()Ljava/lang/String;", "getAverageTimeSpent", "()I", "getCommonExitPoints", "()Ljava/util/List;", "getCompletionRate", "()F", "getHelpfulVotes", "getNotHelpfulVotes", "getUserFeedback", "getViewCount", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class HelpAnalytics {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String articleId = null;
    private final int viewCount = 0;
    private final float completionRate = 0.0F;
    private final int averageTimeSpent = 0;
    private final int helpfulVotes = 0;
    private final int notHelpfulVotes = 0;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> commonExitPoints = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpFeedback> userFeedback = null;
    
    public HelpAnalytics(@org.jetbrains.annotations.NotNull
    java.lang.String articleId, int viewCount, float completionRate, int averageTimeSpent, int helpfulVotes, int notHelpfulVotes, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> commonExitPoints, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFeedback> userFeedback) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getArticleId() {
        return null;
    }
    
    public final int getViewCount() {
        return 0;
    }
    
    public final float getCompletionRate() {
        return 0.0F;
    }
    
    public final int getAverageTimeSpent() {
        return 0;
    }
    
    public final int getHelpfulVotes() {
        return 0;
    }
    
    public final int getNotHelpfulVotes() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getCommonExitPoints() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpFeedback> getUserFeedback() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpFeedback> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpAnalytics copy(@org.jetbrains.annotations.NotNull
    java.lang.String articleId, int viewCount, float completionRate, int averageTimeSpent, int helpfulVotes, int notHelpfulVotes, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> commonExitPoints, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFeedback> userFeedback) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}