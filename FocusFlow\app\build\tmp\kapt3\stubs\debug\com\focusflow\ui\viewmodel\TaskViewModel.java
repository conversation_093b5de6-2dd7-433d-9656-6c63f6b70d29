package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0000\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0012\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010,\u001a\u00020-J\u001c\u0010.\u001a\u00020-2\u0006\u0010/\u001a\u00020\u00152\f\u00100\u001a\b\u0012\u0004\u0012\u00020\r0\u0014J\u0006\u00101\u001a\u00020-J\u0006\u00102\u001a\u00020-J\u000e\u00103\u001a\u00020-2\u0006\u0010/\u001a\u00020\u0015JK\u00104\u001a\u00020-2\u0006\u00105\u001a\u00020\r2\b\b\u0002\u00106\u001a\u00020\r2\n\b\u0002\u00107\u001a\u0004\u0018\u0001082\b\b\u0002\u00109\u001a\u00020\r2\n\b\u0002\u0010:\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010;\u001a\u0004\u0018\u00010<\u00a2\u0006\u0002\u0010=J\u000e\u0010>\u001a\u00020-2\u0006\u0010/\u001a\u00020\u0015J\u000e\u0010?\u001a\u00020@2\u0006\u0010/\u001a\u00020\u0015J\u0015\u0010A\u001a\u00020\r2\b\u0010B\u001a\u0004\u0018\u00010<\u00a2\u0006\u0002\u0010CJ\u001b\u0010D\u001a\u00020E2\u0006\u00109\u001a\u00020\r\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bF\u0010GJ\u000e\u0010H\u001a\u00020\r2\u0006\u00109\u001a\u00020\rJ\f\u0010I\u001a\b\u0012\u0004\u0012\u00020\u00150\u0014J\f\u0010J\u001a\b\u0012\u0004\u0012\u00020\u00150\u0014J\u000e\u0010K\u001a\u00020\r2\u0006\u0010/\u001a\u00020\u0015J\u0014\u0010L\u001a\b\u0012\u0004\u0012\u00020\u00150\u00142\u0006\u0010M\u001a\u00020\rJ\b\u0010N\u001a\u00020-H\u0002J\b\u0010O\u001a\u00020-H\u0002J\u0006\u0010P\u001a\u00020-J\u000e\u0010Q\u001a\u00020-2\u0006\u0010/\u001a\u00020\u0015J\u000e\u0010R\u001a\u00020-2\u0006\u0010S\u001a\u00020\u000fJ\u000e\u0010T\u001a\u00020-2\u0006\u0010U\u001a\u00020\rJ\u000e\u0010V\u001a\u00020-2\u0006\u0010/\u001a\u00020\u0015R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u001d\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0017R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0017R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0017R\u001d\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0017R\u001d\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0017R\u001b\u0010#\u001a\f\u0012\b\u0012\u00060\rj\u0002`$0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0017R\u0017\u0010&\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u0017R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u0017R\u0017\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00110\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u0017R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006W"}, d2 = {"Lcom/focusflow/ui/viewmodel/TaskViewModel;", "Landroidx/lifecycle/ViewModel;", "taskRepository", "Lcom/focusflow/data/repository/TaskRepository;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "aiService", "Lcom/focusflow/service/AIService;", "gamificationService", "Lcom/focusflow/service/GamificationService;", "(Lcom/focusflow/data/repository/TaskRepository;Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/service/AIService;Lcom/focusflow/service/GamificationService;)V", "_searchQuery", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_selectedFilter", "Lcom/focusflow/ui/viewmodel/TaskFilter;", "_uiState", "Lcom/focusflow/ui/viewmodel/TaskUiState;", "allTasks", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/focusflow/data/model/Task;", "getAllTasks", "()Lkotlinx/coroutines/flow/StateFlow;", "completedTasks", "getCompletedTasks", "filteredTasks", "", "getFilteredTasks", "incompleteTasks", "getIncompleteTasks", "overdueTasks", "getOverdueTasks", "recommendedTasks", "getRecommendedTasks", "searchQuery", "Lcom/focusflow/ui/viewmodel/SearchQuery;", "getSearchQuery", "selectedFilter", "getSelectedFilter", "todaysTasks", "getTodaysTasks", "uiState", "getUiState", "acceptTaskBreakdown", "", "breakDownTask", "task", "subtasks", "clearError", "clearLastAction", "completeTask", "createTask", "title", "description", "dueDate", "Lkotlinx/datetime/LocalDateTime;", "priority", "category", "estimatedDuration", "", "(Ljava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "deleteTask", "detectProcrastination", "", "formatDuration", "minutes", "(Ljava/lang/Integer;)Ljava/lang/String;", "getPriorityColor", "Landroidx/compose/ui/graphics/Color;", "getPriorityColor-vNxB06k", "(Ljava/lang/String;)J", "getPriorityEmoji", "getProcrastinatedTasks", "getQuickWinTasks", "getTaskStatusMessage", "getTasksByEnergyLevel", "energyLevel", "loadTaskCategories", "loadTaskStatistics", "rejectTaskBreakdown", "requestTaskBreakdown", "setFilter", "filter", "setSearchQuery", "query", "updateTask", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class TaskViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.TaskRepository taskRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.AIService aiService = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.GamificationService gamificationService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.TaskUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.TaskFilter> _selectedFilter = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskFilter> selectedFilter = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _searchQuery = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> searchQuery = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> allTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> incompleteTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> completedTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> todaysTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> overdueTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> recommendedTasks = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Object> filteredTasks = null;
    
    @javax.inject.Inject
    public TaskViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.TaskRepository taskRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.AIService aiService, @org.jetbrains.annotations.NotNull
    com.focusflow.service.GamificationService gamificationService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.TaskFilter> getSelectedFilter() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getSearchQuery() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getAllTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getIncompleteTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getCompletedTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getTodaysTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getOverdueTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.focusflow.data.model.Task>> getRecommendedTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Object> getFilteredTasks() {
        return null;
    }
    
    private final void loadTaskStatistics() {
    }
    
    private final void loadTaskCategories() {
    }
    
    public final void createTask(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime dueDate, @org.jetbrains.annotations.NotNull
    java.lang.String priority, @org.jetbrains.annotations.Nullable
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.Integer estimatedDuration) {
    }
    
    public final void completeTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void deleteTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void updateTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void breakDownTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> subtasks) {
    }
    
    public final void setFilter(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.TaskFilter filter) {
    }
    
    public final void setSearchQuery(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
    }
    
    public final void clearError() {
    }
    
    public final void clearLastAction() {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPriorityEmoji(@org.jetbrains.annotations.NotNull
    java.lang.String priority) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatDuration(@org.jetbrains.annotations.Nullable
    java.lang.Integer minutes) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTaskStatusMessage(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
        return null;
    }
    
    public final void requestTaskBreakdown(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void acceptTaskBreakdown() {
    }
    
    public final void rejectTaskBreakdown() {
    }
    
    public final boolean detectProcrastination(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.Task> getTasksByEnergyLevel(@org.jetbrains.annotations.NotNull
    java.lang.String energyLevel) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.Task> getProcrastinatedTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.Task> getQuickWinTasks() {
        return null;
    }
}