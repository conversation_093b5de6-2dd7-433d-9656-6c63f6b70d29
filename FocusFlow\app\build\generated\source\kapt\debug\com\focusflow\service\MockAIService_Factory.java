package com.focusflow.service;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MockAIService_Factory implements Factory<MockAIService> {
  @Override
  public MockAIService get() {
    return newInstance();
  }

  public static MockAIService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockAIService newInstance() {
    return new MockAIService();
  }

  private static final class InstanceHolder {
    private static final MockAIService_Factory INSTANCE = new MockAIService_Factory();
  }
}
