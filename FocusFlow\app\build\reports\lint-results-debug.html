<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 6 errors, 200 warnings and 5 hints</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Tue Jun 17 22:22:53 AEST 2025 by AGP (8.10.1)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#MissingPermission"><i class="material-icons error-icon">error</i>Missing Permissions (2)</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (82)</a>
      <a class="mdl-navigation__link" href="#Instantiatable"><i class="material-icons error-icon">error</i>Registered class is not instantiatable (1)</a>
      <a class="mdl-navigation__link" href="#NewApi"><i class="material-icons error-icon">error</i>Calling new methods on older versions (2)</a>
      <a class="mdl-navigation__link" href="#OldTargetApi"><i class="material-icons warning-icon">warning</i>Target SDK attribute is not targeting latest version (1)</a>
      <a class="mdl-navigation__link" href="#ScheduleExactAlarm"><i class="material-icons error-icon">error</i>Scheduling Exact Alarms Without Required Permission (1)</a>
      <a class="mdl-navigation__link" href="#RedundantLabel"><i class="material-icons warning-icon">warning</i>Redundant label on activity (1)</a>
      <a class="mdl-navigation__link" href="#SelectedPhotoAccess"><i class="material-icons warning-icon">warning</i>Behavior change when requesting photo library access (2)</a>
      <a class="mdl-navigation__link" href="#AndroidGradlePluginVersion"><i class="material-icons warning-icon">warning</i>Obsolete Android Gradle Plugin Version (1)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (24)</a>
      <a class="mdl-navigation__link" href="#ComposableModifierFactory"><i class="material-icons warning-icon">warning</i>Modifier factory functions should not be @Composable (1)</a>
      <a class="mdl-navigation__link" href="#ModifierFactoryExtensionFunction"><i class="material-icons warning-icon">warning</i>Modifier factory functions should be extensions on Modifier (1)</a>
      <a class="mdl-navigation__link" href="#ModifierParameter"><i class="material-icons warning-icon">warning</i>Guidelines for Modifier parameters in a Composable function (3)</a>
      <a class="mdl-navigation__link" href="#MutableCollectionMutableState"><i class="material-icons warning-icon">warning</i>Creating a MutableState object with a mutable collection type (2)</a>
      <a class="mdl-navigation__link" href="#DiscouragedApi"><i class="material-icons warning-icon">warning</i>Using discouraged APIs (2)</a>
      <a class="mdl-navigation__link" href="#KaptUsageInsteadOfKsp"><i class="material-icons warning-icon">warning</i>Kapt usage should be replaced with KSP (1)</a>
      <a class="mdl-navigation__link" href="#AutoboxingStateCreation"><i class="material-icons warning-icon">warning</i><code>State&lt;T></code> will autobox values assigned to this state. Use a specialized state type instead. (5)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (73)</a>
      <a class="mdl-navigation__link" href="#UseKtx"><i class="material-icons warning-icon">warning</i>Use KTX extension function (6)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingPermission">MissingPermission</a>: Missing Permissions</td></tr>
<tr>
<td class="countColumn">82</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#Instantiatable">Instantiatable</a>: Registered class is not instantiatable</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#NewApi">NewApi</a>: Calling new methods on older versions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#OldTargetApi">OldTargetApi</a>: Target SDK attribute is not targeting latest version</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#ScheduleExactAlarm">ScheduleExactAlarm</a>: Scheduling Exact Alarms Without Required Permission</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RedundantLabel">RedundantLabel</a>: Redundant label on activity</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SelectedPhotoAccess">SelectedPhotoAccess</a>: Behavior change when requesting photo library access</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AndroidGradlePluginVersion">AndroidGradlePluginVersion</a>: Obsolete Android Gradle Plugin Version</td></tr>
<tr>
<td class="countColumn">24</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ComposableModifierFactory">ComposableModifierFactory</a>: Modifier factory functions should not be @Composable</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ModifierFactoryExtensionFunction">ModifierFactoryExtensionFunction</a>: Modifier factory functions should be extensions on Modifier</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ModifierParameter">ModifierParameter</a>: Guidelines for Modifier parameters in a Composable function</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#MutableCollectionMutableState">MutableCollectionMutableState</a>: Creating a MutableState object with a mutable collection type</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DiscouragedApi">DiscouragedApi</a>: Using discouraged APIs</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#KaptUsageInsteadOfKsp">KaptUsageInsteadOfKsp</a>: Kapt usage should be replaced with KSP</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AutoboxingStateCreation">AutoboxingStateCreation</a>: <code>State&lt;T></code> will autobox values assigned to this state. Use a specialized state type instead.</td></tr>
<tr>
<td class="countColumn">73</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Productivity">Productivity</a>
</td></tr>
<tr>
<td class="countColumn">6</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseKtx">UseKtx</a>: Use KTX extension function</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (83)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (41)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="MissingPermission"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingPermissionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing Permissions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/receiver/AlarmReceiver.kt">../../src/main/java/com/focusflow/receiver/AlarmReceiver.kt</a>:78</span>: <span class="message">Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with <code>checkPermission</code>) or explicitly handle a potential <code>SecurityException</code></span><br /><pre class="errorlines">
<span class="lineno">  75 </span>          .build()
<span class="lineno">  76 </span>
<span class="lineno">  77 </span>      <span class="keyword">val</span> notificationManager = NotificationManagerCompat.from(context)
<span class="caretline"><span class="lineno">  78 </span>      <span class="error">notificationManager.notify(notificationId, notification)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  79 </span>  }
<span class="lineno">  80 </span>
<span class="lineno">  81 </span>  private <span class="keyword">fun</span> NotificationCompat.Builder.addQuickActions(context: Context, type: String): NotificationCompat.Builder {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/NotificationManager.kt">../../src/main/java/com/focusflow/service/NotificationManager.kt</a>:281</span>: <span class="message">Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with <code>checkPermission</code>) or explicitly handle a potential <code>SecurityException</code></span><br /><pre class="errorlines">
<span class="lineno"> 278 </span>            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
<span class="lineno"> 279 </span>            .build()
<span class="lineno"> 280 </span>        
<span class="caretline"><span class="lineno"> 281 </span>        <span class="error">notificationManager.notify(notificationId, notification)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 282 </span>    }
<span class="lineno"> 283 </span>    
<span class="lineno"> 284 </span>    <span class="comment">// Cancel specific notifications</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMissingPermission" style="display: none;">
This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.<br/>
<br/>
Furthermore, for permissions that are revocable (with <code>targetSdkVersion</code> 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingPermission</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 9/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingPermissionLink" onclick="reveal('explanationMissingPermission');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingPermissionCardLink" onclick="hideid('MissingPermissionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/utils/ADHDDesignValidator.kt">../../src/main/java/com/focusflow/utils/ADHDDesignValidator.kt</a>:134</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 131 </span>  
<span class="lineno"> 132 </span>  <span class="keyword">when</span> {
<span class="lineno"> 133 </span>      contrastRatio &lt; Standards.MIN_CONTRAST_RATIO -> {
<span class="caretline"><span class="lineno"> 134 </span>          issues.add(<span class="string">"Insufficient contrast ratio: ${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, contrastRatio)</span><span class="string">} &lt; ${</span>Standards.MIN_CONTRAST_RATIO<span class="string">}"</span>)</span>
<span class="lineno"> 135 </span>          recommendations.add(<span class="string">"Increase contrast to at least ${</span>Standards.MIN_CONTRAST_RATIO<span class="string">}:1"</span>)
<span class="lineno"> 136 </span>      }
<span class="lineno"> 137 </span>      contrastRatio &lt; Standards.RECOMMENDED_CONTRAST_RATIO -> {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.kt">../../src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.kt</a>:155</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 152 </span><span class="string">            📊 **Your Spending Analysis**
</span><span class="lineno"> 153 </span><span class="string">
</span><span class="lineno"> 154 </span><span class="string">            Based on your recent activity:
</span><span class="caretline"><span class="lineno"> 155 </span><span class="string">            • Weekly budget: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, weeklyBudget)</span><span class="string">}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="string">
</span><span class="lineno"> 156 </span><span class="string">            • You're doing great at tracking expenses!
</span><span class="lineno"> 157 </span><span class="string">            • Consider setting up categories for better insights
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:119</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span><span class="string">  📊 **Your Spending Analysis**
</span><span class="lineno"> 117 </span><span class="string">  
</span><span class="lineno"> 118 </span><span class="string">  **This Week's Summary:**
</span><span class="caretline"><span class="lineno"> 119 </span><span class="string">  • Total Spent: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, totalSpent)</span><span class="string">}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="string">
</span><span class="lineno"> 120 </span><span class="string">  • Transactions: ${</span>expenses.size<span class="string">}
</span><span class="lineno"> 121 </span><span class="string">  • Top Category: ${</span>topCategory?.key ?: <span class="string">"None"} ($${</span>String.format(<span class="string">"%.2f"</span>, topCategory?.value ?: <span class="number">0.0</span>)<span class="string">})
</span><span class="lineno"> 122 </span><span class="string">  
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:121</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span><span class="string">  **This Week's Summary:**
</span><span class="lineno"> 119 </span><span class="string">  • Total Spent: $${</span>String.format(<span class="string">"%.2f"</span>, totalSpent)<span class="string">}
</span><span class="lineno"> 120 </span><span class="string">  • Transactions: ${</span>expenses.size<span class="string">}
</span><span class="caretline"><span class="lineno"> 121 </span><span class="string">  • Top Category: ${</span>topCategory?.key ?: <span class="string">"None"} ($${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, topCategory?.value ?: <span class="number">0.0</span>)</span><span class="string">})</span></span><span class="string">
</span><span class="lineno"> 122 </span><span class="string">  
</span><span class="lineno"> 123 </span><span class="string">  **ADHD-Friendly Insights:**
</span><span class="lineno"> 124 </span><span class="string">  ${</span><span class="keyword">if</span> (totalSpent > (budget?.weeklyAmount ?: <span class="number">300.0</span>)) {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:127</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 124 </span><span class="string">  ${</span><span class="keyword">if</span> (totalSpent > (budget?.weeklyAmount ?: <span class="number">300.0</span>)) {
<span class="lineno"> 125 </span>      <span class="string">"⚠️ You're over your weekly budget. Try the '24-hour rule' for non-essential purchases."</span>
<span class="lineno"> 126 </span>  } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 127 </span>      <span class="string">"✅ Great job staying within budget! You have $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, (budget?.weeklyAmount ?: <span class="number">300.0</span>) - totalSpent)</span><span class="string">} left this week."</span></span>
<span class="lineno"> 128 </span>  }<span class="string">}
</span><span class="lineno"> 129 </span><span class="string">  
</span><span class="lineno"> 130 </span><span class="string">  **Quick Tips:**
</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="DefaultLocaleDivLink" onclick="reveal('DefaultLocaleDiv');" />+ 77 More Occurrences...</button>
<div id="DefaultLocaleDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:175</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 172 </span><span class="string">            📊 **Budget Optimization Suggestions**
</span><span class="lineno"> 173 </span><span class="string">            
</span><span class="lineno"> 174 </span><span class="string">            **Current Performance:**
</span><span class="caretline"><span class="lineno"> 175 </span><span class="string">            • Weekly Budget: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, currentBudget.weeklyAmount)</span><span class="string">}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="string">
</span><span class="lineno"> 176 </span><span class="string">            • Average Spending: $${</span>String.format(<span class="string">"%.2f"</span>, avgSpending)<span class="string">}
</span><span class="lineno"> 177 </span><span class="string">            
</span><span class="lineno"> 178 </span><span class="string">            **Recommendations:**
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:176</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 173 </span><span class="string">            
</span><span class="lineno"> 174 </span><span class="string">            **Current Performance:**
</span><span class="lineno"> 175 </span><span class="string">            • Weekly Budget: $${</span>String.format(<span class="string">"%.2f"</span>, currentBudget.weeklyAmount)<span class="string">}
</span><span class="caretline"><span class="lineno"> 176 </span><span class="string">            • Average Spending: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, avgSpending)</span><span class="string">}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="string">
</span><span class="lineno"> 177 </span><span class="string">            
</span><span class="lineno"> 178 </span><span class="string">            **Recommendations:**
</span><span class="lineno"> 179 </span><span class="string">            ${</span><span class="keyword">if</span> (avgSpending > currentBudget.weeklyAmount) {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:238</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 235 </span><span class="string">            💳 **Your Debt Analysis**
</span><span class="lineno"> 236 </span><span class="string">            
</span><span class="lineno"> 237 </span><span class="string">            **Current Situation:**
</span><span class="caretline"><span class="lineno"> 238 </span><span class="string">            • Total Debt: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, totalDebt)</span><span class="string">}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="string">
</span><span class="lineno"> 239 </span><span class="string">            • Monthly Minimums: $${</span>String.format(<span class="string">"%.2f"</span>, totalMinPayments)<span class="string">}
</span><span class="lineno"> 240 </span><span class="string">            • Highest Interest Rate: ${</span>String.format(<span class="string">"%.1f"</span>, highestRate)<span class="string">}%
</span><span class="lineno"> 241 </span><span class="string">            
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:239</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 236 </span><span class="string">            
</span><span class="lineno"> 237 </span><span class="string">            **Current Situation:**
</span><span class="lineno"> 238 </span><span class="string">            • Total Debt: $${</span>String.format(<span class="string">"%.2f"</span>, totalDebt)<span class="string">}
</span><span class="caretline"><span class="lineno"> 239 </span><span class="string">            • Monthly Minimums: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, totalMinPayments)</span><span class="string">}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="string">
</span><span class="lineno"> 240 </span><span class="string">            • Highest Interest Rate: ${</span>String.format(<span class="string">"%.1f"</span>, highestRate)<span class="string">}%
</span><span class="lineno"> 241 </span><span class="string">            
</span><span class="lineno"> 242 </span><span class="string">            **ADHD-Friendly Strategy Recommendations:**
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:240</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 237 </span><span class="string">            **Current Situation:**
</span><span class="lineno"> 238 </span><span class="string">            • Total Debt: $${</span>String.format(<span class="string">"%.2f"</span>, totalDebt)<span class="string">}
</span><span class="lineno"> 239 </span><span class="string">            • Monthly Minimums: $${</span>String.format(<span class="string">"%.2f"</span>, totalMinPayments)<span class="string">}
</span><span class="caretline"><span class="lineno"> 240 </span><span class="string">            • Highest Interest Rate: ${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, highestRate)</span><span class="string">}%</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="string">
</span><span class="lineno"> 241 </span><span class="string">            
</span><span class="lineno"> 242 </span><span class="string">            **ADHD-Friendly Strategy Recommendations:**
</span><span class="lineno"> 243 </span><span class="string">            
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:245</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 242 </span><span class="string">  **ADHD-Friendly Strategy Recommendations:**
</span><span class="lineno"> 243 </span><span class="string">  
</span><span class="lineno"> 244 </span><span class="string">  **🎯 Snowball Method (Motivation-focused):**
</span><span class="caretline"><span class="lineno"> 245 </span><span class="string">  Start with: ${</span>smallestDebt?.name<span class="string">} ($${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, smallestDebt?.balance ?: <span class="number">0.0</span>)</span><span class="string">})</span></span><span class="string">
</span><span class="lineno"> 246 </span><span class="string">  • Quick wins build momentum
</span><span class="lineno"> 247 </span><span class="string">  • Great for ADHD motivation
</span><span class="lineno"> 248 </span><span class="string">  • Celebrate each payoff! 🎉
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AIService.kt">../../src/main/java/com/focusflow/service/AIService.kt</a>:251</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 248 </span><span class="string">  • Celebrate each payoff! 🎉
</span><span class="lineno"> 249 </span><span class="string">  
</span><span class="lineno"> 250 </span><span class="string">  **📊 Avalanche Method (Math-focused):**
</span><span class="caretline"><span class="lineno"> 251 </span><span class="string">  Start with: ${</span>highestRateCard?.name<span class="string">} (${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, highestRateCard?.interestRate ?: <span class="number">0.0</span>)</span><span class="string">}% APR)</span></span><span class="string">
</span><span class="lineno"> 252 </span><span class="string">  • Saves more money long-term
</span><span class="lineno"> 253 </span><span class="string">  • Better if you're motivated by numbers
</span><span class="lineno"> 254 </span><span class="string">  
</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AdvancedAnalyticsService.kt">../../src/main/java/com/focusflow/service/AdvancedAnalyticsService.kt</a>:390</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 387 </span>  BehaviorInsight(
<span class="lineno"> 388 </span>      insightType = <span class="string">"spending_pattern"</span>,
<span class="lineno"> 389 </span>      title = <span class="string">"Weekend Spending Spike"</span>,
<span class="caretline"><span class="lineno"> 390 </span>      description = <span class="string">"You spend significantly more on weekends (${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, weekendAvg)</span><span class="string">}) compared to weekdays (${</span>String.format(<span class="string">"%.2f"</span>, weekdayAvg)<span class="string">})"</span>,</span>
<span class="lineno"> 391 </span>      impact = <span class="string">"medium"</span>,
<span class="lineno"> 392 </span>      actionable = <span class="keyword">true</span>,
<span class="lineno"> 393 </span>      suggestedActions = listOf(
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/AdvancedAnalyticsService.kt">../../src/main/java/com/focusflow/service/AdvancedAnalyticsService.kt</a>:390</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 387 </span>  BehaviorInsight(
<span class="lineno"> 388 </span>      insightType = <span class="string">"spending_pattern"</span>,
<span class="lineno"> 389 </span>      title = <span class="string">"Weekend Spending Spike"</span>,
<span class="caretline"><span class="lineno"> 390 </span>      description = <span class="string">"You spend significantly more on weekends (${</span>String.format(<span class="string">"%.2f"</span>, weekendAvg)<span class="string">}) compared to weekdays (${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, weekdayAvg)</span><span class="string">})"</span>,</span>
<span class="lineno"> 391 </span>      impact = <span class="string">"medium"</span>,
<span class="lineno"> 392 </span>      actionable = <span class="keyword">true</span>,
<span class="lineno"> 393 </span>      suggestedActions = listOf(
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/BudgetRecommendationService.kt">../../src/main/java/com/focusflow/service/BudgetRecommendationService.kt</a>:191</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 188 </span>  
<span class="lineno"> 189 </span>  <span class="keyword">return</span> <span class="keyword">when</span> {
<span class="lineno"> 190 </span>      percentageDifference > <span class="number">0.3</span> -> {
<span class="caretline"><span class="lineno"> 191 </span>          <span class="string">"spending_increase"</span> to <span class="string">"Your spending in this category has increased significantly. Consider increasing your budget by ${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, abs(difference))</span><span class="string">}."</span></span>
<span class="lineno"> 192 </span>      }
<span class="lineno"> 193 </span>      percentageDifference &lt; -<span class="number">0.3</span> -> {
<span class="lineno"> 194 </span>          <span class="string">"spending_decrease"</span> to <span class="string">"You've been spending less in this category. You could reduce your budget by ${</span>String.format(<span class="string">"%.0f"</span>, abs(difference))<span class="string">}."</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/service/BudgetRecommendationService.kt">../../src/main/java/com/focusflow/service/BudgetRecommendationService.kt</a>:194</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 191 </span>      <span class="string">"spending_increase"</span> to <span class="string">"Your spending in this category has increased significantly. Consider increasing your budget by ${</span>String.format(<span class="string">"%.0f"</span>, abs(difference))<span class="string">}."</span>
<span class="lineno"> 192 </span>  }
<span class="lineno"> 193 </span>  percentageDifference &lt; -<span class="number">0.3</span> -> {
<span class="caretline"><span class="lineno"> 194 </span>      <span class="string">"spending_decrease"</span> to <span class="string">"You've been spending less in this category. You could reduce your budget by ${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, abs(difference))</span><span class="string">}."</span></span>
<span class="lineno"> 195 </span>  }
<span class="lineno"> 196 </span>  analysisData.trendFactor > <span class="number">1.2</span> -> {
<span class="lineno"> 197 </span>      <span class="string">"increasing_trend"</span> to <span class="string">"Your spending shows an increasing trend. Consider adjusting your budget to match this pattern."</span></pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DashboardScreen.kt">../../src/main/java/com/focusflow/ui/screens/DashboardScreen.kt</a>:163</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 160 </span>                )
<span class="lineno"> 161 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 162 </span>                Text(
<span class="caretline"><span class="lineno"> 163 </span>                    text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, safeToSpend)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 164 </span>                    style = MaterialTheme.typography.h3,
<span class="lineno"> 165 </span>                    fontWeight = FontWeight.Bold,
<span class="lineno"> 166 </span>                    color = <span class="keyword">when</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DashboardScreen.kt">../../src/main/java/com/focusflow/ui/screens/DashboardScreen.kt</a>:213</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 210 </span>      color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 211 </span>  )
<span class="lineno"> 212 </span>  Text(
<span class="caretline"><span class="lineno"> 213 </span>      text = <span class="keyword">if</span> (totalDebt > <span class="number">0</span>) <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, totalDebt)</span><span class="string">}"</span> <span class="keyword">else</span> <span class="string">"No debt"</span>,</span>
<span class="lineno"> 214 </span>      style = MaterialTheme.typography.h6,
<span class="lineno"> 215 </span>      fontWeight = FontWeight.Bold
<span class="lineno"> 216 </span>  )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DashboardScreen.kt">../../src/main/java/com/focusflow/ui/screens/DashboardScreen.kt</a>:225</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 222 </span>      color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 223 </span>  )
<span class="lineno"> 224 </span>  Text(
<span class="caretline"><span class="lineno"> 225 </span>      text = <span class="keyword">if</span> (nextPayment > <span class="number">0</span>) <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, nextPayment)</span><span class="string">}"</span> <span class="keyword">else</span> <span class="string">"None due"</span>,</span>
<span class="lineno"> 226 </span>      style = MaterialTheme.typography.h6,
<span class="lineno"> 227 </span>      fontWeight = FontWeight.Bold,
<span class="lineno"> 228 </span>      color = <span class="keyword">if</span> (nextPayment > <span class="number">0</span>) Color(<span class="number">0xFFFF9800</span>) <span class="keyword">else</span> MaterialTheme.colors.onSurface
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:287</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 284 </span>            )
<span class="lineno"> 285 </span>            Spacer(modifier = Modifier.height(<span class="number">4.d</span>p))
<span class="lineno"> 286 </span>            Text(
<span class="caretline"><span class="lineno"> 287 </span>                text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, amount)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 288 </span>                style = MaterialTheme.typography.h6,
<span class="lineno"> 289 </span>                fontWeight = FontWeight.Bold,
<span class="lineno"> 290 </span>                color = color
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:362</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 359 </span>                color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 360 </span>            )
<span class="lineno"> 361 </span>            Text(
<span class="caretline"><span class="lineno"> 362 </span>                text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, creditCard.currentBalance)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 363 </span>                style = MaterialTheme.typography.body1,
<span class="lineno"> 364 </span>                fontWeight = FontWeight.Medium
<span class="lineno"> 365 </span>            )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:375</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 372 </span>                   color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 373 </span>               )
<span class="lineno"> 374 </span>               Text(
<span class="caretline"><span class="lineno"> 375 </span>                   text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, creditCard.creditLimit)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 376 </span>                   style = MaterialTheme.typography.body1,
<span class="lineno"> 377 </span>                   fontWeight = FontWeight.Medium
<span class="lineno"> 378 </span>               )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:397</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 394 </span>                     color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 395 </span>                 )
<span class="lineno"> 396 </span>                 Text(
<span class="caretline"><span class="lineno"> 397 </span>                     text = <span class="string">"${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, utilization * <span class="number">100</span>)</span><span class="string">}%"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 398 </span>                     style = MaterialTheme.typography.caption,
<span class="lineno"> 399 </span>                     color = <span class="keyword">when</span> {
<span class="lineno"> 400 </span>                         utilization > <span class="number">0.9</span> -> Color(<span class="number">0xFFF44336</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:431</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 428 </span>  ) {
<span class="lineno"> 429 </span>      Column {
<span class="lineno"> 430 </span>          Text(
<span class="caretline"><span class="lineno"> 431 </span>              text = <span class="string">"Min Payment: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, creditCard.minimumPayment)</span><span class="string">}"</span>,&nbsp;</span>
<span class="lineno"> 432 </span>              style = MaterialTheme.typography.body2
<span class="lineno"> 433 </span>          )
<span class="lineno"> 434 </span>          Text(
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:787</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 784 </span>      fontWeight = FontWeight.Medium
<span class="lineno"> 785 </span>  )
<span class="lineno"> 786 </span>  Text(
<span class="caretline"><span class="lineno"> 787 </span>      text = <span class="string">"Current Balance: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, creditCard.currentBalance)</span><span class="string">}"</span>,&nbsp;</span>
<span class="lineno"> 788 </span>      style = MaterialTheme.typography.body2,
<span class="lineno"> 789 </span>      color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 790 </span>  )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:809</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 806 </span>      horizontalArrangement = Arrangement.SpaceBetween
<span class="lineno"> 807 </span>  ) {
<span class="lineno"> 808 </span>      TextButton(
<span class="caretline"><span class="lineno"> 809 </span>          onClick = { paymentAmount = <span class="warning">String.format(<span class="string">"%.2f"</span>, creditCard.minimumPayment)</span> }</span>
<span class="lineno"> 810 </span>      ) {
<span class="lineno"> 811 </span>          Text(<span class="string">"Min Payment"</span>)
<span class="lineno"> 812 </span>      }
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt">../../src/main/java/com/focusflow/ui/screens/DebtScreen.kt</a>:814</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 811 </span>      Text(<span class="string">"Min Payment"</span>)
<span class="lineno"> 812 </span>  }
<span class="lineno"> 813 </span>  TextButton(
<span class="caretline"><span class="lineno"> 814 </span>      onClick = { paymentAmount = <span class="warning">String.format(<span class="string">"%.2f"</span>, creditCard.currentBalance)</span> }</span>
<span class="lineno"> 815 </span>  ) {
<span class="lineno"> 816 </span>      Text(<span class="string">"Pay Full"</span>)
<span class="lineno"> 817 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt</a>:142</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>             color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 140 </span>         )
<span class="lineno"> 141 </span>         Text(
<span class="caretline"><span class="lineno"> 142 </span>             text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, recommendation.currentAmount)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>             style = MaterialTheme.typography.h6,
<span class="lineno"> 144 </span>             fontWeight = FontWeight.Bold
<span class="lineno"> 145 </span>         )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt</a>:162</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 159 </span>         color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 160 </span>     )
<span class="lineno"> 161 </span>     Text(
<span class="caretline"><span class="lineno"> 162 </span>         text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, recommendation.recommendedAmount)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 163 </span>         style = MaterialTheme.typography.h6,
<span class="lineno"> 164 </span>         fontWeight = FontWeight.Bold,
<span class="lineno"> 165 </span>         color = Color(<span class="number">0xFF9C27B0</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt</a>:298</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 295 </span>      horizontalAlignment = Alignment.End
<span class="lineno"> 296 </span>  ) {
<span class="lineno"> 297 </span>      Text(
<span class="caretline"><span class="lineno"> 298 </span>          text = <span class="string">"${</span><span class="keyword">if</span> (isOverBudget) <span class="string">"+"</span> <span class="keyword">else</span> <span class="string">""}${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, variance)</span><span class="string">}"</span>,</span>
<span class="lineno"> 299 </span>          style = MaterialTheme.typography.h6,
<span class="lineno"> 300 </span>          fontWeight = FontWeight.Bold,
<span class="lineno"> 301 </span>          color = alertColor
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.kt</a>:304</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 301 </span>                  color = alertColor
<span class="lineno"> 302 </span>              )
<span class="lineno"> 303 </span>              Text(
<span class="caretline"><span class="lineno"> 304 </span>                  text = <span class="string">"${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, abs(variancePercentage))</span><span class="string">}%"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 305 </span>                  style = MaterialTheme.typography.caption,
<span class="lineno"> 306 </span>                  color = alertColor
<span class="lineno"> 307 </span>              )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt">../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt</a>:244</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 241 </span>   
<span class="lineno"> 242 </span>   <span class="keyword">if</span> (unallocatedAmount > <span class="number">0</span>) {
<span class="lineno"> 243 </span>       Text(
<span class="caretline"><span class="lineno"> 244 </span>           text = <span class="string">"You have $${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, unallocatedAmount)</span><span class="string">} unallocated"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 245 </span>           style = MaterialTheme.typography.body2,
<span class="lineno"> 246 </span>           color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 247 </span>       )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt">../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt</a>:512</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 509 </span>  text = {
<span class="lineno"> 510 </span>      Column {
<span class="lineno"> 511 </span>          Text(
<span class="caretline"><span class="lineno"> 512 </span>              text = <span class="string">"Allocate your $${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, unallocatedAmount)</span><span class="string">} across these categories:"</span>,</span>
<span class="lineno"> 513 </span>              style = MaterialTheme.typography.body2,
<span class="lineno"> 514 </span>              color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 515 </span>          )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt">../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt</a>:547</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 544 </span>         <span class="keyword">val</span> remaining = unallocatedAmount - totalAllocated
<span class="lineno"> 545 </span>
<span class="lineno"> 546 </span>         Text(
<span class="caretline"><span class="lineno"> 547 </span>             text = <span class="string">"Remaining: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, remaining)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 548 </span>             style = MaterialTheme.typography.caption,
<span class="lineno"> 549 </span>             color = <span class="keyword">if</span> (remaining >= <span class="number">0</span>) Color(<span class="number">0xFF4CAF50</span>) <span class="keyword">else</span> Color(<span class="number">0xFFF44336</span>),
<span class="lineno"> 550 </span>             fontWeight = FontWeight.Medium
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:123</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>                modifier = Modifier.fillMaxWidth()
<span class="lineno"> 121 </span>            ) {
<span class="lineno"> 122 </span>                Text(
<span class="caretline"><span class="lineno"> 123 </span>                    text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, remaining)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>                    style = MaterialTheme.typography.h5,
<span class="lineno"> 125 </span>                    fontWeight = FontWeight.Bold,
<span class="lineno"> 126 </span>                    color = <span class="keyword">if</span> (remaining >= <span class="number">0</span>) statusColor <span class="keyword">else</span> Color(<span class="number">0xFFF44336</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:142</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>                    horizontalArrangement = Arrangement.SpaceBetween
<span class="lineno"> 140 </span>                ) {
<span class="lineno"> 141 </span>                    Text(
<span class="caretline"><span class="lineno"> 142 </span>                        text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, category.spentAmount)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>                        style = MaterialTheme.typography.caption,
<span class="lineno"> 144 </span>                        color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 145 </span>                    )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:147</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 144 </span>                 color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 145 </span>             )
<span class="lineno"> 146 </span>             Text(
<span class="caretline"><span class="lineno"> 147 </span>                 text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, category.allocatedAmount)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 148 </span>                 style = MaterialTheme.typography.caption,
<span class="lineno"> 149 </span>                 color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 150 </span>             )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:169</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 166 </span>            <span class="comment">// Status indicator</span>
<span class="lineno"> 167 </span>            <span class="keyword">if</span> (spentPercentage > <span class="number">1.0</span>) {
<span class="lineno"> 168 </span>                Text(
<span class="caretline"><span class="lineno"> 169 </span>                    text = <span class="string">"Over by $${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, -remaining)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 170 </span>                    style = MaterialTheme.typography.caption,
<span class="lineno"> 171 </span>                    color = Color(<span class="number">0xFFF44336</span>),
<span class="lineno"> 172 </span>                    textAlign = TextAlign.Center,
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:257</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 254 </span>  Text(
<span class="lineno"> 255 </span>      text = <span class="keyword">when</span> {
<span class="lineno"> 256 </span>          isBalanced -> <span class="string">"✅ Budget is perfectly balanced!"</span>
<span class="caretline"><span class="lineno"> 257 </span>          unallocatedAmount > <span class="number">0</span> -> <span class="string">"💰 You have $${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, unallocatedAmount)</span><span class="string">} to allocate"</span></span>
<span class="lineno"> 258 </span>          <span class="keyword">else</span> -> <span class="string">"⚠️ You've over-allocated by $${</span>String.format(<span class="string">"%.0f"</span>, abs(unallocatedAmount))<span class="string">}"</span>
<span class="lineno"> 259 </span>      },
<span class="lineno"> 260 </span>      style = MaterialTheme.typography.body2,
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:258</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 255 </span>  text = <span class="keyword">when</span> {
<span class="lineno"> 256 </span>      isBalanced -> <span class="string">"✅ Budget is perfectly balanced!"</span>
<span class="lineno"> 257 </span>      unallocatedAmount > <span class="number">0</span> -> <span class="string">"💰 You have $${</span>String.format(<span class="string">"%.0f"</span>, unallocatedAmount)<span class="string">} to allocate"</span>
<span class="caretline"><span class="lineno"> 258 </span>      <span class="keyword">else</span> -> <span class="string">"⚠️ You've over-allocated by $${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, abs(unallocatedAmount))</span><span class="string">}"</span></span>
<span class="lineno"> 259 </span>  },
<span class="lineno"> 260 </span>  style = MaterialTheme.typography.body2,
<span class="lineno"> 261 </span>  color = <span class="keyword">when</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:296</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 293 </span>            color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 294 </span>        )
<span class="lineno"> 295 </span>        Text(
<span class="caretline"><span class="lineno"> 296 </span>            text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, amount)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 297 </span>            style = MaterialTheme.typography.subtitle1,
<span class="lineno"> 298 </span>            fontWeight = FontWeight.Bold,
<span class="lineno"> 299 </span>            color = color
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:335</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 332 </span>  
<span class="lineno"> 333 </span>  Text(
<span class="lineno"> 334 </span>      text = <span class="keyword">if</span> (currentIncome > <span class="number">0</span>) {
<span class="caretline"><span class="lineno"> 335 </span>          <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.0f"</span>, currentIncome)</span><span class="string">} ${</span>period.replaceFirstChar { it.lowercase() }<span class="string">}"</span></span>
<span class="lineno"> 336 </span>      } <span class="keyword">else</span> {
<span class="lineno"> 337 </span>          <span class="string">"Set Your ${</span>period.replaceFirstChar { it.titlecase() }<span class="string">} Income"</span>
<span class="lineno"> 338 </span>      },
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:474</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 471 </span>      fontWeight = FontWeight.Medium
<span class="lineno"> 472 </span>  )
<span class="lineno"> 473 </span>  Text(
<span class="caretline"><span class="lineno"> 474 </span>      text = <span class="string">"Available: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, fromCategory.allocatedAmount - fromCategory.spentAmount)</span><span class="string">}"</span>,</span>
<span class="lineno"> 475 </span>      style = MaterialTheme.typography.caption,
<span class="lineno"> 476 </span>      color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 477 </span>  )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:667</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 664 </span>                color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 665 </span>            )
<span class="lineno"> 666 </span>            Text(
<span class="caretline"><span class="lineno"> 667 </span>                text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, remaining)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 668 </span>                style = MaterialTheme.typography.h6,
<span class="lineno"> 669 </span>                fontWeight = FontWeight.Bold,
<span class="lineno"> 670 </span>                color = <span class="keyword">if</span> (remaining >= <span class="number">0</span>) statusColor <span class="keyword">else</span> Color(<span class="number">0xFFF44336</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:681</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 678 </span>                 color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 679 </span>             )
<span class="lineno"> 680 </span>             Text(
<span class="caretline"><span class="lineno"> 681 </span>                 text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, category.allocatedAmount)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 682 </span>                 style = MaterialTheme.typography.h6,
<span class="lineno"> 683 </span>                 fontWeight = FontWeight.Bold,
<span class="lineno"> 684 </span>                 color = categoryColor
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:698</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 695 </span>               horizontalArrangement = Arrangement.SpaceBetween
<span class="lineno"> 696 </span>           ) {
<span class="lineno"> 697 </span>               Text(
<span class="caretline"><span class="lineno"> 698 </span>                   text = <span class="string">"$${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, category.spentAmount)</span><span class="string">} spent"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 699 </span>                   style = MaterialTheme.typography.body2,
<span class="lineno"> 700 </span>                   color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 701 </span>               )
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:703</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 700 </span>                    color = MaterialTheme.colors.onSurface.copy(alpha = <span class="number">0.7f</span>)
<span class="lineno"> 701 </span>                )
<span class="lineno"> 702 </span>                Text(
<span class="caretline"><span class="lineno"> 703 </span>                    text = <span class="string">"${</span><span class="warning">String.format(<span class="string">"%.1f"</span>, spentPercentage * <span class="number">100</span>)</span><span class="string">}%"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 704 </span>                    style = MaterialTheme.typography.body2,
<span class="lineno"> 705 </span>                    color = statusColor,
<span class="lineno"> 706 </span>                    fontWeight = FontWeight.Medium
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:737</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 734 </span>            )
<span class="lineno"> 735 </span>            Spacer(modifier = Modifier.width(<span class="number">4.d</span>p))
<span class="lineno"> 736 </span>            Text(
<span class="caretline"><span class="lineno"> 737 </span>                text = <span class="string">"Over budget by $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, -remaining)</span><span class="string">}"</span>,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 738 </span>                style = MaterialTheme.typography.caption,
<span class="lineno"> 739 </span>                color = Color(<span class="number">0xFFF44336</span>),
<span class="lineno"> 740 </span>                fontWeight = FontWeight.Medium
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/utils/ErrorHandling.kt">../../src/main/java/com/focusflow/utils/ErrorHandling.kt</a>:348</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 345 </span><span class="comment">// Enhanced accessibility utilities for ADHD-friendly design</span>
<span class="lineno"> 346 </span><span class="keyword">object</span> AccessibilityUtils {
<span class="lineno"> 347 </span>    <span class="keyword">fun</span> getContentDescription(amount: Double, context: String): String {
<span class="caretline"><span class="lineno"> 348 </span>        <span class="keyword">val</span> formattedAmount = <span class="warning">String.format(<span class="string">"%.2f"</span>, amount)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 349 </span>        <span class="keyword">return</span> <span class="string">"$context: $formattedAmount dollars"</span>
<span class="lineno"> 350 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/utils/ErrorHandling.kt">../../src/main/java/com/focusflow/utils/ErrorHandling.kt</a>:359</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 356 </span>
<span class="lineno"> 357 </span>  <span class="keyword">fun</span> getSpendingCategoryDescription(category: String, amount: Double, budget: Double): String {
<span class="lineno"> 358 </span>      <span class="keyword">val</span> percentage = <span class="keyword">if</span> (budget > <span class="number">0</span>) (amount / budget * <span class="number">100</span>).toInt() <span class="keyword">else</span> <span class="number">0</span>
<span class="caretline"><span class="lineno"> 359 </span>      <span class="keyword">return</span> <span class="string">"$category spending: $${</span><span class="warning">String.format(<span class="string">"%.2f"</span>, amount)</span><span class="string">} of $${</span>String.format(<span class="string">"%.2f"</span>, budget)<span class="string">} budget, $percentage percent used"</span></span>
<span class="lineno"> 360 </span>  }
<span class="lineno"> 361 </span>
<span class="lineno"> 362 </span>  <span class="keyword">fun</span> getDebtDescription(cardName: String, balance: Double, limit: Double): String {
</pre>

<br/><b>NOTE: 32 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.ROOT)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Instantiatable"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InstantiatableCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Registered class is not instantiatable</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:69</span>: <span class="message"><code>NotificationService</code> must extend android.app.Service</span><br /><pre class="errorlines">
<span class="lineno"> 66 </span>
<span class="lineno"> 67 </span>        <span class="comment">&lt;!-- Notification Service --></span>
<span class="lineno"> 68 </span>        <span class="tag">&lt;service</span><span class="attribute">
</span><span class="caretline"><span class="lineno"> 69 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="error"><span class="value">.service.NotificationService</span></span><span class="value">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 70 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"false"</span> />
<span class="lineno"> 71 </span>
<span class="lineno"> 72 </span>        <span class="comment">&lt;!-- Alarm Receiver for scheduled notifications --></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInstantiatable" style="display: none;">
Activities, services, broadcast receivers etc. registered in the manifest file (or for custom views, in a layout file) must be "instantiatable" by the system, which means that the class must be public, it must have an empty public constructor, and if it's an inner class, it must be a static inner class.<br/>
<br/>
If you use a custom <code>AppComponentFactory</code> to instantiate app components yourself, consider disabling this Lint issue in order to avoid false positives.<br/>To suppress this error, use the issue id "Instantiatable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Instantiatable</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Fatal</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInstantiatableLink" onclick="reveal('explanationInstantiatable');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InstantiatableCardLink" onclick="hideid('InstantiatableCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NewApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NewApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Calling new methods on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/security/DataProtectionService.kt">../../src/main/java/com/focusflow/security/DataProtectionService.kt</a>:119</span>: <span class="message">Call requires API level 34 (current min is 24): <code>java.util.regex.Matcher#replaceAll</code></span><br /><pre class="errorlines">
<span class="lineno"> 116 </span>        <span class="keyword">var</span> masked = input
<span class="lineno"> 117 </span>        
<span class="lineno"> 118 </span>        <span class="comment">// Mask credit card numbers</span>
<span class="caretline"><span class="lineno"> 119 </span>        masked = CREDIT_CARD_PATTERN.matcher(masked).<span class="error">replaceAll</span> { matchResult ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>            <span class="keyword">val</span> cardNumber = matchResult.group().replace(<span class="string">"[ -]"</span>.toRegex(), <span class="string">""</span>)
<span class="lineno"> 121 </span>            <span class="keyword">if</span> (cardNumber.length >= <span class="number">4</span>) {
<span class="lineno"> 122 </span>                <span class="string">"*"</span>.repeat(cardNumber.length - <span class="number">4</span>) + cardNumber.takeLast(<span class="number">4</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/security/DataProtectionService.kt">../../src/main/java/com/focusflow/security/DataProtectionService.kt</a>:132</span>: <span class="message">Call requires API level 34 (current min is 24): <code>java.util.regex.Matcher#replaceAll</code></span><br /><pre class="errorlines">
<span class="lineno"> 129 </span>        masked = SSN_PATTERN.matcher(masked).replaceAll(<span class="string">"***-**-****"</span>)
<span class="lineno"> 130 </span>        
<span class="lineno"> 131 </span>        <span class="comment">// Mask bank account numbers</span>
<span class="caretline"><span class="lineno"> 132 </span>        masked = BANK_ACCOUNT_PATTERN.matcher(masked).<span class="error">replaceAll</span> { matchResult ->&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 133 </span>            <span class="keyword">val</span> accountNumber = matchResult.group()
<span class="lineno"> 134 </span>            <span class="keyword">if</span> (accountNumber.length >= <span class="number">4</span>) {
<span class="lineno"> 135 </span>                <span class="string">"*"</span>.repeat(accountNumber.length - <span class="number">4</span>) + accountNumber.takeLast(<span class="number">4</span>)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNewApi" style="display: none;">
This check scans through all the Android API calls in the application and warns about any calls that are not available on <b>all</b> versions targeted by this application (according to its minimum SDK attribute in the manifest).<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>
<br/>
If you are deliberately setting <code>android:</code> attributes in style definitions, make sure you place this in a <code>values-v</code><i>NN</i> folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.<br/>
<br/>
Similarly, you can use tools:targetApi="11" in an XML file to indicate that the element will only be inflated in an adequate context.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NewApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NewApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNewApiLink" onclick="reveal('explanationNewApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NewApiCardLink" onclick="hideid('NewApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="OldTargetApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OldTargetApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Target SDK attribute is not targeting latest version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:16</span>: <span class="message">Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the <code>android.os.Build.VERSION_CODES</code> javadoc for details.</span><br /><pre class="errorlines">
<span class="lineno">  13 </span>    defaultConfig {
<span class="lineno">  14 </span>        applicationId <span class="string">"com.focusflow"</span>
<span class="lineno">  15 </span>        minSdk <span class="number">24</span>
<span class="caretline"><span class="lineno">  16 </span>        <span class="warning">targetSdk <span class="number">34</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  17 </span>        versionCode <span class="number">1</span>
<span class="lineno">  18 </span>        versionName <span class="string">"1.0.0"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOldTargetApi" style="display: none;">
When your application or sdk runs on a version of Android that is more recent than your <code>targetSdkVersion</code> specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the <code>targetSdkVersion</code> is less than 14, your app may get an option button in the UI.<br/>
<br/>
To fix this issue, set the <code>targetSdkVersion</code> to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: <a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a> as well as follow this guide:<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
<li><a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "OldTargetApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">OldTargetApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOldTargetApiLink" onclick="reveal('explanationOldTargetApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OldTargetApiCardLink" onclick="hideid('OldTargetApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ScheduleExactAlarm"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ScheduleExactAlarmCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Scheduling Exact Alarms Without Required Permission</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/service/NotificationManager.kt">../../src/main/java/com/focusflow/service/NotificationManager.kt</a>:155</span>: <span class="message">When scheduling exact alarms, apps should explicitly call <code>AlarmManager#canScheduleExactAlarms</code> or handle `SecurityException`s</span><br /><pre class="errorlines">
<span class="lineno"> 152 </span>                 PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
<span class="lineno"> 153 </span>             )
<span class="lineno"> 154 </span>             
<span class="caretline"><span class="lineno"> 155 </span>             <span class="error">alarmManager.setExactAndAllowWhileIdle(</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 156 </span>                 AlarmManager.RTC_WAKEUP,
<span class="lineno"> 157 </span>                 reminderTime,
<span class="lineno"> 158 </span>                 pendingIntent
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationScheduleExactAlarm" style="display: none;">
Applications looking to schedule exact alarms should ensure that the <code>SCHEDULE_EXACT_ALARM</code> permission is granted by calling the <code>AlarmManager#canScheduleExactAlarms</code> API before attempting to set an exact alarm. If the permission is not granted to your application, please consider requesting it from the user by starting the <code>ACTION_REQUEST_SCHEDULE_EXACT_ALARM</code> intent or gracefully falling back to another option.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/scheduling/alarms#exact">https://developer.android.com/training/scheduling/alarms#exact</a>
</div>To suppress this error, use the issue id "ScheduleExactAlarm" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ScheduleExactAlarm</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationScheduleExactAlarmLink" onclick="reveal('explanationScheduleExactAlarm');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ScheduleExactAlarmCardLink" onclick="hideid('ScheduleExactAlarmCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RedundantLabel"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RedundantLabelCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Redundant label on activity</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:50</span>: <span class="message">Redundant label can be removed</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>        <span class="tag">&lt;activity</span><span class="attribute">
</span><span class="lineno"> 48 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".MainActivity"</span>
<span class="lineno"> 49 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"true"</span>
<span class="caretline"><span class="lineno"> 50 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.FocusFlow"</span>
<span class="lineno"> 52 </span>            <span class="prefix">android:</span><span class="attribute">screenOrientation</span>=<span class="value">"unspecified"</span>
<span class="lineno"> 53 </span>            <span class="prefix">android:</span><span class="attribute">windowSoftInputMode</span>=<span class="value">"adjustResize"</span>>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRedundantLabel" style="display: none;">
When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted.<br/>To suppress this error, use the issue id "RedundantLabel" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RedundantLabel</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRedundantLabelLink" onclick="reveal('explanationRedundantLabel');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RedundantLabelCardLink" onclick="hideid('RedundantLabelCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SelectedPhotoAccess"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SelectedPhotoAccessCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Behavior change when requesting photo library access</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:18</span>: <span class="message">Your app is currently not handling Selected Photos Access introduced in Android 14+</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>    <span class="tag">&lt;uses-feature</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.hardware.camera"</span> <span class="prefix">android:</span><span class="attribute">required</span>=<span class="value">"false"</span> />
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Storage permissions for receipt images --></span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.READ_MEDIA_IMAGES</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_MEDIA_VIDEO"</span> />
<span class="lineno"> 20 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_EXTERNAL_STORAGE"</span> 
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">maxSdkVersion</span>=<span class="value">"28"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:19</span>: <span class="message">Your app is currently not handling Selected Photos Access introduced in Android 14+</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Storage permissions for receipt images --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_MEDIA_IMAGES"</span> />
<span class="caretline"><span class="lineno"> 19 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.READ_MEDIA_VIDEO</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_EXTERNAL_STORAGE"</span> 
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">maxSdkVersion</span>=<span class="value">"28"</span> />
<span class="lineno"> 22 </span>    
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSelectedPhotoAccess" style="display: none;">
Selected Photo Access is a new ability for users to share partial access to their photo library when apps request access to their device storage on Android 14+.<br/>
<br/>
Instead of letting the system manage the selection lifecycle, we recommend you adapt your app to handle partial access to the photo library.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/about/versions/14/changes/partial-photo-video-access">https://developer.android.com/about/versions/14/changes/partial-photo-video-access</a>
</div>To suppress this error, use the issue id "SelectedPhotoAccess" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SelectedPhotoAccess</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSelectedPhotoAccessLink" onclick="reveal('explanationSelectedPhotoAccess');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SelectedPhotoAccessCardLink" onclick="hideid('SelectedPhotoAccessCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AndroidGradlePluginVersion"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AndroidGradlePluginVersionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Android Gradle Plugin Version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../gradle/wrapper/gradle-wrapper.properties">../../../gradle/wrapper/gradle-wrapper.properties</a>:3</span>: <span class="message">A newer version of Gradle than 8.12 is available: 8.14.2</span><br /><pre class="errorlines">
<span class="lineno"> 1 </span>distributionBase=GRADLE_USER_HOME
<span class="lineno"> 2 </span>distributionPath=wrapper/dists
<span class="caretline"><span class="lineno"> 3 </span>distributionUrl=<span class="warning">https\://services.gradle.org/distributions/gradle-8.12-bin.zip</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>networkTimeout=10000
<span class="lineno"> 5 </span>validateDistributionUrl=true
<span class="lineno"> 6 </span>zipStoreBase=GRADLE_USER_HOME
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAndroidGradlePluginVersion" style="display: none;">
This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AndroidGradlePluginVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AndroidGradlePluginVersion</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAndroidGradlePluginVersionLink" onclick="reveal('explanationAndroidGradlePluginVersion');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AndroidGradlePluginVersionCardLink" onclick="hideid('AndroidGradlePluginVersionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:11</span>: <span class="message">A newer version of <code>compileSdkVersion</code> than 34 is available: 35</span><br /><pre class="errorlines">
<span class="lineno">   8 </span>
<span class="lineno">   9 </span>android {
<span class="lineno">  10 </span>    namespace <span class="string">'com.focusflow'</span>
<span class="caretline"><span class="lineno">  11 </span>    <span class="warning">compileSdk <span class="number">34</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  12 </span>
<span class="lineno">  13 </span>    defaultConfig {
<span class="lineno">  14 </span>        applicationId <span class="string">"com.focusflow"</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:116</span>: <span class="message">A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0</span><br /><pre class="errorlines">
<span class="lineno"> 113 </span>
<span class="lineno"> 114 </span>dependencies {
<span class="lineno"> 115 </span>    <span class="comment">// Core Android</span>
<span class="caretline"><span class="lineno"> 116 </span>    implementation <span class="warning"><span class="string">'androidx.core:core-ktx:1.12.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 117 </span>    implementation <span class="string">'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'</span>
<span class="lineno"> 118 </span>    implementation <span class="string">'androidx.activity:activity-compose:1.8.2'</span>
<span class="lineno"> 119 </span>    
</pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:117</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1</span><br /><pre class="errorlines">
<span class="lineno"> 114 </span>dependencies {
<span class="lineno"> 115 </span>    <span class="comment">// Core Android</span>
<span class="lineno"> 116 </span>    implementation <span class="string">'androidx.core:core-ktx:1.12.0'</span>
<span class="caretline"><span class="lineno"> 117 </span>    implementation <span class="warning"><span class="string">'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 118 </span>    implementation <span class="string">'androidx.activity:activity-compose:1.8.2'</span>
<span class="lineno"> 119 </span>    
<span class="lineno"> 120 </span>    <span class="comment">// Compose BOM</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:118</span>: <span class="message">A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1</span><br /><pre class="errorlines">
<span class="lineno"> 115 </span>    <span class="comment">// Core Android</span>
<span class="lineno"> 116 </span>    implementation <span class="string">'androidx.core:core-ktx:1.12.0'</span>
<span class="lineno"> 117 </span>    implementation <span class="string">'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'</span>
<span class="caretline"><span class="lineno"> 118 </span>    implementation <span class="warning"><span class="string">'androidx.activity:activity-compose:1.8.2'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 119 </span>    
<span class="lineno"> 120 </span>    <span class="comment">// Compose BOM</span>
<span class="lineno"> 121 </span>    implementation platform(<span class="string">'androidx.compose:compose-bom:2023.10.01'</span>)
</pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:121</span>: <span class="message">A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00</span><br /><pre class="errorlines">
<span class="lineno"> 118 </span>    implementation <span class="string">'androidx.activity:activity-compose:1.8.2'</span>
<span class="lineno"> 119 </span>    
<span class="lineno"> 120 </span>    <span class="comment">// Compose BOM</span>
<span class="caretline"><span class="lineno"> 121 </span>    implementation <span class="warning">platform(<span class="string">'androidx.compose:compose-bom:2023.10.01'</span>)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 122 </span>    implementation <span class="string">'androidx.compose.ui:ui'</span>
<span class="lineno"> 123 </span>    implementation <span class="string">'androidx.compose.ui:ui-graphics'</span>
<span class="lineno"> 124 </span>    implementation <span class="string">'androidx.compose.ui:ui-tooling-preview'</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="GradleDependencyDivLink" onclick="reveal('GradleDependencyDiv');" />+ 19 More Occurrences...</button>
<div id="GradleDependencyDiv" style="display: none">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:129</span>: <span class="message">A newer version of androidx.navigation:navigation-compose than 2.7.5 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno"> 126 </span>    implementation <span class="string">'androidx.compose.material:material-icons-extended'</span>
<span class="lineno"> 127 </span>    
<span class="lineno"> 128 </span>    <span class="comment">// Navigation</span>
<span class="caretline"><span class="lineno"> 129 </span>    implementation <span class="warning"><span class="string">'androidx.navigation:navigation-compose:2.7.5'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 130 </span>    
<span class="lineno"> 131 </span>    <span class="comment">// ViewModel</span>
<span class="lineno"> 132 </span>    implementation <span class="string">'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:132</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.1</span><br /><pre class="errorlines">
<span class="lineno"> 129 </span>    implementation <span class="string">'androidx.navigation:navigation-compose:2.7.5'</span>
<span class="lineno"> 130 </span>    
<span class="lineno"> 131 </span>    <span class="comment">// ViewModel</span>
<span class="caretline"><span class="lineno"> 132 </span>    implementation <span class="warning"><span class="string">'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 133 </span>    implementation <span class="string">'androidx.lifecycle:lifecycle-runtime-compose:2.7.0'</span>
<span class="lineno"> 134 </span>    
<span class="lineno"> 135 </span>    <span class="comment">// Hilt</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:133</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.1</span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>    
<span class="lineno"> 131 </span>    <span class="comment">// ViewModel</span>
<span class="lineno"> 132 </span>    implementation <span class="string">'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'</span>
<span class="caretline"><span class="lineno"> 133 </span>    implementation <span class="warning"><span class="string">'androidx.lifecycle:lifecycle-runtime-compose:2.7.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>    
<span class="lineno"> 135 </span>    <span class="comment">// Hilt</span>
<span class="lineno"> 136 </span>    implementation <span class="string">'com.google.dagger:hilt-android:2.48'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:137</span>: <span class="message">A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0</span><br /><pre class="errorlines">
<span class="lineno"> 134 </span>    
<span class="lineno"> 135 </span>    <span class="comment">// Hilt</span>
<span class="lineno"> 136 </span>    implementation <span class="string">'com.google.dagger:hilt-android:2.48'</span>
<span class="caretline"><span class="lineno"> 137 </span>    implementation <span class="warning"><span class="string">'androidx.hilt:hilt-navigation-compose:1.1.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 138 </span>    kapt <span class="string">'com.google.dagger:hilt-compiler:2.48'</span>
<span class="lineno"> 139 </span>    
<span class="lineno"> 140 </span>    <span class="comment">// Room</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:141</span>: <span class="message">A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1</span><br /><pre class="errorlines">
<span class="lineno"> 138 </span>    kapt <span class="string">'com.google.dagger:hilt-compiler:2.48'</span>
<span class="lineno"> 139 </span>    
<span class="lineno"> 140 </span>    <span class="comment">// Room</span>
<span class="caretline"><span class="lineno"> 141 </span>    implementation <span class="warning"><span class="string">'androidx.room:room-runtime:2.6.1'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 142 </span>    implementation <span class="string">'androidx.room:room-ktx:2.6.1'</span>
<span class="lineno"> 143 </span>    kapt <span class="string">'androidx.room:room-compiler:2.6.1'</span>
<span class="lineno"> 144 </span>    
</pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:142</span>: <span class="message">A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>    
<span class="lineno"> 140 </span>    <span class="comment">// Room</span>
<span class="lineno"> 141 </span>    implementation <span class="string">'androidx.room:room-runtime:2.6.1'</span>
<span class="caretline"><span class="lineno"> 142 </span>    implementation <span class="warning"><span class="string">'androidx.room:room-ktx:2.6.1'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>    kapt <span class="string">'androidx.room:room-compiler:2.6.1'</span>
<span class="lineno"> 144 </span>    
<span class="lineno"> 145 </span>    <span class="comment">// Kotlinx DateTime</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:143</span>: <span class="message">A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1</span><br /><pre class="errorlines">
<span class="lineno"> 140 </span>    <span class="comment">// Room</span>
<span class="lineno"> 141 </span>    implementation <span class="string">'androidx.room:room-runtime:2.6.1'</span>
<span class="lineno"> 142 </span>    implementation <span class="string">'androidx.room:room-ktx:2.6.1'</span>
<span class="caretline"><span class="lineno"> 143 </span>    kapt <span class="warning"><span class="string">'androidx.room:room-compiler:2.6.1'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 144 </span>    
<span class="lineno"> 145 </span>    <span class="comment">// Kotlinx DateTime</span>
<span class="lineno"> 146 </span>    implementation <span class="string">'org.jetbrains.kotlinx:kotlinx-datetime:0.5.0'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:163</span>: <span class="message">A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-beta01</span><br /><pre class="errorlines">
<span class="lineno"> 160 </span>    implementation <span class="string">'androidx.biometric:biometric:1.1.0'</span>
<span class="lineno"> 161 </span>    
<span class="lineno"> 162 </span>    <span class="comment">// Encrypted SharedPreferences</span>
<span class="caretline"><span class="lineno"> 163 </span>    implementation <span class="warning"><span class="string">'androidx.security:security-crypto:1.1.0-alpha06'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 164 </span>    
<span class="lineno"> 165 </span>    <span class="comment">// Work Manager for background tasks</span>
<span class="lineno"> 166 </span>    implementation <span class="string">'androidx.work:work-runtime-ktx:2.9.0'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:166</span>: <span class="message">A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1</span><br /><pre class="errorlines">
<span class="lineno"> 163 </span>    implementation <span class="string">'androidx.security:security-crypto:1.1.0-alpha06'</span>
<span class="lineno"> 164 </span>    
<span class="lineno"> 165 </span>    <span class="comment">// Work Manager for background tasks</span>
<span class="caretline"><span class="lineno"> 166 </span>    implementation <span class="warning"><span class="string">'androidx.work:work-runtime-ktx:2.9.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 167 </span>    implementation <span class="string">'androidx.hilt:hilt-work:1.1.0'</span>
<span class="lineno"> 168 </span>    
<span class="lineno"> 169 </span>    <span class="comment">// Permissions</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:167</span>: <span class="message">A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0</span><br /><pre class="errorlines">
<span class="lineno"> 164 </span>    
<span class="lineno"> 165 </span>    <span class="comment">// Work Manager for background tasks</span>
<span class="lineno"> 166 </span>    implementation <span class="string">'androidx.work:work-runtime-ktx:2.9.0'</span>
<span class="caretline"><span class="lineno"> 167 </span>    implementation <span class="warning"><span class="string">'androidx.hilt:hilt-work:1.1.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 168 </span>    
<span class="lineno"> 169 </span>    <span class="comment">// Permissions</span>
<span class="lineno"> 170 </span>    implementation <span class="string">'com.google.accompanist:accompanist-permissions:0.32.0'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:173</span>: <span class="message">A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0</span><br /><pre class="errorlines">
<span class="lineno"> 170 </span>    implementation <span class="string">'com.google.accompanist:accompanist-permissions:0.32.0'</span>
<span class="lineno"> 171 </span>
<span class="lineno"> 172 </span>    <span class="comment">// Speech recognition and text-to-speech</span>
<span class="caretline"><span class="lineno"> 173 </span>    implementation <span class="warning"><span class="string">'androidx.core:core-ktx:1.12.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 174 </span>    
<span class="lineno"> 175 </span>    <span class="comment">// Date picker</span>
<span class="lineno"> 176 </span>    implementation <span class="string">'io.github.vanpra.compose-material-dialogs:datetime:0.9.0'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:184</span>: <span class="message">A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5</span><br /><pre class="errorlines">
<span class="lineno"> 181 </span>    <span class="comment">// implementation 'com.github.PhilJay:MPAndroidChart:3.1.0'</span>
<span class="lineno"> 182 </span>    
<span class="lineno"> 183 </span>    <span class="comment">// Desugaring for Java 8+ APIs</span>
<span class="caretline"><span class="lineno"> 184 </span>    coreLibraryDesugaring <span class="warning"><span class="string">'com.android.tools:desugar_jdk_libs:2.0.4'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 185 </span>    
<span class="lineno"> 186 </span>    <span class="comment">// Testing</span>
<span class="lineno"> 187 </span>    testImplementation <span class="string">'junit:junit:4.13.2'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:188</span>: <span class="message">A newer version of androidx.test:core than 1.5.0 is available: 1.6.1</span><br /><pre class="errorlines">
<span class="lineno"> 185 </span>    
<span class="lineno"> 186 </span>    <span class="comment">// Testing</span>
<span class="lineno"> 187 </span>    testImplementation <span class="string">'junit:junit:4.13.2'</span>
<span class="caretline"><span class="lineno"> 188 </span>    testImplementation <span class="warning"><span class="string">'androidx.test:core:1.5.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 189 </span>    testImplementation <span class="string">'androidx.test.ext:junit:1.1.5'</span>
<span class="lineno"> 190 </span>    testImplementation <span class="string">'androidx.room:room-testing:2.6.1'</span>
<span class="lineno"> 191 </span>    testImplementation <span class="string">'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:189</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno"> 186 </span>    <span class="comment">// Testing</span>
<span class="lineno"> 187 </span>    testImplementation <span class="string">'junit:junit:4.13.2'</span>
<span class="lineno"> 188 </span>    testImplementation <span class="string">'androidx.test:core:1.5.0'</span>
<span class="caretline"><span class="lineno"> 189 </span>    testImplementation <span class="warning"><span class="string">'androidx.test.ext:junit:1.1.5'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 190 </span>    testImplementation <span class="string">'androidx.room:room-testing:2.6.1'</span>
<span class="lineno"> 191 </span>    testImplementation <span class="string">'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'</span>
<span class="lineno"> 192 </span>    testImplementation <span class="string">'com.google.truth:truth:1.1.4'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:190</span>: <span class="message">A newer version of androidx.room:room-testing than 2.6.1 is available: 2.7.1</span><br /><pre class="errorlines">
<span class="lineno"> 187 </span>    testImplementation <span class="string">'junit:junit:4.13.2'</span>
<span class="lineno"> 188 </span>    testImplementation <span class="string">'androidx.test:core:1.5.0'</span>
<span class="lineno"> 189 </span>    testImplementation <span class="string">'androidx.test.ext:junit:1.1.5'</span>
<span class="caretline"><span class="lineno"> 190 </span>    testImplementation <span class="warning"><span class="string">'androidx.room:room-testing:2.6.1'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 191 </span>    testImplementation <span class="string">'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'</span>
<span class="lineno"> 192 </span>    testImplementation <span class="string">'com.google.truth:truth:1.1.4'</span>
<span class="lineno"> 193 </span>    testImplementation <span class="string">'androidx.arch.core:core-testing:2.2.0'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:196</span>: <span class="message">A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1</span><br /><pre class="errorlines">
<span class="lineno"> 193 </span>    testImplementation <span class="string">'androidx.arch.core:core-testing:2.2.0'</span>
<span class="lineno"> 194 </span>    
<span class="lineno"> 195 </span>    <span class="comment">// Android Testing</span>
<span class="caretline"><span class="lineno"> 196 </span>    androidTestImplementation <span class="warning"><span class="string">'androidx.test.ext:junit:1.1.5'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 197 </span>    androidTestImplementation <span class="string">'androidx.test.espresso:espresso-core:3.5.1'</span>
<span class="lineno"> 198 </span>    androidTestImplementation platform(<span class="string">'androidx.compose:compose-bom:2023.10.01'</span>)
<span class="lineno"> 199 </span>    androidTestImplementation <span class="string">'androidx.compose.ui:ui-test-junit4'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:197</span>: <span class="message">A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1</span><br /><pre class="errorlines">
<span class="lineno"> 194 </span>    
<span class="lineno"> 195 </span>    <span class="comment">// Android Testing</span>
<span class="lineno"> 196 </span>    androidTestImplementation <span class="string">'androidx.test.ext:junit:1.1.5'</span>
<span class="caretline"><span class="lineno"> 197 </span>    androidTestImplementation <span class="warning"><span class="string">'androidx.test.espresso:espresso-core:3.5.1'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 198 </span>    androidTestImplementation platform(<span class="string">'androidx.compose:compose-bom:2023.10.01'</span>)
<span class="lineno"> 199 </span>    androidTestImplementation <span class="string">'androidx.compose.ui:ui-test-junit4'</span>
<span class="lineno"> 200 </span>    androidTestImplementation <span class="string">'androidx.navigation:navigation-testing:2.7.5'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:198</span>: <span class="message">A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00</span><br /><pre class="errorlines">
<span class="lineno"> 195 </span>    <span class="comment">// Android Testing</span>
<span class="lineno"> 196 </span>    androidTestImplementation <span class="string">'androidx.test.ext:junit:1.1.5'</span>
<span class="lineno"> 197 </span>    androidTestImplementation <span class="string">'androidx.test.espresso:espresso-core:3.5.1'</span>
<span class="caretline"><span class="lineno"> 198 </span>    androidTestImplementation <span class="warning">platform(<span class="string">'androidx.compose:compose-bom:2023.10.01'</span>)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 199 </span>    androidTestImplementation <span class="string">'androidx.compose.ui:ui-test-junit4'</span>
<span class="lineno"> 200 </span>    androidTestImplementation <span class="string">'androidx.navigation:navigation-testing:2.7.5'</span>
<span class="lineno"> 201 </span>    androidTestImplementation <span class="string">'com.google.dagger:hilt-android-testing:2.48'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:200</span>: <span class="message">A newer version of androidx.navigation:navigation-testing than 2.7.5 is available: 2.9.0</span><br /><pre class="errorlines">
<span class="lineno"> 197 </span>    androidTestImplementation <span class="string">'androidx.test.espresso:espresso-core:3.5.1'</span>
<span class="lineno"> 198 </span>    androidTestImplementation platform(<span class="string">'androidx.compose:compose-bom:2023.10.01'</span>)
<span class="lineno"> 199 </span>    androidTestImplementation <span class="string">'androidx.compose.ui:ui-test-junit4'</span>
<span class="caretline"><span class="lineno"> 200 </span>    androidTestImplementation <span class="warning"><span class="string">'androidx.navigation:navigation-testing:2.7.5'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 201 </span>    androidTestImplementation <span class="string">'com.google.dagger:hilt-android-testing:2.48'</span>
<span class="lineno"> 202 </span>    kaptAndroidTest <span class="string">'com.google.dagger:hilt-compiler:2.48'</span>
<span class="lineno"> 203 </span>    
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ComposableModifierFactory"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ComposableModifierFactoryCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Modifier factory functions should not be @Composable</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt">../../src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt</a>:217</span>: <span class="message">Modifier factory functions should not be marked as @Composable, and should use composed instead</span><br /><pre class="errorlines">
<span class="lineno"> 214 </span><span class="javadoc">     * Responsive card layout
</span><span class="lineno"> 215 </span><span class="javadoc">     */</span>
<span class="lineno"> 216 </span>    <span class="annotation">@Composable</span>
<span class="caretline"><span class="lineno"> 217 </span>    <span class="keyword">fun</span> <span class="warning">getCardModifier</span>(): Modifier {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 218 </span>        <span class="keyword">val</span> config = getLayoutConfig()
<span class="lineno"> 219 </span>        <span class="keyword">return</span> Modifier
<span class="lineno"> 220 </span>            .fillMaxWidth()
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationComposableModifierFactory" style="display: none;">
Modifier factory functions that need to be aware of the composition should use androidx.compose.ui.composed {} in their implementation instead of being marked as @Composable. This allows Modifiers to be referenced in top level variables and constructed outside of the composition.<br/>To suppress this error, use the issue id "ComposableModifierFactory" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ComposableModifierFactory</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationComposableModifierFactoryLink" onclick="reveal('explanationComposableModifierFactory');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ComposableModifierFactoryCardLink" onclick="hideid('ComposableModifierFactoryCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ModifierFactoryExtensionFunction"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ModifierFactoryExtensionFunctionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Modifier factory functions should be extensions on Modifier</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt">../../src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt</a>:217</span>: <span class="message">Modifier factory functions should be extensions on Modifier</span><br /><pre class="errorlines">
<span class="lineno"> 214 </span><span class="javadoc">     * Responsive card layout
</span><span class="lineno"> 215 </span><span class="javadoc">     */</span>
<span class="lineno"> 216 </span>    <span class="annotation">@Composable</span>
<span class="caretline"><span class="lineno"> 217 </span>    <span class="keyword">fun</span> <span class="warning">getCardModifier</span>(): Modifier {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 218 </span>        <span class="keyword">val</span> config = getLayoutConfig()
<span class="lineno"> 219 </span>        <span class="keyword">return</span> Modifier
<span class="lineno"> 220 </span>            .fillMaxWidth()
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationModifierFactoryExtensionFunction" style="display: none;">
Modifier factory functions should be defined as extension functions on Modifier to allow modifiers to be fluently chained.<br/>To suppress this error, use the issue id "ModifierFactoryExtensionFunction" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ModifierFactoryExtensionFunction</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationModifierFactoryExtensionFunctionLink" onclick="reveal('explanationModifierFactoryExtensionFunction');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ModifierFactoryExtensionFunctionCardLink" onclick="hideid('ModifierFactoryExtensionFunctionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ModifierParameter"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ModifierParameterCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Guidelines for Modifier parameters in a Composable function</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/DecisionSupportComponents.kt">../../src/main/java/com/focusflow/ui/components/DecisionSupportComponents.kt</a>:29</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  26 </span>    noText: String = <span class="string">"No"</span>,
<span class="lineno">  27 </span>    onYes: () -> Unit,
<span class="lineno">  28 </span>    onNo: () -> Unit,
<span class="caretline"><span class="lineno">  29 </span>    <span class="warning">modifier</span>: Modifier = Modifier,&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  30 </span>    isLoading: Boolean = <span class="keyword">false</span>
<span class="lineno">  31 </span>) {
<span class="lineno">  32 </span>    Card(
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/FocusTimerComponents.kt">../../src/main/java/com/focusflow/ui/components/FocusTimerComponents.kt</a>:41</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  38 </span>    onStartPause: () -> Unit,
<span class="lineno">  39 </span>    onStop: () -> Unit,
<span class="lineno">  40 </span>    onSkipBreak: () -> Unit = {},
<span class="caretline"><span class="lineno">  41 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  42 </span>) {
<span class="lineno">  43 </span>    <span class="keyword">val</span> progress = <span class="keyword">if</span> (totalTimeSeconds > <span class="number">0</span>) {
<span class="lineno">  44 </span>        (totalTimeSeconds - remainingTimeSeconds).toFloat() / totalTimeSeconds.toFloat()
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/ImpulseControlSettingsCard.kt">../../src/main/java/com/focusflow/ui/components/ImpulseControlSettingsCard.kt</a>:23</span>: <span class="message">Modifier parameter should be the first optional parameter</span><br /><pre class="errorlines">
<span class="lineno">  20 </span><span class="annotation">@Composable</span>
<span class="lineno">  21 </span><span class="keyword">fun</span> ImpulseControlSettingsCard(
<span class="lineno">  22 </span>    impulseControlViewModel: ImpulseControlViewModel = hiltViewModel(),
<span class="caretline"><span class="lineno">  23 </span>    <span class="warning">modifier</span>: Modifier = Modifier&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  24 </span>) {
<span class="lineno">  25 </span>    <span class="keyword">val</span> uiState by impulseControlViewModel.uiState.collectAsStateWithLifecycle()
<span class="lineno">  26 </span>    
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationModifierParameter" style="display: none;">
The first (or only) Modifier parameter in a Composable function should follow the following rules:<br/>
- Be named <code>modifier</code><br/>
- Have a type of <code>Modifier</code><br/>
- Either have no default value, or have a default value of <code>Modifier</code><br/>
- If optional, be the first optional parameter in the parameter list<br/>To suppress this error, use the issue id "ModifierParameter" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ModifierParameter</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationModifierParameterLink" onclick="reveal('explanationModifierParameter');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ModifierParameterCardLink" onclick="hideid('ModifierParameterCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="MutableCollectionMutableState"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MutableCollectionMutableStateCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Creating a MutableState object with a mutable collection type</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt">../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt</a>:493</span>: <span class="message">Creating a MutableState object with a mutable collection type</span><br /><pre class="errorlines">
<span class="lineno"> 490 </span>    )
<span class="lineno"> 491 </span>
<span class="lineno"> 492 </span>    <span class="keyword">var</span> selectedCategories by remember {
<span class="caretline"><span class="lineno"> 493 </span>        <span class="warning">mutableStateOf</span>(&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 494 </span>            defaultCategories.mapValues {
<span class="lineno"> 495 </span>                kotlin.math.min(it.value, unallocatedAmount / defaultCategories.size)
<span class="lineno"> 496 </span>            }.toMutableMap()
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/PlaceholderScreens.kt">../../src/main/java/com/focusflow/ui/screens/PlaceholderScreens.kt</a>:407</span>: <span class="message">Creating a MutableState object with a mutable collection type</span><br /><pre class="errorlines">
<span class="lineno"> 404 </span>    onDismiss: () -> Unit,
<span class="lineno"> 405 </span>    onSetupComplete: (List&lt;Pair&lt;String, Double>>) -> Unit
<span class="lineno"> 406 </span>) {
<span class="caretline"><span class="lineno"> 407 </span>    <span class="keyword">var</span> selectedCategories by remember { <span class="warning">mutableStateOf</span>(DefaultBudgetCategories.categories.toMap().toMutableMap()) }</span>
<span class="lineno"> 408 </span>
<span class="lineno"> 409 </span>    AlertDialog(
<span class="lineno"> 410 </span>        onDismissRequest = onDismiss,
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMutableCollectionMutableState" style="display: none;">
Writes to mutable collections inside a MutableState will not cause a recomposition - only writes to the MutableState itself will. In most cases you should either use a read-only collection (such as List or Map) and assign a new instance to the MutableState when your data changes, or you can use an snapshot-backed collection such as SnapshotStateList or SnapshotStateMap which will correctly cause a recomposition when their contents are modified.<br/>To suppress this error, use the issue id "MutableCollectionMutableState" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MutableCollectionMutableState</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMutableCollectionMutableStateLink" onclick="reveal('explanationMutableCollectionMutableState');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MutableCollectionMutableStateCardLink" onclick="hideid('MutableCollectionMutableStateCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DiscouragedApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DiscouragedApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using discouraged APIs</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:52</span>: <span class="message">Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.</span><br /><pre class="errorlines">
<span class="lineno"> 49 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"true"</span>
<span class="lineno"> 50 </span>            <span class="prefix">android:</span><span class="attribute">label</span>=<span class="value">"@string/app_name"</span>
<span class="lineno"> 51 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.FocusFlow"</span>
<span class="caretline"><span class="lineno"> 52 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">screenOrientation</span>=<span class="value">"unspecified"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 53 </span>            <span class="prefix">android:</span><span class="attribute">windowSoftInputMode</span>=<span class="value">"adjustResize"</span>>
<span class="lineno"> 54 </span>            <span class="tag">&lt;intent-filter></span>
<span class="lineno"> 55 </span>                <span class="tag">&lt;action</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.intent.action.MAIN"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:65</span>: <span class="message">Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.</span><br /><pre class="errorlines">
<span class="lineno"> 62 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".ui.onboarding.OnboardingActivity"</span>
<span class="lineno"> 63 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"false"</span>
<span class="lineno"> 64 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.FocusFlow.NoActionBar"</span>
<span class="caretline"><span class="lineno"> 65 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">screenOrientation</span>=<span class="value">"unspecified"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 66 </span>
<span class="lineno"> 67 </span>        <span class="comment">&lt;!-- Notification Service --></span>
<span class="lineno"> 68 </span>        <span class="tag">&lt;service</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDiscouragedApi" style="display: none;">
Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior).<br/>To suppress this error, use the issue id "DiscouragedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DiscouragedApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDiscouragedApiLink" onclick="reveal('explanationDiscouragedApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DiscouragedApiCardLink" onclick="hideid('DiscouragedApiCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="KaptUsageInsteadOfKsp"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="KaptUsageInsteadOfKspCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Kapt usage should be replaced with KSP</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:143</span>: <span class="message">This library supports using KSP instead of kapt, which greatly improves performance. Learn more: <a href="https://developer.android.com/studio/build/migrate-to-ksp">https://developer.android.com/studio/build/migrate-to-ksp</a></span><br /><pre class="errorlines">
<span class="lineno"> 140 </span>    <span class="comment">// Room</span>
<span class="lineno"> 141 </span>    implementation <span class="string">'androidx.room:room-runtime:2.6.1'</span>
<span class="lineno"> 142 </span>    implementation <span class="string">'androidx.room:room-ktx:2.6.1'</span>
<span class="caretline"><span class="lineno"> 143 </span>    <span class="warning">kapt <span class="string">'androidx.room:room-compiler:2.6.1'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 144 </span>    
<span class="lineno"> 145 </span>    <span class="comment">// Kotlinx DateTime</span>
<span class="lineno"> 146 </span>    implementation <span class="string">'org.jetbrains.kotlinx:kotlinx-datetime:0.5.0'</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationKaptUsageInsteadOfKsp" style="display: none;">
KSP is a more efficient replacement for kapt. For libraries that support both, KSP should be used to improve build times.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/studio/build/migrate-to-ksp">https://developer.android.com/studio/build/migrate-to-ksp</a>
</div>To suppress this error, use the issue id "KaptUsageInsteadOfKsp" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">KaptUsageInsteadOfKsp</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationKaptUsageInsteadOfKspLink" onclick="reveal('explanationKaptUsageInsteadOfKsp');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="KaptUsageInsteadOfKspCardLink" onclick="hideid('KaptUsageInsteadOfKspCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AutoboxingStateCreation"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AutoboxingStateCreationCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">State&lt;T> will autobox values assigned to this state. Use a specialized state type instead.</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/ImpulseControlComponents.kt">../../src/main/java/com/focusflow/ui/components/ImpulseControlComponents.kt</a>:27</span>: <span class="message">Prefer <code>mutableIntStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno">  24 </span>    onCancel: () -> Unit,
<span class="lineno">  25 </span>    onDelay: () -> Unit
<span class="lineno">  26 </span>) {
<span class="caretline"><span class="lineno">  27 </span>    <span class="keyword">var</span> timeRemaining by remember { <span class="warning">mutableStateOf</span>(<span class="number">10</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  28 </span>    <span class="keyword">var</span> isDelayActive by remember { mutableStateOf(<span class="keyword">false</span>) }
<span class="lineno">  29 </span>
<span class="lineno">  30 </span>    LaunchedEffect(isDelayActive) {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/ImpulseControlComponents.kt">../../src/main/java/com/focusflow/ui/components/ImpulseControlComponents.kt</a>:469</span>: <span class="message">Prefer <code>mutableIntStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 466 </span>    modifier: Modifier = Modifier
<span class="lineno"> 467 </span>) {
<span class="lineno"> 468 </span>    <span class="keyword">var</span> currentPhase by remember { mutableStateOf(<span class="string">"inhale"</span>) }
<span class="caretline"><span class="lineno"> 469 </span>    <span class="keyword">var</span> secondsRemaining by remember { <span class="warning">mutableStateOf</span>(<span class="number">4</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 470 </span>    <span class="keyword">var</span> cycleCount by remember { mutableStateOf(<span class="number">0</span>) }
<span class="lineno"> 471 </span>    <span class="keyword">val</span> totalCycles = <span class="number">3</span>
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/ImpulseControlComponents.kt">../../src/main/java/com/focusflow/ui/components/ImpulseControlComponents.kt</a>:470</span>: <span class="message">Prefer <code>mutableIntStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 467 </span>) {
<span class="lineno"> 468 </span>    <span class="keyword">var</span> currentPhase by remember { mutableStateOf(<span class="string">"inhale"</span>) }
<span class="lineno"> 469 </span>    <span class="keyword">var</span> secondsRemaining by remember { mutableStateOf(<span class="number">4</span>) }
<span class="caretline"><span class="lineno"> 470 </span>    <span class="keyword">var</span> cycleCount by remember { <span class="warning">mutableStateOf</span>(<span class="number">0</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 471 </span>    <span class="keyword">val</span> totalCycles = <span class="number">3</span>
<span class="lineno"> 472 </span>
<span class="lineno"> 473 </span>    LaunchedEffect(Unit) {
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/ImpulseControlSettingsCard.kt">../../src/main/java/com/focusflow/ui/components/ImpulseControlSettingsCard.kt</a>:335</span>: <span class="message">Prefer <code>mutableIntStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno"> 332 </span>    onUpdatePeriod: (Int) -> Unit
<span class="lineno"> 333 </span>) {
<span class="lineno"> 334 </span>    <span class="keyword">val</span> periodOptions = listOf(<span class="number">5</span>, <span class="number">10</span>, <span class="number">15</span>, <span class="number">30</span>, <span class="number">60</span>)
<span class="caretline"><span class="lineno"> 335 </span>    <span class="keyword">var</span> selectedPeriod by remember { <span class="warning">mutableStateOf</span>(currentPeriod) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 336 </span>
<span class="lineno"> 337 </span>    AlertDialog(
<span class="lineno"> 338 </span>        onDismissRequest = onDismiss,
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/VisualAnalyticsComponents.kt">../../src/main/java/com/focusflow/ui/components/VisualAnalyticsComponents.kt</a>:32</span>: <span class="message">Prefer <code>mutableFloatStateOf</code> instead of <code>mutableStateOf</code></span><br /><pre class="errorlines">
<span class="lineno">  29 </span>    showGrid: Boolean = <span class="keyword">true</span>,
<span class="lineno">  30 </span>    animateEntry: Boolean = <span class="keyword">true</span>
<span class="lineno">  31 </span>) {
<span class="caretline"><span class="lineno">  32 </span>    <span class="keyword">var</span> animationProgress by remember { <span class="warning">mutableStateOf</span>(<span class="keyword">if</span> (animateEntry) <span class="number">0f</span> <span class="keyword">else</span> <span class="number">1f</span>) }&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  33 </span>    
<span class="lineno">  34 </span>    LaunchedEffect(data) {
<span class="lineno">  35 </span>        <span class="keyword">if</span> (animateEntry) {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAutoboxingStateCreation" style="display: none;">
Calling <code>mutableStateOf&lt;T>()</code> when <code>T</code> is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for <code>Int</code>, <code>Long</code>, <code>Float</code>, and <code>Double</code> when the state does not need to track null values and does not override the default <code>SnapshotMutationPolicy</code>.<br/>To suppress this error, use the issue id "AutoboxingStateCreation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AutoboxingStateCreation</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Hint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAutoboxingStateCreationLink" onclick="reveal('explanationAutoboxingStateCreation');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AutoboxingStateCreationCardLink" onclick="hideid('AutoboxingStateCreationCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:4</span>: <span class="message">The resource <code>R.color.purple_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Primary brand colors --></span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_200"</span></span>>#FFBB86FC<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:5</span>: <span class="message">The resource <code>R.color.purple_500</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Primary brand colors --></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_500"</span></span>>#FF6200EE<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:6</span>: <span class="message">The resource <code>R.color.purple_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Primary brand colors --></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_700"</span></span>>#FF3700B3<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  9 </span>      <span class="comment">&lt;!-- Focus Flow brand colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:7</span>: <span class="message">The resource <code>R.color.teal_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"teal_200"</span></span>>#FF03DAC5<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
<span class="lineno">  9 </span>      <span class="comment">&lt;!-- Focus Flow brand colors --></span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue"</span>>#FF2196F3<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:8</span>: <span class="message">The resource <code>R.color.teal_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"teal_700"</span></span>>#FF018786<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>      <span class="comment">&lt;!-- Focus Flow brand colors --></span>
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue"</span>>#FF2196F3<span class="tag">&lt;/color></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue_dark"</span>>#FF1976D2<span class="tag">&lt;/color></span>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UnusedResourcesDivLink" onclick="reveal('UnusedResourcesDiv');" />+ 68 More Occurrences...</button>
<div id="UnusedResourcesDiv" style="display: none">
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:13</span>: <span class="message">The resource <code>R.color.focus_orange</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue"</span>>#FF2196F3<span class="tag">&lt;/color></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue_dark"</span>>#FF1976D2<span class="tag">&lt;/color></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_green"</span>>#FF4CAF50<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"focus_orange"</span></span>>#FFFF9800<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>    
<span class="lineno"> 15 </span>    <span class="comment">&lt;!-- Launcher icon colors --></span>
<span class="lineno"> 16 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ic_launcher_background"</span>>#FF2196F3<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:19</span>: <span class="message">The resource <code>R.color.black</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ic_launcher_background"</span>>#FF2196F3<span class="tag">&lt;/color></span>
<span class="lineno"> 17 </span>    
<span class="lineno"> 18 </span>    <span class="comment">&lt;!-- System colors --></span>
<span class="caretline"><span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"black"</span></span>>#FF000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_50"</span>>#FFFAFAFA<span class="tag">&lt;/color></span>
<span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_100"</span>>#FFF5F5F5<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:21</span>: <span class="message">The resource <code>R.color.gray_50</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 18 </span>    <span class="comment">&lt;!-- System colors --></span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 21 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_50"</span></span>>#FFFAFAFA<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_100"</span>>#FFF5F5F5<span class="tag">&lt;/color></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_200"</span>>#FFEEEEEE<span class="tag">&lt;/color></span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_300"</span>>#FFE0E0E0<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:22</span>: <span class="message">The resource <code>R.color.gray_100</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"black"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_50"</span>>#FFFAFAFA<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_100"</span></span>>#FFF5F5F5<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_200"</span>>#FFEEEEEE<span class="tag">&lt;/color></span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_300"</span>>#FFE0E0E0<span class="tag">&lt;/color></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_400"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:23</span>: <span class="message">The resource <code>R.color.gray_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_50"</span>>#FFFAFAFA<span class="tag">&lt;/color></span>
<span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_100"</span>>#FFF5F5F5<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_200"</span></span>>#FFEEEEEE<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_300"</span>>#FFE0E0E0<span class="tag">&lt;/color></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_400"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_500"</span>>#FF9E9E9E<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:24</span>: <span class="message">The resource <code>R.color.gray_300</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_50"</span>>#FFFAFAFA<span class="tag">&lt;/color></span>
<span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_100"</span>>#FFF5F5F5<span class="tag">&lt;/color></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_200"</span>>#FFEEEEEE<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_300"</span></span>>#FFE0E0E0<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_400"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_500"</span>>#FF9E9E9E<span class="tag">&lt;/color></span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_600"</span>>#FF757575<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:25</span>: <span class="message">The resource <code>R.color.gray_400</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_100"</span>>#FFF5F5F5<span class="tag">&lt;/color></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_200"</span>>#FFEEEEEE<span class="tag">&lt;/color></span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_300"</span>>#FFE0E0E0<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_400"</span></span>>#FFBDBDBD<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_500"</span>>#FF9E9E9E<span class="tag">&lt;/color></span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_600"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_700"</span>>#FF616161<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:26</span>: <span class="message">The resource <code>R.color.gray_500</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_200"</span>>#FFEEEEEE<span class="tag">&lt;/color></span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_300"</span>>#FFE0E0E0<span class="tag">&lt;/color></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_400"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_500"</span></span>>#FF9E9E9E<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_600"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_700"</span>>#FF616161<span class="tag">&lt;/color></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_800"</span>>#FF424242<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:27</span>: <span class="message">The resource <code>R.color.gray_600</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_300"</span>>#FFE0E0E0<span class="tag">&lt;/color></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_400"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_500"</span>>#FF9E9E9E<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_600"</span></span>>#FF757575<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_700"</span>>#FF616161<span class="tag">&lt;/color></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_800"</span>>#FF424242<span class="tag">&lt;/color></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_900"</span>>#FF212121<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:28</span>: <span class="message">The resource <code>R.color.gray_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_400"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_500"</span>>#FF9E9E9E<span class="tag">&lt;/color></span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_600"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_700"</span></span>>#FF616161<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_800"</span>>#FF424242<span class="tag">&lt;/color></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_900"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="lineno"> 31 </span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:29</span>: <span class="message">The resource <code>R.color.gray_800</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_500"</span>>#FF9E9E9E<span class="tag">&lt;/color></span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_600"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_700"</span>>#FF616161<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_800"</span></span>>#FF424242<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_900"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="lineno"> 31 </span>
<span class="lineno"> 32 </span>    <span class="comment">&lt;!-- Light theme colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:30</span>: <span class="message">The resource <code>R.color.gray_900</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_600"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_700"</span>>#FF616161<span class="tag">&lt;/color></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"gray_800"</span>>#FF424242<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"gray_900"</span></span>>#FF212121<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>
<span class="lineno"> 32 </span>    <span class="comment">&lt;!-- Light theme colors --></span>
<span class="lineno"> 33 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue_light"</span>>#FF64B5F6<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:35</span>: <span class="message">The resource <code>R.color.text_primary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 32 </span>    <span class="comment">&lt;!-- Light theme colors --></span>
<span class="lineno"> 33 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue_light"</span>>#FF64B5F6<span class="tag">&lt;/color></span>
<span class="lineno"> 34 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_green_light"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 35 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_primary"</span></span>>#FF212121<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 36 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_secondary"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="lineno"> 37 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface_light"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 38 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"on_surface_light"</span>>#FF000000<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:36</span>: <span class="message">The resource <code>R.color.text_secondary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 33 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_blue_light"</span>>#FF64B5F6<span class="tag">&lt;/color></span>
<span class="lineno"> 34 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_green_light"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="lineno"> 35 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_primary"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 36 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_secondary"</span></span>>#FF757575<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 37 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface_light"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 38 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"on_surface_light"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 39 </span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:37</span>: <span class="message">The resource <code>R.color.surface_light</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 34 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"focus_green_light"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="lineno"> 35 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_primary"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="lineno"> 36 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_secondary"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 37 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"surface_light"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 38 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"on_surface_light"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 39 </span>
<span class="lineno"> 40 </span>    <span class="comment">&lt;!-- Dark theme colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:38</span>: <span class="message">The resource <code>R.color.on_surface_light</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 35 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_primary"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="lineno"> 36 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_secondary"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="lineno"> 37 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface_light"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 38 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"on_surface_light"</span></span>>#FF000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 39 </span>
<span class="lineno"> 40 </span>    <span class="comment">&lt;!-- Dark theme colors --></span>
<span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_background"</span>>#FF121212<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:42</span>: <span class="message">The resource <code>R.color.dark_background_soft</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>
<span class="lineno"> 40 </span>    <span class="comment">&lt;!-- Dark theme colors --></span>
<span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_background"</span>>#FF121212<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 42 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"dark_background_soft"</span></span>>#FF1E1E1E<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 43 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_surface"</span>>#FF1F1F1F<span class="tag">&lt;/color></span>
<span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_on_surface"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 45 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_text_primary"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:44</span>: <span class="message">The resource <code>R.color.dark_on_surface</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_background"</span>>#FF121212<span class="tag">&lt;/color></span>
<span class="lineno"> 42 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_background_soft"</span>>#FF1E1E1E<span class="tag">&lt;/color></span>
<span class="lineno"> 43 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_surface"</span>>#FF1F1F1F<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"dark_on_surface"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 45 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_text_primary"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 46 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_text_secondary"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="lineno"> 47 </span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:45</span>: <span class="message">The resource <code>R.color.dark_text_primary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 42 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_background_soft"</span>>#FF1E1E1E<span class="tag">&lt;/color></span>
<span class="lineno"> 43 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_surface"</span>>#FF1F1F1F<span class="tag">&lt;/color></span>
<span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_on_surface"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 45 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"dark_text_primary"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 46 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_text_secondary"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>    <span class="comment">&lt;!-- High contrast colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:46</span>: <span class="message">The resource <code>R.color.dark_text_secondary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 43 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_surface"</span>>#FF1F1F1F<span class="tag">&lt;/color></span>
<span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_on_surface"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 45 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_text_primary"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 46 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"dark_text_secondary"</span></span>>#FFBDBDBD<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>    <span class="comment">&lt;!-- High contrast colors --></span>
<span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_primary"</span>>#FF000000<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:49</span>: <span class="message">The resource <code>R.color.high_contrast_primary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 46 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"dark_text_secondary"</span>>#FFBDBDBD<span class="tag">&lt;/color></span>
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>    <span class="comment">&lt;!-- High contrast colors --></span>
<span class="caretline"><span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_primary"</span></span>>#FF000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 50 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_accent"</span>>#FFFF0000<span class="tag">&lt;/color></span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_text"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_surface"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:50</span>: <span class="message">The resource <code>R.color.high_contrast_accent</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>
<span class="lineno"> 48 </span>    <span class="comment">&lt;!-- High contrast colors --></span>
<span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_primary"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 50 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_accent"</span></span>>#FFFF0000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_text"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_surface"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 53 </span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:51</span>: <span class="message">The resource <code>R.color.high_contrast_text</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 48 </span>    <span class="comment">&lt;!-- High contrast colors --></span>
<span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_primary"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 50 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_accent"</span>>#FFFF0000<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 51 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_text"</span></span>>#FF000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_surface"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 53 </span>
<span class="lineno"> 54 </span>    <span class="comment">&lt;!-- High contrast dark colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:52</span>: <span class="message">The resource <code>R.color.high_contrast_surface</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_primary"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 50 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_accent"</span>>#FFFF0000<span class="tag">&lt;/color></span>
<span class="lineno"> 51 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_text"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_surface"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 53 </span>
<span class="lineno"> 54 </span>    <span class="comment">&lt;!-- High contrast dark colors --></span>
<span class="lineno"> 55 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_primary"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:55</span>: <span class="message">The resource <code>R.color.high_contrast_dark_primary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_surface"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 53 </span>
<span class="lineno"> 54 </span>    <span class="comment">&lt;!-- High contrast dark colors --></span>
<span class="caretline"><span class="lineno"> 55 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_dark_primary"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 56 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_accent"</span>>#FFFF4444<span class="tag">&lt;/color></span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_text"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 58 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_surface"</span>>#FF000000<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:56</span>: <span class="message">The resource <code>R.color.high_contrast_dark_accent</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 53 </span>
<span class="lineno"> 54 </span>    <span class="comment">&lt;!-- High contrast dark colors --></span>
<span class="lineno"> 55 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_primary"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 56 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_dark_accent"</span></span>>#FFFF4444<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_text"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 58 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_surface"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 59 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_background"</span>>#FF000000<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:57</span>: <span class="message">The resource <code>R.color.high_contrast_dark_text</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 54 </span>    <span class="comment">&lt;!-- High contrast dark colors --></span>
<span class="lineno"> 55 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_primary"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 56 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_accent"</span>>#FFFF4444<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 57 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_dark_text"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 58 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_surface"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 59 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_background"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 60 </span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:58</span>: <span class="message">The resource <code>R.color.high_contrast_dark_surface</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 55 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_primary"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 56 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_accent"</span>>#FFFF4444<span class="tag">&lt;/color></span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_text"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 58 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_dark_surface"</span></span>>#FF000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 59 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_background"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 60 </span>
<span class="lineno"> 61 </span>    <span class="comment">&lt;!-- ADHD-friendly colors --></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:59</span>: <span class="message">The resource <code>R.color.high_contrast_dark_background</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 56 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_accent"</span>>#FFFF4444<span class="tag">&lt;/color></span>
<span class="lineno"> 57 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_text"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 58 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_surface"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 59 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"high_contrast_dark_background"</span></span>>#FF000000<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 60 </span>
<span class="lineno"> 61 </span>    <span class="comment">&lt;!-- ADHD-friendly colors --></span>
<span class="lineno"> 62 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_calm_blue"</span>>#FF81C784<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:62</span>: <span class="message">The resource <code>R.color.adhd_calm_blue</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 59 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"high_contrast_dark_background"</span>>#FF000000<span class="tag">&lt;/color></span>
<span class="lineno"> 60 </span>
<span class="lineno"> 61 </span>    <span class="comment">&lt;!-- ADHD-friendly colors --></span>
<span class="caretline"><span class="lineno"> 62 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"adhd_calm_blue"</span></span>>#FF81C784<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 63 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_focus_green"</span>>#FF66BB6A<span class="tag">&lt;/color></span>
<span class="lineno"> 64 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_warning_amber"</span>>#FFFFB74D<span class="tag">&lt;/color></span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_error_red"</span>>#FFE57373<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:63</span>: <span class="message">The resource <code>R.color.adhd_focus_green</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 60 </span>
<span class="lineno"> 61 </span>    <span class="comment">&lt;!-- ADHD-friendly colors --></span>
<span class="lineno"> 62 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_calm_blue"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 63 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"adhd_focus_green"</span></span>>#FF66BB6A<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 64 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_warning_amber"</span>>#FFFFB74D<span class="tag">&lt;/color></span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_error_red"</span>>#FFE57373<span class="tag">&lt;/color></span>
<span class="lineno"> 66 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_success_green"</span>>#FF81C784<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:64</span>: <span class="message">The resource <code>R.color.adhd_warning_amber</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 61 </span>    <span class="comment">&lt;!-- ADHD-friendly colors --></span>
<span class="lineno"> 62 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_calm_blue"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="lineno"> 63 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_focus_green"</span>>#FF66BB6A<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 64 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"adhd_warning_amber"</span></span>>#FFFFB74D<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_error_red"</span>>#FFE57373<span class="tag">&lt;/color></span>
<span class="lineno"> 66 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_success_green"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="lineno"> 67 </span><span class="tag">&lt;/resources></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:65</span>: <span class="message">The resource <code>R.color.adhd_error_red</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 62 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_calm_blue"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="lineno"> 63 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_focus_green"</span>>#FF66BB6A<span class="tag">&lt;/color></span>
<span class="lineno"> 64 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_warning_amber"</span>>#FFFFB74D<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 65 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"adhd_error_red"</span></span>>#FFE57373<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 66 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_success_green"</span>>#FF81C784<span class="tag">&lt;/color></span>
<span class="lineno"> 67 </span><span class="tag">&lt;/resources></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:66</span>: <span class="message">The resource <code>R.color.adhd_success_green</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 63 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_focus_green"</span>>#FF66BB6A<span class="tag">&lt;/color></span>
<span class="lineno"> 64 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_warning_amber"</span>>#FFFFB74D<span class="tag">&lt;/color></span>
<span class="lineno"> 65 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"adhd_error_red"</span>>#FFE57373<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 66 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"adhd_success_green"</span></span>>#FF81C784<span class="tag">&lt;/color></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 67 </span><span class="tag">&lt;/resources></span>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:4</span>: <span class="message">The resource <code>R.dimen.text_size_headline</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- ADHD-friendly text sizes --></span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_headline"</span></span>>24sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title"</span>>20sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body"</span>>16sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption"</span>>14sp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:5</span>: <span class="message">The resource <code>R.dimen.text_size_title</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- ADHD-friendly text sizes --></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline"</span>>24sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_title"</span></span>>20sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body"</span>>16sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption"</span>>14sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_small"</span>>12sp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:6</span>: <span class="message">The resource <code>R.dimen.text_size_body</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- ADHD-friendly text sizes --></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline"</span>>24sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title"</span>>20sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_body"</span></span>>16sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption"</span>>14sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_small"</span>>12sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  9 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:7</span>: <span class="message">The resource <code>R.dimen.text_size_caption</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  4 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline"</span>>24sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title"</span>>20sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body"</span>>16sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno">  7 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_caption"</span></span>>14sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  8 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_small"</span>>12sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  9 </span>    
<span class="lineno"> 10 </span>    <span class="comment">&lt;!-- Large text sizes for accessibility --></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:8</span>: <span class="message">The resource <code>R.dimen.text_size_small</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  5 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title"</span>>20sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body"</span>>16sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption"</span>>14sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno">  8 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_small"</span></span>>12sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  9 </span>    
<span class="lineno"> 10 </span>    <span class="comment">&lt;!-- Large text sizes for accessibility --></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline_large"</span>>28sp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:11</span>: <span class="message">The resource <code>R.dimen.text_size_headline_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_small"</span>>12sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  9 </span>    
<span class="lineno"> 10 </span>    <span class="comment">&lt;!-- Large text sizes for accessibility --></span>
<span class="caretline"><span class="lineno"> 11 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_headline_large"</span></span>>28sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title_large"</span>>24sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body_large"</span>>18sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption_large"</span>>16sp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:12</span>: <span class="message">The resource <code>R.dimen.text_size_title_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>    
<span class="lineno"> 10 </span>    <span class="comment">&lt;!-- Large text sizes for accessibility --></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline_large"</span>>28sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 12 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_title_large"</span></span>>24sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body_large"</span>>18sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption_large"</span>>16sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 15 </span>    
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:13</span>: <span class="message">The resource <code>R.dimen.text_size_body_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    <span class="comment">&lt;!-- Large text sizes for accessibility --></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline_large"</span>>28sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title_large"</span>>24sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_body_large"</span></span>>18sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption_large"</span>>16sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 15 </span>    
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- Extra large text sizes for high accessibility needs --></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:14</span>: <span class="message">The resource <code>R.dimen.text_size_caption_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline_large"</span>>28sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title_large"</span>>24sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body_large"</span>>18sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 14 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_caption_large"</span></span>>16sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>    
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- Extra large text sizes for high accessibility needs --></span>
<span class="lineno"> 17 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline_xl"</span>>32sp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:17</span>: <span class="message">The resource <code>R.dimen.text_size_headline_xl</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 14 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption_large"</span>>16sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 15 </span>    
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- Extra large text sizes for high accessibility needs --></span>
<span class="caretline"><span class="lineno"> 17 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_headline_xl"</span></span>>32sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_title_xl"</span>>28sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body_xl"</span>>22sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption_xl"</span>>20sp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:18</span>: <span class="message">The resource <code>R.dimen.text_size_title_xl</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>    
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- Extra large text sizes for high accessibility needs --></span>
<span class="lineno"> 17 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_headline_xl"</span>>32sp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"text_size_title_xl"</span></span>>28sp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_body_xl"</span>>22sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption_xl"</span>>20sp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 21 </span>    
</pre>

<br/><b>NOTE: 23 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>skip-libraries</b> (default is true):<br/>
Whether the unused resource check should skip reporting unused resources in libraries.<br/>
<br/>
Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with <code>checkDependencies=true</code>).<br/>
<br/>
However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnusedResources"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"skip-libraries"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Productivity"></a>
<a name="UseKtx"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseKtxCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use KTX extension function</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt">../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt</a>:397</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 394 </span>         modifier = Modifier
<span class="lineno"> 395 </span>             .size(<span class="number">32.d</span>p)
<span class="lineno"> 396 </span>             .background(
<span class="caretline"><span class="lineno"> 397 </span>                 Color(<span class="warning">android.graphics.Color.parseColor(color)</span>),&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 398 </span>                 shape = androidx.compose.foundation.shape.CircleShape
<span class="lineno"> 399 </span>             )
<span class="lineno"> 400 </span>             .clickable { selectedColor = color }
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt">../../src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.kt</a>:431</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 428 </span>         modifier = Modifier
<span class="lineno"> 429 </span>             .size(<span class="number">32.d</span>p)
<span class="lineno"> 430 </span>             .background(
<span class="caretline"><span class="lineno"> 431 </span>                 Color(<span class="warning">android.graphics.Color.parseColor(color)</span>),&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 432 </span>                 shape = androidx.compose.foundation.shape.CircleShape
<span class="lineno"> 433 </span>             )
<span class="lineno"> 434 </span>             .clickable { selectedColor = color }
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:71</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno">  68 </span>    }
<span class="lineno">  69 </span>    
<span class="lineno">  70 </span>    <span class="keyword">val</span> categoryColor = <span class="keyword">try</span> {
<span class="caretline"><span class="lineno">  71 </span>        Color(<span class="warning">android.graphics.Color.parseColor(category.categoryColor)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  72 </span>    } catch (e: Exception) {
<span class="lineno">  73 </span>        MaterialTheme.colors.primary
<span class="lineno">  74 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt">../../src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.kt</a>:581</span>: <span class="message">Use the KTX extension function <code>String.toColorInt</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 578 </span>    }
<span class="lineno"> 579 </span>
<span class="lineno"> 580 </span>    <span class="keyword">val</span> categoryColor = <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 581 </span>        Color(<span class="warning">android.graphics.Color.parseColor(category.categoryColor)</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 582 </span>    } catch (e: Exception) {
<span class="lineno"> 583 </span>        MaterialTheme.colors.primary
<span class="lineno"> 584 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/security/SecurityManager.kt">../../src/main/java/com/focusflow/security/SecurityManager.kt</a>:153</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 150 </span>    suspend <span class="keyword">fun</span> storeSecureData(key: String, value: String) {
<span class="lineno"> 151 </span>        withContext(Dispatchers.IO) {
<span class="lineno"> 152 </span>            <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 153 </span>                <span class="warning">encryptedPrefs.edit()</span>.putString(key, value).apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 154 </span>            } catch (e: Exception) {
<span class="lineno"> 155 </span>                ErrorHandler.logError(<span class="string">"Failed to store secure data"</span>, e)
<span class="lineno"> 156 </span>                <span class="keyword">throw</span> SecurityException(<span class="string">"Failed to store secure data"</span>, e)
</pre>

<span class="location"><a href="../../src/main/java/com/focusflow/security/SecurityManager.kt">../../src/main/java/com/focusflow/security/SecurityManager.kt</a>:276</span>: <span class="message">Use the KTX extension function <code>SharedPreferences.edit</code> instead?</span><br /><pre class="errorlines">
<span class="lineno"> 273 </span>        withContext(Dispatchers.IO) {
<span class="lineno"> 274 </span>            <span class="keyword">try</span> {
<span class="lineno"> 275 </span>                <span class="comment">// Clear encrypted preferences</span>
<span class="caretline"><span class="lineno"> 276 </span>                <span class="warning">encryptedPrefs.edit()</span>.clear().apply()&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 277 </span>                
<span class="lineno"> 278 </span>                <span class="comment">// Remove keys from keystore</span>
<span class="lineno"> 279 </span>                <span class="keyword">if</span> (keyStore.containsAlias(KEYSTORE_ALIAS)) {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseKtx" style="display: none;">
The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>remove-defaults</b> (default is true):<br/>
Whether to skip arguments that match the defaults provided by the extension.<br/>
<br/>
Extensions often provide default values for some of the parameters. For example:
<pre>
fun Path.readLines(charset: Charset = Charsets.UTF_8): List&lt;String> { return Files.readAllLines(this, charset) }
</pre>
This lint check will by default automatically omit parameters that match the default, so if your code was calling<br/>

<pre>
Files.readAllLines(file, Charset.UTF_8)
</pre>
lint would replace this with
<pre>
file.readLines()
</pre>
rather than<br/>

<pre>
file.readLines(Charset.UTF_8
</pre>
You can turn this behavior off using this option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"remove-defaults"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
<b>require-present</b> (default is true):<br/>
Whether to only offer extensions already available.<br/>
<br/>
This option lets you only have lint suggest extension replacements if those extensions are already available on the class path (in other words, you're already depending on the library containing the extension method.)<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UseKtx"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"require-present"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>To suppress this error, use the issue id "UseKtx" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseKtx</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseKtxLink" onclick="reveal('explanationUseKtx');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseKtxCardLink" onclick="hideid('UseKtxCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AutoboxingStateCreation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>mutableStateOf&lt;T>()</code> when <code>T</code> is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for <code>Int</code>, <code>Long</code>, <code>Float</code>, and <code>Double</code> when the state does not need to track null values and does not override the default <code>SnapshotMutationPolicy</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AutoboxingStateValueProperty<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Avoid using the generic value accessor when using a State objects with a specialized types. Usages of the generic value property result in an unnecessary autoboxing operation whenever the state's value is read or written to. Use the specialized value accessor or property delegation to avoid unnecessary allocations.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BadConfigurationProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
An <code>android.app.Application</code> must implement <code>androidx.work.Configuration.Provider</code><br/>
for on-demand initialization.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BadPeriodicWorkRequestEnqueue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>enqueue()</code> for `PeriodicWorkRequest`s, you might end up enqueuing<br/>
duplicate requests unintentionally. You should be using<br/>
<code>enqueueUniquePeriodicWork</code> with an <code>ExistingPeriodicWorkPolicy.KEEP</code> instead.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableDestinationInComposeScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Composable destinations should only be constructed directly within a NavGraphBuilder scope. Composable destinations cannot not be nested, and you should use the <code>navigation</code> function to create a nested graph instead.<br/><div class="vendor">
Vendor: Jetpack Navigation Compose<br/>
Identifier: androidx.navigation.compose<br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableModifierFactory<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions that need to be aware of the composition should use androidx.compose.ui.composed {} in their implementation instead of being marked as @Composable. This allows Modifiers to be referenced in top level variables and constructed outside of the composition.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
@Composable functions without a return type should use similar naming to classes, starting with an uppercase letter and ending with a noun. @Composable functions with a return type should be treated as normal Kotlin functions, starting with a lowercase letter.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableNavGraphInComposeScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Composable destinations should only be constructed directly within a NavGraphBuilder scope.<br/><div class="vendor">
Vendor: Jetpack Navigation Compose<br/>
Identifier: androidx.navigation.compose<br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CompositionLocalNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
CompositionLocal properties should be prefixed with <code>Local</code>. This helps make it clear at their use site that these values are local to the current composition. Typically the full name will be <code>Local</code> + the type of the CompositionLocal, for example val LocalFoo = compositionLocalOf { Foo() }.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConflictingOnColor<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In the Material color system background colors have a corresponding 'on' color which is used for the content color inside a component. For example, a button colored <code>primary</code> will have <code>onPrimary</code> text. Because of this, it is important that there is only one possible <code>onColor</code> for a given color value, otherwise there is no way to know which 'on' color should be used inside a component. To fix this either use the same 'on' color for identical background colors, or use a different background color for each 'on' color.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CoroutineCreationDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a coroutine with <code>async</code> or <code>launch</code> during composition is often incorrect - this means that a coroutine will be created even if the composition fails / is rolled back, and it also means that multiple coroutines could end up mutating the same state, causing inconsistent results. Instead, use <code>LaunchedEffect</code> and create coroutines inside the suspending block. The block will only run after a successful composition, and will cancel existing coroutines when <code>key</code> changes, allowing correct cleanup.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeepLinkInActivityDestination<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attaching a &lt;deeplink> to an &lt;activity> destination will never give                 the right behavior when using an implicit deep link on another app's task                 (where the system back should immediately take the user back to the app that                 triggered the deep link). Instead, attach the deep link directly to                 the second activity (either by manually writing the appropriate &lt;intent-filter>                 or by adding the &lt;deeplink> to the start destination of a nav host in that second                 activity).<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DetachAndAttachSameFragment<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When doing a FragmentTransaction that includes both attach()                 and detach() operations being committed on the same fragment instance, it is a                 no-op. The reason for this is that the FragmentManager optimizes all operations                 within a single transaction so the attach() and detach() cancel each other out                 and neither is actually executed. To get the desired behavior, you should separate                 the attach() and detach() calls into separate FragmentTransactions.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DialogFragmentCallbacksDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using a <code>DialogFragment</code>, the <code>setOnCancelListener</code> and                 <code>setOnDismissListener</code> callback functions within the <code>onCreateDialog</code> function                  __must not be used__ because the <code>DialogFragment</code> owns these callbacks.                  Instead the respective <code>onCancel</code> and <code>onDismiss</code> functions can be used to                  achieve the desired effect.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EmptyNavDeepLink<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attempting to create an empty NavDeepLink will result in an IllegalStateException at runtime. You may set these arguments within the lambda of the call to navDeepLink.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FieldSiteTargetOnQualifierAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
It's redundant to use 'field:' site-targets for qualifier annotations.<br/><div class="vendor">
Vendor: Google<br/>
Identifier: com.google.dagger:dagger-lint<br/>
Contact: <a href="https://github.com/google/dagger">https://github.com/google/dagger</a><br/>
Feedback: <a href="https://github.com/google/dagger/issues">https://github.com/google/dagger/issues</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FlowOperatorInvokedInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling a Flow operator function within composition will result in a new Flow being created every recomposition, which will reset collectAsState() and cause other related problems. Instead Flow operators should be called inside <code>remember</code>, or a side effect such as LaunchedEffect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentAddMenuProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FrequentlyChangedStateReadInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This property is observable and is updated after every scroll or remeasure. If you use it in the composable function directly, it will be recomposed on every change, causing potential performance issues including infinity recomposition loops. Prefer wrapping it with derivedStateOf to use calculation based on this property in composition or collect changes inside LaunchedEffect instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IdleBatteryChargingConstraints<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Some devices are never considered charging and idle at the same time.<br/>
Consider removing one of these constraints.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidColorHexValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined / incorrect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPeriodicWorkRequestInterval<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The interval duration for a <code>PeriodicWorkRequest</code> must be at least 15 minutes.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">JvmStaticProvidesInObjectDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
It's redundant to annotate @Provides functions in object classes with @JvmStatic.<br/><div class="vendor">
Vendor: Google<br/>
Identifier: com.google.dagger:dagger-lint<br/>
Contact: <a href="https://github.com/google/dagger">https://github.com/google/dagger</a><br/>
Feedback: <a href="https://github.com/google/dagger/issues">https://github.com/google/dagger/issues</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LaunchDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>launch</code> during composition is incorrect. Doing so will cause launch to be called multiple times resulting in a RuntimeException. Instead, use <code>SideEffect</code> and <code>launch</code> inside of the suspending block. The block will only run after a successful composition.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingColorAlphaChannel<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined. For example, passing 0xFF0000 will result in a missing alpha channel, so the color will not appear visible.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryExtensionFunction<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should be defined as extension functions on Modifier to allow modifiers to be fluently chained.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should return Modifier as their type, and not a subtype of Modifier (such as Modifier.Element).<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryUnreferencedReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions are fluently chained to construct a chain of Modifier objects that will be applied to a layout. As a result, each factory function <i>must</i> use the receiver <code>Modifier</code> parameter, to ensure that the function is returning a chain that includes previous items in the chain. Make sure the returned chain either explicitly includes <code>this</code>, such as <code>return this.then(MyModifier)</code> or implicitly by returning a chain that starts with an implicit call to another factory function, such as <code>return myModifier()</code>, where <code>myModifier</code> is defined as <code>fun Modifier.myModifier(): Modifier</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierNodeInspectableProperties<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
ModifierNodeElements may override inspectableProperties() to provide information about the modifier in the layout inspector. The default implementation attempts to read all of the properties on the class reflectively, which may not comprehensively or effectively describe the modifier.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first (or only) Modifier parameter in a Composable function should follow the following rules:<br/>
- Be named <code>modifier</code><br/>
- Have a type of <code>Modifier</code><br/>
- Either have no default value, or have a default value of <code>Modifier</code><br/>
- If optional, be the first optional parameter in the parameter list<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModuleCompanionObjects<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Companion objects in @Module-annotated classes are considered part of the API.<br/><div class="vendor">
Vendor: Google<br/>
Identifier: com.google.dagger:dagger-lint<br/>
Contact: <a href="https://github.com/google/dagger">https://github.com/google/dagger</a><br/>
Feedback: <a href="https://github.com/google/dagger/issues">https://github.com/google/dagger/issues</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModuleCompanionObjectsNotInModuleParent<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Companion objects in @Module-annotated classes are considered part of the API. This<br/>
companion object is not a companion to an @Module-annotated class though, and should be<br/>
moved to a top-level object declaration instead otherwise Dagger will ignore companion<br/>
object.<br/><div class="vendor">
Vendor: Google<br/>
Identifier: com.google.dagger:dagger-lint<br/>
Contact: <a href="https://github.com/google/dagger">https://github.com/google/dagger</a><br/>
Feedback: <a href="https://github.com/google/dagger/issues">https://github.com/google/dagger/issues</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MultipleAwaitPointerEventScopes<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. Multiple calls to awaitPointerEventScope may exit the scope. During this time there is no guarantee that the events will be queued and some events may be dropped. It is recommended to use a single top-level block and perform the pointer events processing within such block.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MutableCollectionMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Writes to mutable collections inside a MutableState will not cause a recomposition - only writes to the MutableState itself will. In most cases you should either use a read-only collection (such as List or Map) and assign a new instance to the MutableState when your data changes, or you can use an snapshot-backed collection such as SnapshotStateList or SnapshotStateMap which will correctly cause a recomposition when their contents are modified.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoCollectCallFound<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
You must call collect on the progress in the onBack function. The collect call is what properly splits the callback so it knows what to do when the back gestures is started vs when it is completed. Failing to call collect will cause all code in the block to run when the gesture is started.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OpaqueUnitKey<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Certain Compose functions including <code>remember</code>, <code>LaunchedEffect</code>, and <code>DisposableEffect</code> declare (and sometimes require) one or more key parameters. When a key parameter changes, it is a signal that the previous invocation is now invalid. In certain cases, it may be required to pass <code>Unit</code> as a key to one of these functions, indicating that the invocation never becomes invalid. Using <code>Unit</code> as a key should be done infrequently, and should always be done explicitly by passing the <code>Unit</code> literal. This inspection checks for invocations where <code>Unit</code> is being passed as a key argument in any form other than the <code>Unit</code> literal. This is usually done by mistake, and can harm readability. If a Unit expression is being passed as a key, it is always equivalent to move the expression before the function invocation and pass the <code>Unit</code> literal instead.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionLaunchedDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calls to <code>launchPermissionRequest</code> or <code>launchMultiplePermissionRequest</code> in the Composition throw a runtime exception. Please call them inside a regular lambda or in a side-effect.<br/><div class="vendor">
Vendor: Accompanist Permissions<br/>
Identifier: com.google.accompanist.permissions<br/>
Feedback: <a href="https://github.com/google/accompanist/issues/new/choose">https://github.com/google/accompanist/issues/new/choose</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ProduceStateDoesNotAssignValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
produceState returns an observable State using values assigned inside the producer lambda. If the lambda never assigns (i.e <code>value = foo</code>), then the State will never change. Make sure to assign a value when the source you are producing values from changes / emits a new value. For sample usage see the produceState documentation.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A call to <code>remember</code> that returns <code>Unit</code> is always an error. This typically happens when using <code>remember</code> to mutate variables on an object. <code>remember</code> is executed during the composition, which means that if the composition fails or is happening on a separate thread, the mutated variables may not reflect the true state of the composition. Instead, use <code>SideEffect</code> to make deferred changes once the composition succeeds, or mutate <code>MutableState</code> backed variables directly, as these will handle composition failure for you.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberSaveableSaverParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first parameter to <code>rememberSaveable</code> is a vararg parameter for inputs that when changed will cause the state to reset. Passing a <code>Saver</code> object to this parameter is an error, as the intention is to pass the <code>Saver</code> object to the saver parameter. Since the saver parameter is not the first parameter, it must be explicitly named.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime.saveable<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RemoveWorkManagerInitializer<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If an <code>android.app.Application</code> implements <code>androidx.work.Configuration.Provider</code>,<br/>
the default <code>androidx.startup.InitializationProvider</code> needs to be removed from the<br/>
AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ReturnFromAwaitPointerEventScope<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Pointer Input events are queued inside awaitPointerEventScope. By using the return value of awaitPointerEventScope one might unexpectedly lose events. If another awaitPointerEventScope is restarted there is no guarantee that the events will persist between those calls. In this case you should keep all events inside the awaitPointerEventScope block<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyForegroundServiceType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using the setForegroundAsync() API, the application must override &lt;service /> entry for <code>SystemForegroundService</code> to include the foreground service type in the  <code>AndroidManifest.xml</code> file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyJobSchedulerIdRange<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>JobScheduler</code> APIs directly, <code>WorkManager</code> requires that developers specify a range of <code>JobScheduler</code> ids that are safe for <code>WorkManager</code> to use so the `id`s do not collide. <br/>
For more information look at <code>androidx.work.Configuration.Builder.setJobSchedulerJobIdRange(int, int)</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StateFlowValueCalledInComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling StateFlow.value within composition will not observe changes to the StateFlow, so changes might not be reflected within the composition. Instead you should use stateFlow.collectAsState() to observe changes to the StateFlow, and recompose when it changes.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousCompositionLocalModifierRead<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Jetpack Compose is unable to send updated values of a CompositionLocal when it's read in a Modifier.Node's initializer and onAttach() or onDetach() callbacks. Modifier.Node's callbacks are not aware of snapshot reads, and their lifecycle callbacks are not invoked on every recomposition. If you read a CompositionLocal in onAttach() or onDetach(), you will only get the CompositionLocal's value once at the moment of the read, which may lead to unexpected behaviors. We recommend instead reading CompositionLocals at time-of-use in callbacks that apply your Modifier's behavior, like measure() for LayoutModifierNode, draw() for DrawModifierNode, and so on. To observe the value of the CompositionLocal manually, extend from the ObserverNode interface and place the read inside an observeReads {} block within the onObservedReadsChanged() callback.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TestManifestGradleConfiguration<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The androidx.compose.ui:ui-test-manifest dependency is needed for launching a Compose host, such as with createComposeRule. However, it only needs to be present in testing configurations therefore use this dependency with the debugImplementation configuration<br/><div class="moreinfo">More info: <a href="https://developer.android.com/jetpack/compose/testing#setup">https://developer.android.com/jetpack/compose/testing#setup</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.compose.ui.test.manifest<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=741505">https://issuetracker.google.com/issues/new?component=741505</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnnecessaryComposedModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.composed</code> allows invoking @Composable functions when creating a <code>Modifier</code> instance - for example, using <code>remember</code> to have instance-specific state, allowing the same <code>Modifier</code> object to be safely used in multiple places. Using <code>Modifier.composed</code> without calling any @Composable functions inside is unnecessary, and since the Modifier is no longer skippable, this can cause a lot of extra work inside the composed body, leading to worse performance.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedAnimatable<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Animatable instances created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the Animatable to an object that is not created during composition, or wrap the Animatable in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedGetBackStackEntry<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Backstack entries retrieved during composition need to be `remember`ed, otherwise they will be retrieved from the navController again, and be changed. You also need to pass in a key of a NavBackStackEntry to the remember call or they will not be updated properly. If this is in a <code>NavGraphBuilder.composable</code> scope, you should pass in the lambda's given entry as the key. Either hoist the state to an object that is not created during composition, or wrap the state in a call to <code>remember</code> with a <code>NavBackStackEntry</code> as a key.<br/><div class="vendor">
Vendor: Jetpack Navigation Compose<br/>
Identifier: androidx.navigation.compose<br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
State objects created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the state to an object that is not created during composition, or wrap the state in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeRepeatOnLifecycleDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used with the viewLifecycleOwner                 in Fragments as opposed to lifecycleOwner.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedContentLambdaTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in AnimatedContent works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly animated. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous transition between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedCrossfadeTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>content</code> lambda in Crossfade works as a lookup function that returns the corresponding content based on the parameter (a state of type <code>T</code>). It is important for this lambda to return content <i>specific</i> to the input parameter, so that the different contents can be properly crossfaded. Not using the input parameter to the content lambda will result in the same content for different input (i.e. target state) and therefore an erroneous crossfade between the exact same content.`<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedMaterialScaffoldPaddingParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The <code>content</code> lambda in Scaffold has a padding parameter which will include any inner padding for the content due to app bars. If this parameter is ignored, then content may be obscured by the app bars resulting in visual issues or elements that can't be interacted with.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.material<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedTransitionTargetStateParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Transition.animate* functions provide a target state parameter in the lambda that will be used to calculate the value for a given state. This target state parameter in the lambda may or may not be the same as the actual state, as the animation system occasionally needs to look up target values for other states to do proper seeking/tooling preview. Relying on other state than the provided <code>targetState</code> could also result in unnecessary recompositions. Therefore, it is generally considered an error if this <code>targetState</code> parameter is not used.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.animation.core<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAndroidAlpha<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ColorStateList</code> uses app:alpha without <code>android:alpha</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAppTint<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForColorStateLists<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of color state lists<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableApis<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of compound text view drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>TextView</code> uses <code>android:</code> compound drawable attributes instead of <code>app:</code> ones<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseGetLayoutInflater<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Using LayoutInflater.from(Context) can return a LayoutInflater                  that does not have the correct theme.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseOfNonLambdaOffsetOverload<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.offset()</code> is recommended to be used with static arguments only to avoid unnecessary recompositions. <code>Modifier.offset{ }</code> is preferred in the cases where the arguments are backed by a <code>State</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.foundation<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRxSetProgress2<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>setCompletableProgress(...)</code> instead of `setProgress(...) in <code>RxWorker</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSupportActionBar<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>AppCompatActivity.setSupportActionBar</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialCode<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>SwitchMaterial</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>SwitchMaterial</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingOnClickInXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Old versions of the platform do not properly support resolving <code>android:onClick</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WorkerHasAPublicModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When you define a ListenableWorker which is constructed using the <br/>
default WorkerFactory, the ListenableWorker sub-type needs to be public.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterNaming<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should use the name <code>content</code> for the parameter.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterPosition<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should place the parameter at the end of the parameter list, so it can be used as a trailing lambda.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>PsiEquivalenceUtil.areElementsEquivalent(PsiElement, PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivacySandboxBlockedCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Many APIs are unavailable in the Privacy Sandbox, depending on the <code>targetSdk</code>.<br/>
<br/>
If your code is designed to run in the sandbox (and never outside the sandbox) then you should remove the blocked calls to avoid exceptions at runtime.<br/>
<br/>
If your code is part of a library that can be executed both inside and outside the sandbox, surround the code with <code>if (!Process.isSdkSandbox()) { ... }</code> (or use your own field or method annotated with <code>@ChecksRestrictedEnvironment</code>) to avoid executing blocked calls when in the sandbox. Or, add the <code>@RestrictedForEnvironment</code> annotation to the containing method if the entire method should not be called when in the sandbox.<br/>
<br/>
This check is disabled by default, and should only be enabled in modules that may execute in the Privacy Sandbox.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>