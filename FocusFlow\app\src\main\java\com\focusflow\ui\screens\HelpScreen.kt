package com.focusflow.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.focusflow.data.model.*
import com.focusflow.ui.components.help.*
import com.focusflow.ui.theme.ADHDFriendlyColors
import com.focusflow.viewmodel.HelpViewModel

/**
 * Main Help & Support screen for FocusFlow
 * ADHD-friendly design with clear navigation and progressive disclosure
 */
@Composable
fun HelpScreen(
    navController: NavController,
    viewModel: HelpViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    
    LaunchedEffect(Unit) {
        viewModel.loadHelpContent()
        viewModel.recordHelpSession()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .semantics {
                contentDescription = "Help and Support screen"
            }
    ) {
        // Header with search
        HelpHeader(
            searchQuery = uiState.searchQuery,
            onSearchQueryChange = viewModel::updateSearchQuery,
            onSearch = viewModel::performSearch,
            onClearSearch = viewModel::clearSearch
        )
        
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = ADHDFriendlyColors.focusBlue
                    )
                }
            }
            
            uiState.searchQuery.isNotEmpty() -> {
                HelpSearchResults(
                    searchResult = uiState.searchResult,
                    userProgress = uiState.userProgress,
                    onArticleClick = { article ->
                        navController.navigate("help_article/${article.id}")
                    },
                    onBookmarkClick = viewModel::toggleBookmark
                )
            }
            
            else -> {
                HelpMainContent(
                    categories = uiState.categories,
                    featuredArticles = uiState.featuredArticles,
                    quickTip = uiState.dailyTip,
                    userProgress = uiState.userProgress,
                    onCategoryClick = { category ->
                        navController.navigate("help_category/${category.id}")
                    },
                    onArticleClick = { article ->
                        navController.navigate("help_article/${article.id}")
                    },
                    onBookmarkClick = viewModel::toggleBookmark,
                    onDismissTip = viewModel::dismissDailyTip
                )
            }
        }
    }
}

@Composable
private fun HelpHeader(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onSearch: () -> Unit,
    onClearSearch: () -> Unit
) {
    Surface(
        color = MaterialTheme.colors.surface,
        elevation = 4.dp
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Title
            Text(
                text = "Help & Support",
                style = MaterialTheme.typography.h4,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.semantics {
                    heading()
                }
            )
            
            Text(
                text = "Find answers and learn how to make the most of FocusFlow",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Search bar
            HelpSearchBar(
                query = searchQuery,
                onQueryChange = onSearchQueryChange,
                onSearch = onSearch,
                placeholder = "Search help articles, FAQs, and tips..."
            )
        }
    }
}

@Composable
private fun HelpMainContent(
    categories: List<HelpCategory>,
    featuredArticles: List<HelpArticle>,
    quickTip: HelpQuickTip?,
    userProgress: HelpUserProgress,
    onCategoryClick: (HelpCategory) -> Unit,
    onArticleClick: (HelpArticle) -> Unit,
    onBookmarkClick: (String) -> Unit,
    onDismissTip: () -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Daily tip (if available)
        quickTip?.let { tip ->
            item {
                QuickTipCard(
                    tip = tip,
                    onDismiss = onDismissTip,
                    modifier = Modifier.semantics {
                        contentDescription = "Daily ADHD tip: ${tip.title}"
                    }
                )
            }
        }
        
        // Quick actions
        item {
            HelpQuickActions(
                onGetStartedClick = { /* Navigate to getting started */ },
                onFAQClick = { /* Navigate to FAQ */ },
                onContactSupportClick = { /* Open contact support */ }
            )
        }
        
        // Featured articles
        if (featuredArticles.isNotEmpty()) {
            item {
                Text(
                    text = "Featured Guides",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colors.onSurface,
                    modifier = Modifier.semantics {
                        heading()
                    }
                )
            }
            
            items(featuredArticles.take(3)) { article ->
                HelpArticleCard(
                    article = article,
                    isCompleted = userProgress.completedArticles.contains(article.id),
                    isBookmarked = userProgress.bookmarkedArticles.contains(article.id),
                    onArticleClick = { onArticleClick(article) },
                    onBookmarkClick = { onBookmarkClick(article.id) }
                )
            }
        }
        
        // Categories
        item {
            Text(
                text = "Browse by Category",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.semantics {
                    heading()
                }
            )
        }
        
        items(categories) { category ->
            HelpCategoryCard(
                category = category,
                articleCount = 5, // This would come from the view model
                onClick = { onCategoryClick(category) }
            )
        }
        
        // Progress summary
        if (userProgress.completedArticles.isNotEmpty() || userProgress.completedTutorials.isNotEmpty()) {
            item {
                HelpProgressSummary(
                    userProgress = userProgress,
                    modifier = Modifier.semantics {
                        contentDescription = "Your learning progress summary"
                    }
                )
            }
        }
    }
}

@Composable
private fun HelpQuickActions(
    onGetStartedClick: () -> Unit,
    onFAQClick: () -> Unit,
    onContactSupportClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 2.dp,
        backgroundColor = ADHDFriendlyColors.focusBlue.copy(alpha = 0.05f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Quick Actions",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.semantics {
                    heading()
                }
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                QuickActionButton(
                    icon = Icons.Default.PlayArrow,
                    text = "Get Started",
                    onClick = onGetStartedClick,
                    modifier = Modifier.weight(1f)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                QuickActionButton(
                    icon = Icons.Default.QuestionAnswer,
                    text = "FAQ",
                    onClick = onFAQClick,
                    modifier = Modifier.weight(1f)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                QuickActionButton(
                    icon = Icons.Default.Support,
                    text = "Contact",
                    onClick = onContactSupportClick,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun QuickActionButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier.semantics {
            contentDescription = "$text button"
            role = Role.Button
        },
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = ADHDFriendlyColors.focusBlue
        )
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(vertical = 8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = text,
                style = MaterialTheme.typography.caption,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun HelpProgressSummary(
    userProgress: HelpUserProgress,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 2.dp,
        backgroundColor = ADHDFriendlyColors.successGreen.copy(alpha = 0.05f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.TrendingUp,
                    contentDescription = null,
                    tint = ADHDFriendlyColors.successGreen,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Your Progress",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colors.onSurface
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                ProgressStat(
                    value = userProgress.completedArticles.size,
                    label = "Articles Read",
                    color = ADHDFriendlyColors.focusBlue
                )
                
                ProgressStat(
                    value = userProgress.completedTutorials.size,
                    label = "Tutorials Done",
                    color = ADHDFriendlyColors.motivationPurple
                )
                
                ProgressStat(
                    value = userProgress.helpSessionCount,
                    label = "Help Sessions",
                    color = ADHDFriendlyColors.calmTeal
                )
            }
        }
    }
}

@Composable
private fun ProgressStat(
    value: Int,
    label: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value.toString(),
            style = MaterialTheme.typography.h5,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
    }
}

@Composable
private fun HelpSearchResults(
    searchResult: HelpSearchResult,
    userProgress: HelpUserProgress,
    onArticleClick: (HelpArticle) -> Unit,
    onBookmarkClick: (String) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        item {
            Text(
                text = "Search Results (${searchResult.totalResults})",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.semantics {
                    heading()
                }
            )
        }
        
        if (searchResult.totalResults == 0) {
            item {
                HelpEmptyState(
                    title = "No results found",
                    description = "Try different keywords or browse categories below",
                    icon = Icons.Default.SearchOff,
                    actionText = "Browse Categories",
                    onActionClick = { /* Navigate to categories */ }
                )
            }
        } else {
            items(searchResult.articles) { article ->
                HelpArticleCard(
                    article = article,
                    isCompleted = userProgress.completedArticles.contains(article.id),
                    isBookmarked = userProgress.bookmarkedArticles.contains(article.id),
                    onArticleClick = { onArticleClick(article) },
                    onBookmarkClick = { onBookmarkClick(article.id) }
                )
            }
        }
    }
}
