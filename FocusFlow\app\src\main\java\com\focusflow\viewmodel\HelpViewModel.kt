package com.focusflow.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.*
import com.focusflow.data.repository.HelpRepository
import com.focusflow.service.CrashReportingManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for Help & Support system
 * Manages help content, search, and user progress with ADHD-friendly features
 */
@HiltViewModel
class HelpViewModel @Inject constructor(
    private val helpRepository: HelpRepository,
    private val crashReportingManager: CrashReportingManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HelpUiState())
    val uiState: StateFlow<HelpUiState> = _uiState.asStateFlow()
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    init {
        loadHelpContent()
    }
    
    fun loadHelpContent() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // Load categories
                helpRepository.getAllCategories().collect { categories ->
                    _uiState.value = _uiState.value.copy(categories = categories)
                }
                
                // Load featured articles
                helpRepository.getFeaturedArticles().collect { articles ->
                    _uiState.value = _uiState.value.copy(featuredArticles = articles)
                }
                
                // Load user progress
                val userProgress = helpRepository.getUserProgress()
                _uiState.value = _uiState.value.copy(userProgress = userProgress)
                
                // Load daily tip
                val dailyTip = helpRepository.getRandomTip()
                _uiState.value = _uiState.value.copy(
                    dailyTip = dailyTip,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                crashReportingManager.logException(e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load help content"
                )
            }
        }
    }
    
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
        _uiState.value = _uiState.value.copy(searchQuery = query)
        
        if (query.isNotEmpty()) {
            performSearch()
        } else {
            clearSearchResults()
        }
    }
    
    fun performSearch() {
        val query = _searchQuery.value.trim()
        if (query.isEmpty()) return
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isSearching = true)
                
                val searchQuery = HelpSearchQuery(
                    query = query,
                    adhdSpecificOnly = false
                )
                
                val searchResult = helpRepository.searchContent(searchQuery)
                
                _uiState.value = _uiState.value.copy(
                    searchResult = searchResult,
                    isSearching = false
                )
                
                // Record search for analytics
                helpRepository.recordSearch(query)
                
            } catch (e: Exception) {
                crashReportingManager.logException(e)
                _uiState.value = _uiState.value.copy(
                    isSearching = false,
                    error = "Search failed"
                )
            }
        }
    }
    
    fun clearSearch() {
        _searchQuery.value = ""
        _uiState.value = _uiState.value.copy(
            searchQuery = "",
            searchResult = HelpSearchResult(totalResults = 0, searchTime = 0)
        )
    }
    
    private fun clearSearchResults() {
        _uiState.value = _uiState.value.copy(
            searchResult = HelpSearchResult(totalResults = 0, searchTime = 0)
        )
    }
    
    fun toggleBookmark(articleId: String) {
        viewModelScope.launch {
            try {
                helpRepository.toggleBookmark(articleId)
                
                // Update user progress in UI state
                val updatedProgress = helpRepository.getUserProgress()
                _uiState.value = _uiState.value.copy(userProgress = updatedProgress)
                
                crashReportingManager.logUserAction("bookmark_toggled", "help_article:$articleId")
                
            } catch (e: Exception) {
                crashReportingManager.logException(e)
            }
        }
    }
    
    fun markArticleAsCompleted(articleId: String) {
        viewModelScope.launch {
            try {
                helpRepository.markArticleAsCompleted(articleId)
                
                // Update user progress in UI state
                val updatedProgress = helpRepository.getUserProgress()
                _uiState.value = _uiState.value.copy(userProgress = updatedProgress)
                
                crashReportingManager.logUserAction("article_completed", "help_article:$articleId")
                
            } catch (e: Exception) {
                crashReportingManager.logException(e)
            }
        }
    }
    
    fun markTutorialAsCompleted(tutorialId: String) {
        viewModelScope.launch {
            try {
                helpRepository.markTutorialAsCompleted(tutorialId)
                
                // Update user progress in UI state
                val updatedProgress = helpRepository.getUserProgress()
                _uiState.value = _uiState.value.copy(userProgress = updatedProgress)
                
                crashReportingManager.logUserAction("tutorial_completed", "help_tutorial:$tutorialId")
                
            } catch (e: Exception) {
                crashReportingManager.logException(e)
            }
        }
    }
    
    fun dismissDailyTip() {
        _uiState.value = _uiState.value.copy(dailyTip = null)
        crashReportingManager.logUserAction("daily_tip_dismissed")
    }
    
    fun recordHelpSession() {
        viewModelScope.launch {
            try {
                helpRepository.recordHelpSession()
                crashReportingManager.logUserAction("help_session_started")
            } catch (e: Exception) {
                crashReportingManager.logException(e)
            }
        }
    }
    
    fun loadCategoryContent(categoryId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val category = helpRepository.getCategoryById(categoryId)
                
                helpRepository.getArticlesByCategory(categoryId).collect { articles ->
                    _uiState.value = _uiState.value.copy(
                        currentCategory = category,
                        categoryArticles = articles,
                        isLoading = false
                    )
                }
                
                helpRepository.getFAQsByCategory(categoryId).collect { faqs ->
                    _uiState.value = _uiState.value.copy(categoryFAQs = faqs)
                }
                
            } catch (e: Exception) {
                crashReportingManager.logException(e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load category content"
                )
            }
        }
    }
    
    fun loadArticleContent(articleId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val article = helpRepository.getArticleById(articleId)
                val tutorials = helpRepository.getTutorialsByArticle(articleId)
                
                _uiState.value = _uiState.value.copy(
                    currentArticle = article,
                    articleTutorials = tutorials,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                crashReportingManager.logException(e)
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load article content"
                )
            }
        }
    }
    
    fun getADHDSpecificTips() {
        viewModelScope.launch {
            try {
                val tips = helpRepository.getADHDSpecificTips()
                _uiState.value = _uiState.value.copy(adhdTips = tips)
            } catch (e: Exception) {
                crashReportingManager.logException(e)
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

/**
 * UI State for Help & Support system
 */
data class HelpUiState(
    val isLoading: Boolean = false,
    val isSearching: Boolean = false,
    val error: String? = null,
    val searchQuery: String = "",
    
    // Main content
    val categories: List<HelpCategory> = emptyList(),
    val featuredArticles: List<HelpArticle> = emptyList(),
    val dailyTip: HelpQuickTip? = null,
    val userProgress: HelpUserProgress = HelpUserProgress(
        userId = "default",
        lastAccessDate = ""
    ),
    
    // Search results
    val searchResult: HelpSearchResult = HelpSearchResult(
        totalResults = 0,
        searchTime = 0
    ),
    
    // Category view
    val currentCategory: HelpCategory? = null,
    val categoryArticles: List<HelpArticle> = emptyList(),
    val categoryFAQs: List<HelpFAQ> = emptyList(),
    
    // Article view
    val currentArticle: HelpArticle? = null,
    val articleTutorials: List<HelpTutorial> = emptyList(),
    
    // ADHD-specific content
    val adhdTips: List<HelpQuickTip> = emptyList()
)
