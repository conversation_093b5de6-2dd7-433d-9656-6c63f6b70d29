package com.focusflow.service;

/**
 * Data classes for marketing content
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u001a\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u007f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u0012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u0012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u0012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u0012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0006\u0012\u0006\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0011J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u00c6\u0003J\u000f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\t0\u0006H\u00c6\u0003J\u000f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\u000f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\u000f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\u000f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006H\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0006H\u00c6\u0003J\u0097\u0001\u0010(\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00062\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00030\u00062\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u00062\b\b\u0002\u0010\u0010\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010)\u001a\u00020*2\b\u0010+\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010,\u001a\u00020-H\u00d6\u0001J\t\u0010.\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0015R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0013R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0013\u00a8\u0006/"}, d2 = {"Lcom/focusflow/service/AppStoreContent;", "", "shortDescription", "", "fullDescription", "featureHighlights", "", "Lcom/focusflow/data/model/FeatureHighlight;", "screenshots", "Lcom/focusflow/data/model/MarketingScreenshot;", "keywordList", "whatMakesItSpecial", "userBenefits", "adhdSpecificBenefits", "faqSection", "Lcom/focusflow/service/MarketingFAQ;", "callToAction", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/lang/String;)V", "getAdhdSpecificBenefits", "()Ljava/util/List;", "getCallToAction", "()Ljava/lang/String;", "getFaqSection", "getFeatureHighlights", "getFullDescription", "getKeywordList", "getScreenshots", "getShortDescription", "getUserBenefits", "getWhatMakesItSpecial", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class AppStoreContent {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String shortDescription = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String fullDescription = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.FeatureHighlight> featureHighlights = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.MarketingScreenshot> screenshots = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> keywordList = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> whatMakesItSpecial = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> userBenefits = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> adhdSpecificBenefits = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.service.MarketingFAQ> faqSection = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String callToAction = null;
    
    public AppStoreContent(@org.jetbrains.annotations.NotNull
    java.lang.String shortDescription, @org.jetbrains.annotations.NotNull
    java.lang.String fullDescription, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.FeatureHighlight> featureHighlights, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.MarketingScreenshot> screenshots, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> keywordList, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> whatMakesItSpecial, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> userBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> adhdSpecificBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.MarketingFAQ> faqSection, @org.jetbrains.annotations.NotNull
    java.lang.String callToAction) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getShortDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFullDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.FeatureHighlight> getFeatureHighlights() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.MarketingScreenshot> getScreenshots() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getKeywordList() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getWhatMakesItSpecial() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getUserBenefits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getAdhdSpecificBenefits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.service.MarketingFAQ> getFaqSection() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCallToAction() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.FeatureHighlight> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.MarketingScreenshot> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.service.MarketingFAQ> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.service.AppStoreContent copy(@org.jetbrains.annotations.NotNull
    java.lang.String shortDescription, @org.jetbrains.annotations.NotNull
    java.lang.String fullDescription, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.FeatureHighlight> featureHighlights, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.MarketingScreenshot> screenshots, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> keywordList, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> whatMakesItSpecial, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> userBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> adhdSpecificBenefits, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.MarketingFAQ> faqSection, @org.jetbrains.annotations.NotNull
    java.lang.String callToAction) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}