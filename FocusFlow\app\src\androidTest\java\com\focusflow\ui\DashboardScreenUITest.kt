package com.focusflow.ui

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.focusflow.MainActivity
import com.focusflow.ui.screens.DashboardScreen
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * UI tests for DashboardScreen focusing on ADHD-friendly design validation
 * Tests visual hierarchy, accessibility, and user interaction patterns
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class DashboardScreenUITest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun dashboardScreen_displaysAllCriticalElements() {
        composeTestRule.apply {
            // Verify Safe-to-Spend widget is prominently displayed
            onNodeWithText("Safe to Spend").assertIsDisplayed()
            
            // Verify credit card summary section
            onNodeWithText("Credit Cards").assertIsDisplayed()
            
            // Verify habit streaks section
            onNodeWithText("Habit Streaks").assertIsDisplayed()
            
            // Verify virtual pet section
            onNodeWithText("Your Pet").assertIsDisplayed()
            
            // Verify quick actions are available
            onNodeWithText("Quick Actions").assertIsDisplayed()
        }
    }

    @Test
    fun safeToSpendWidget_hasProperVisualHierarchy() {
        composeTestRule.apply {
            // Safe-to-Spend should be the most prominent element
            onNodeWithText("Safe to Spend")
                .assertIsDisplayed()
                .assertHasClickAction()
            
            // Amount should be clearly visible and large
            onNode(hasTestTag("safe_to_spend_amount"))
                .assertIsDisplayed()
            
            // Should have proper color coding for different states
            onNode(hasTestTag("safe_to_spend_indicator"))
                .assertIsDisplayed()
        }
    }

    @Test
    fun quickActions_areEasilyAccessible() {
        composeTestRule.apply {
            // Quick action buttons should be large enough for easy tapping
            onNodeWithText("Add Expense")
                .assertIsDisplayed()
                .assertHasClickAction()
                .assertHeightIsAtLeast(48.dp) // Minimum touch target size
            
            onNodeWithText("Check Budget")
                .assertIsDisplayed()
                .assertHasClickAction()
                .assertHeightIsAtLeast(48.dp)
            
            onNodeWithText("Pay Bills")
                .assertIsDisplayed()
                .assertHasClickAction()
                .assertHeightIsAtLeast(48.dp)
        }
    }

    @Test
    fun creditCardSummary_showsImportantInformation() {
        composeTestRule.apply {
            // Should show total balance
            onNode(hasTestTag("total_credit_balance"))
                .assertIsDisplayed()
            
            // Should show next payment due
            onNode(hasTestTag("next_payment_due"))
                .assertIsDisplayed()
            
            // Should have clear visual indicators for urgency
            onNode(hasTestTag("payment_urgency_indicator"))
                .assertIsDisplayed()
        }
    }

    @Test
    fun habitStreaks_provideMotivation() {
        composeTestRule.apply {
            // Should show current streaks
            onNodeWithText("Expense Tracking")
                .assertIsDisplayed()
            
            onNodeWithText("Budget Check-ins")
                .assertIsDisplayed()
            
            // Should have visual streak indicators
            onAllNodes(hasTestTag("streak_indicator"))
                .assertCountEquals(3) // Assuming 3 tracked habits
        }
    }

    @Test
    fun virtualPet_isEngaging() {
        composeTestRule.apply {
            // Pet should be visible and interactive
            onNode(hasTestTag("virtual_pet"))
                .assertIsDisplayed()
                .assertHasClickAction()
            
            // Pet status should be clear
            onNode(hasTestTag("pet_status"))
                .assertIsDisplayed()
            
            // Should show pet happiness/health
            onNode(hasTestTag("pet_happiness"))
                .assertIsDisplayed()
        }
    }

    @Test
    fun navigation_isADHDFriendly() {
        composeTestRule.apply {
            // Bottom navigation should be clearly visible
            onNode(hasTestTag("bottom_navigation"))
                .assertIsDisplayed()
            
            // Navigation items should have both icons and labels
            onNodeWithText("Dashboard").assertIsDisplayed()
            onNodeWithText("Budget").assertIsDisplayed()
            onNodeWithText("Debt").assertIsDisplayed()
            onNodeWithText("Tasks").assertIsDisplayed()
            onNodeWithText("Settings").assertIsDisplayed()
            
            // Current screen should be clearly indicated
            onNode(hasTestTag("nav_dashboard"))
                .assertIsSelected()
        }
    }

    @Test
    fun errorStates_areHandledGracefully() {
        composeTestRule.apply {
            // Test error state when data fails to load
            onNode(hasTestTag("error_message"))
                .assertDoesNotExist() // Should not show error on successful load
            
            // If error exists, it should be user-friendly
            // This would be tested with mock data that triggers errors
        }
    }

    @Test
    fun loadingStates_provideProperFeedback() {
        composeTestRule.apply {
            // Loading indicators should be present during data fetch
            // This test would need to be run with delayed mock data
            
            // Verify loading doesn't block the entire UI
            onNodeWithText("Dashboard").assertIsDisplayed()
        }
    }

    @Test
    fun accessibility_meetsADHDRequirements() {
        composeTestRule.apply {
            // All interactive elements should have content descriptions
            onAllNodes(hasClickAction())
                .assertAll(hasAnyDescendant(hasContentDescription()))
            
            // Text should have sufficient contrast (tested via semantic properties)
            onAllNodes(hasText("", substring = true))
                .assertAll(isDisplayed())
            
            // Touch targets should be at least 48dp
            onAllNodes(hasClickAction())
                .assertAll(hasMinimumTouchTargetSize())
        }
    }

    @Test
    fun visualHierarchy_supportsADHDUsers() {
        composeTestRule.apply {
            // Most important information (Safe-to-Spend) should be at the top
            val safeToSpendBounds = onNodeWithText("Safe to Spend")
                .getBoundsInRoot()
            
            val quickActionsBounds = onNodeWithText("Quick Actions")
                .getBoundsInRoot()
            
            // Safe-to-Spend should be above Quick Actions
            assert(safeToSpendBounds.top < quickActionsBounds.top)
            
            // Elements should have proper spacing to reduce visual clutter
            // This would be verified through layout bounds checking
        }
    }

    @Test
    fun colorCoding_isConsistentAndMeaningful() {
        composeTestRule.apply {
            // Green should indicate positive/safe states
            onNode(hasTestTag("safe_indicator"))
                .assertIsDisplayed()
            
            // Red should indicate urgent/warning states
            onNode(hasTestTag("urgent_indicator"))
                .assertIsDisplayed()
            
            // Colors should not be the only way to convey information
            // (should also have icons or text)
        }
    }

    @Test
    fun userInteractions_provideImmediateFeedback() {
        composeTestRule.apply {
            // Tapping quick actions should provide immediate visual feedback
            onNodeWithText("Add Expense")
                .performClick()
            
            // Should either navigate or show a dialog/bottom sheet
            // This test would verify the response happens quickly
        }
    }

    @Test
    fun informationDensity_isAppropriateForADHD() {
        composeTestRule.apply {
            // Should not overwhelm with too much information at once
            // Verify progressive disclosure is used
            
            // Critical information should be immediately visible
            onNodeWithText("Safe to Spend").assertIsDisplayed()
            
            // Detailed information should be accessible but not overwhelming
            onNodeWithText("View Details")
                .assertIsDisplayed()
                .performClick()
            
            // Detailed view should open without cluttering main screen
        }
    }

    @Test
    fun motivationalElements_arePresent() {
        composeTestRule.apply {
            // Should have positive reinforcement elements
            onNode(hasTestTag("achievement_badge"))
                .assertIsDisplayed()
            
            // Progress indicators should be encouraging
            onNode(hasTestTag("progress_indicator"))
                .assertIsDisplayed()
            
            // Streak counters should be prominent
            onAllNodes(hasTestTag("streak_counter"))
                .assertCountEquals(3)
        }
    }

    @Test
    fun emergencyActions_areEasilyAccessible() {
        composeTestRule.apply {
            // Important actions like "Pay Bills" should be easily findable
            onNodeWithText("Pay Bills")
                .assertIsDisplayed()
                .assertHasClickAction()
            
            // Should have visual urgency indicators when needed
            onNode(hasTestTag("urgent_payment_indicator"))
                .assertExists() // May or may not be displayed depending on data
        }
    }
}
