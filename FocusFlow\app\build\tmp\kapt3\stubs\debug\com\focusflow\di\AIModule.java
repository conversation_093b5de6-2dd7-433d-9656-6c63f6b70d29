package com.focusflow.di;

@dagger.Module
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \f2\u00020\u0001:\u0001\fB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u0004H\'J\u0010\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\bH\'J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u000bH\'\u00a8\u0006\r"}, d2 = {"Lcom/focusflow/di/AIModule;", "", "()V", "bindAIService", "Lcom/focusflow/service/AIService;", "aiService", "bindMockAIService", "mockAIService", "Lcom/focusflow/service/MockAIService;", "bindRealAIService", "claudeAIService", "Lcom/focusflow/service/ClaudeAIService;", "Companion", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract class AIModule {
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.di.AIModule.Companion Companion = null;
    
    public AIModule() {
        super();
    }
    
    @dagger.Binds
    @javax.inject.Singleton
    @MockAI
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.service.AIService bindMockAIService(@org.jetbrains.annotations.NotNull
    com.focusflow.service.MockAIService mockAIService);
    
    @dagger.Binds
    @javax.inject.Singleton
    @RealAI
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.service.AIService bindRealAIService(@org.jetbrains.annotations.NotNull
    com.focusflow.service.ClaudeAIService claudeAIService);
    
    @dagger.Binds
    @javax.inject.Singleton
    @org.jetbrains.annotations.NotNull
    public abstract com.focusflow.service.AIService bindAIService(@MockAI
    @org.jetbrains.annotations.NotNull
    com.focusflow.service.AIService aiService);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007\u00a8\u0006\u0005"}, d2 = {"Lcom/focusflow/di/AIModule$Companion;", "", "()V", "provideOkHttpClient", "Lokhttp3/OkHttpClient;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @dagger.Provides
        @javax.inject.Singleton
        @org.jetbrains.annotations.NotNull
        public final okhttp3.OkHttpClient provideOkHttpClient() {
            return null;
        }
    }
}