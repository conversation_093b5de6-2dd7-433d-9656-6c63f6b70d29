package com.focusflow.ui.components.help

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.focusflow.data.model.*
import com.focusflow.ui.theme.ADHDFriendlyColors

/**
 * Interactive tutorial system for FocusFlow
 * ADHD-friendly step-by-step guidance with visual highlights and progress tracking
 */

@Composable
fun InteractiveTutorialOverlay(
    tutorial: HelpTutorial,
    currentStepIndex: Int,
    onStepComplete: (Int) -> Unit,
    onTutorialComplete: () -> Unit,
    onTutorialSkip: () -> Unit,
    modifier: Modifier = Modifier
) {
    val currentStep = tutorial.steps.getOrNull(currentStepIndex)
    
    if (currentStep != null) {
        Dialog(
            onDismissRequest = { /* Prevent dismissal by tapping outside */ },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.7f))
                    .semantics {
                        contentDescription = "Interactive tutorial: ${tutorial.title}, step ${currentStepIndex + 1} of ${tutorial.steps.size}"
                    }
            ) {
                // Tutorial step content
                TutorialStepCard(
                    step = currentStep,
                    stepNumber = currentStepIndex + 1,
                    totalSteps = tutorial.steps.size,
                    onNext = {
                        if (currentStepIndex < tutorial.steps.size - 1) {
                            onStepComplete(currentStepIndex + 1)
                        } else {
                            onTutorialComplete()
                        }
                    },
                    onSkip = onTutorialSkip,
                    onPrevious = if (currentStepIndex > 0) {
                        { onStepComplete(currentStepIndex - 1) }
                    } else null,
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(16.dp)
                )
                
                // Highlight target element if specified
                currentStep.targetElement?.let { targetId ->
                    TutorialHighlight(
                        targetId = targetId,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

@Composable
private fun TutorialStepCard(
    step: TutorialStep,
    stepNumber: Int,
    totalSteps: Int,
    onNext: () -> Unit,
    onSkip: () -> Unit,
    onPrevious: (() -> Unit)?,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 8.dp,
        shape = RoundedCornerShape(16.dp),
        backgroundColor = MaterialTheme.colors.surface
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            // Header with progress
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Step $stepNumber of $totalSteps",
                    style = MaterialTheme.typography.caption,
                    color = ADHDFriendlyColors.focusBlue,
                    fontWeight = FontWeight.Medium
                )
                
                IconButton(
                    onClick = onSkip,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Skip tutorial",
                        tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
            
            // Progress bar
            LinearProgressIndicator(
                progress = stepNumber.toFloat() / totalSteps.toFloat(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                color = ADHDFriendlyColors.focusBlue,
                backgroundColor = ADHDFriendlyColors.focusBlue.copy(alpha = 0.2f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Step title
            Text(
                text = step.title,
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.semantics {
                    heading()
                }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Step description
            Text(
                text = step.description,
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f),
                lineHeight = MaterialTheme.typography.body2.lineHeight * 1.2
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Step instruction
            Card(
                backgroundColor = ADHDFriendlyColors.focusBlue.copy(alpha = 0.1f),
                elevation = 0.dp,
                shape = RoundedCornerShape(8.dp)
            ) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.Top
                ) {
                    Icon(
                        imageVector = Icons.Default.TouchApp,
                        contentDescription = null,
                        tint = ADHDFriendlyColors.focusBlue,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = step.instruction,
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // ADHD tip if available
            step.adhdTip?.let { tip ->
                Spacer(modifier = Modifier.height(12.dp))
                Card(
                    backgroundColor = ADHDFriendlyColors.calmTeal.copy(alpha = 0.1f),
                    elevation = 0.dp,
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Row(
                        modifier = Modifier.padding(12.dp),
                        verticalAlignment = Alignment.Top
                    ) {
                        Icon(
                            imageVector = Icons.Default.Psychology,
                            contentDescription = "ADHD tip",
                            tint = ADHDFriendlyColors.calmTeal,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Column {
                            Text(
                                text = "ADHD Tip",
                                style = MaterialTheme.typography.caption,
                                color = ADHDFriendlyColors.calmTeal,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = tip,
                                style = MaterialTheme.typography.body2,
                                color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f)
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(20.dp))
            
            // Navigation buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Previous button
                if (onPrevious != null) {
                    OutlinedButton(
                        onClick = onPrevious,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Previous")
                    }
                } else {
                    Spacer(modifier = Modifier.width(1.dp))
                }
                
                // Next/Complete button
                Button(
                    onClick = onNext,
                    colors = ButtonDefaults.buttonColors(
                        backgroundColor = ADHDFriendlyColors.focusBlue
                    ),
                    modifier = Modifier.semantics {
                        contentDescription = if (stepNumber == totalSteps) "Complete tutorial" else "Next step"
                    }
                ) {
                    Text(
                        text = if (stepNumber == totalSteps) "Complete" else "Next",
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = if (stepNumber == totalSteps) Icons.Default.Check else Icons.Default.ArrowForward,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun TutorialHighlight(
    targetId: String,
    modifier: Modifier = Modifier
) {
    // This would implement element highlighting
    // In a real implementation, this would:
    // 1. Find the target UI element by ID
    // 2. Calculate its position and size
    // 3. Draw a highlight overlay (circle, rectangle, or custom shape)
    // 4. Animate the highlight to draw attention
    
    Box(modifier = modifier) {
        // Placeholder for highlight implementation
        // This would use Canvas or custom drawing to highlight specific UI elements
    }
}

@Composable
fun TutorialLauncher(
    tutorials: List<HelpTutorial>,
    onTutorialSelected: (HelpTutorial) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(16.dp)
    ) {
        item {
            Text(
                text = "Interactive Tutorials",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.semantics {
                    heading()
                }
            )
            
            Text(
                text = "Step-by-step guidance to master FocusFlow features",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                modifier = Modifier.padding(top = 4.dp)
            )
        }
        
        items(tutorials) { tutorial ->
            TutorialCard(
                tutorial = tutorial,
                onClick = { onTutorialSelected(tutorial) }
            )
        }
    }
}

@Composable
private fun TutorialCard(
    tutorial: HelpTutorial,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .semantics {
                contentDescription = "${tutorial.title} tutorial, ${tutorial.estimatedDuration} minutes, ${tutorial.steps.size} steps"
                role = Role.Button
            },
        elevation = 2.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = tutorial.title,
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colors.onSurface
                    )
                    
                    Text(
                        text = tutorial.description,
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "Start tutorial",
                    tint = ADHDFriendlyColors.focusBlue,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "Duration",
                        tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${tutorial.estimatedDuration} min",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Icon(
                        imageVector = Icons.Default.List,
                        contentDescription = "Steps",
                        tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${tutorial.steps.size} steps",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
                
                if (tutorial.isInteractive) {
                    Surface(
                        color = ADHDFriendlyColors.focusBlue.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Text(
                            text = "Interactive",
                            style = MaterialTheme.typography.caption,
                            color = ADHDFriendlyColors.focusBlue,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
            }
            
            // Prerequisites if any
            if (tutorial.prerequisites.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = "Prerequisites",
                        tint = ADHDFriendlyColors.warningOrange,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Prerequisites: ${tutorial.prerequisites.joinToString(", ")}",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}
