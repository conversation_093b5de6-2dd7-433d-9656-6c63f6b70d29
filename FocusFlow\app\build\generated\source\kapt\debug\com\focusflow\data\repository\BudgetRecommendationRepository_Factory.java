package com.focusflow.data.repository;

import com.focusflow.data.dao.BudgetRecommendationDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BudgetRecommendationRepository_Factory implements Factory<BudgetRecommendationRepository> {
  private final Provider<BudgetRecommendationDao> budgetRecommendationDaoProvider;

  public BudgetRecommendationRepository_Factory(
      Provider<BudgetRecommendationDao> budgetRecommendationDaoProvider) {
    this.budgetRecommendationDaoProvider = budgetRecommendationDaoProvider;
  }

  @Override
  public BudgetRecommendationRepository get() {
    return newInstance(budgetRecommendationDaoProvider.get());
  }

  public static BudgetRecommendationRepository_Factory create(
      Provider<BudgetRecommendationDao> budgetRecommendationDaoProvider) {
    return new BudgetRecommendationRepository_Factory(budgetRecommendationDaoProvider);
  }

  public static BudgetRecommendationRepository newInstance(
      BudgetRecommendationDao budgetRecommendationDao) {
    return new BudgetRecommendationRepository(budgetRecommendationDao);
  }
}
