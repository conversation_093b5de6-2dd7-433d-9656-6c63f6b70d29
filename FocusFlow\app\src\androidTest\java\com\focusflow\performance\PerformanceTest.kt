package com.focusflow.performance

import androidx.benchmark.junit4.BenchmarkRule
import androidx.benchmark.junit4.measureRepeated
import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.focusflow.MainActivity
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.system.measureTimeMillis

/**
 * Performance tests for FocusFlow focusing on ADHD user experience requirements
 * Tests response times, memory usage, and smooth interactions
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class PerformanceTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @get:Rule(order = 2)
    val benchmarkRule = BenchmarkRule()

    @Before
    fun setup() {
        hiltRule.inject()
    }

    @Test
    fun dashboardLoad_meetsADHDResponseTimeRequirements() {
        composeTestRule.apply {
            val loadTime = measureTimeMillis {
                // Wait for dashboard to fully load
                onNodeWithText("Safe to Spend")
                    .assertIsDisplayed()
                
                onNodeWithText("Quick Actions")
                    .assertIsDisplayed()
                
                onNodeWithText("Credit Cards")
                    .assertIsDisplayed()
            }
            
            // ADHD users need quick feedback - dashboard should load within 2 seconds
            assert(loadTime < 2000) { 
                "Dashboard load time $loadTime ms exceeds ADHD-friendly threshold of 2000ms" 
            }
        }
    }

    @Test
    fun userInputResponse_isFastEnoughForADHD() {
        composeTestRule.apply {
            // Test button tap response time
            val responseTime = measureTimeMillis {
                onNodeWithText("Add Expense")
                    .performClick()
                
                // Wait for response (dialog or navigation)
                onNode(hasTestTag("expense_dialog"))
                    .assertIsDisplayed()
            }
            
            // ADHD users need immediate feedback - should respond within 300ms
            assert(responseTime < 300) { 
                "Button response time $responseTime ms exceeds ADHD-friendly threshold of 300ms" 
            }
        }
    }

    @Test
    fun navigationTransitions_areSmoothAndFast() {
        composeTestRule.apply {
            benchmarkRule.measureRepeated {
                // Test navigation between main screens
                onNodeWithText("Budget")
                    .performClick()
                
                onNodeWithText("Enhanced Budget")
                    .assertIsDisplayed()
                
                onNodeWithText("Dashboard")
                    .performClick()
                
                onNodeWithText("Safe to Spend")
                    .assertIsDisplayed()
            }
        }
    }

    @Test
    fun formInput_respondsQuicklyToUserTyping() {
        composeTestRule.apply {
            // Navigate to expense form
            onNodeWithText("Add Expense")
                .performClick()
            
            val inputResponseTime = measureTimeMillis {
                onNode(hasTestTag("amount_input"))
                    .performTextInput("25.50")
                
                // Verify input is reflected immediately
                onNode(hasTestTag("amount_input"))
                    .assertTextContains("25.50")
            }
            
            // Text input should be nearly instantaneous for ADHD users
            assert(inputResponseTime < 100) { 
                "Text input response time $inputResponseTime ms exceeds threshold of 100ms" 
            }
        }
    }

    @Test
    fun databaseOperations_dontBlockUI() {
        composeTestRule.apply {
            // Test that database operations don't freeze the UI
            val startTime = System.currentTimeMillis()
            
            // Perform database-heavy operation (adding multiple expenses)
            repeat(5) { index ->
                onNodeWithText("Add Expense")
                    .performClick()
                
                onNode(hasTestTag("amount_input"))
                    .performTextInput("${10 + index}.00")
                
                onNode(hasTestTag("description_input"))
                    .performTextInput("Test expense $index")
                
                onNodeWithText("Save")
                    .performClick()
                
                // UI should remain responsive during database operations
                onNodeWithText("Dashboard")
                    .assertIsDisplayed()
            }
            
            val totalTime = System.currentTimeMillis() - startTime
            
            // Multiple database operations should complete reasonably quickly
            assert(totalTime < 5000) { 
                "Database operations took $totalTime ms, may be blocking UI" 
            }
        }
    }

    @Test
    fun memoryUsage_staysWithinReasonableLimits() {
        composeTestRule.apply {
            val runtime = Runtime.getRuntime()
            val initialMemory = runtime.totalMemory() - runtime.freeMemory()
            
            // Navigate through all main screens to load data
            listOf("Budget", "Debt", "Tasks", "Settings", "Dashboard").forEach { screen ->
                onNodeWithText(screen)
                    .performClick()
                
                waitForIdle()
            }
            
            // Force garbage collection
            runtime.gc()
            Thread.sleep(1000)
            
            val finalMemory = runtime.totalMemory() - runtime.freeMemory()
            val memoryIncrease = finalMemory - initialMemory
            val maxMemory = runtime.maxMemory()
            val memoryUsagePercent = (finalMemory * 100 / maxMemory).toInt()
            
            // Memory usage should not exceed 70% of available memory
            assert(memoryUsagePercent < 70) { 
                "Memory usage $memoryUsagePercent% exceeds 70% threshold" 
            }
            
            // Memory increase from navigation should be reasonable
            assert(memoryIncrease < 50 * 1024 * 1024) { // 50MB
                "Memory increase ${memoryIncrease / 1024 / 1024}MB from navigation is excessive" 
            }
        }
    }

    @Test
    fun scrollPerformance_isSmoothForLongLists() {
        composeTestRule.apply {
            // Navigate to a screen with scrollable content (e.g., expense list)
            onNodeWithText("Budget")
                .performClick()
            
            // Test scrolling performance
            benchmarkRule.measureRepeated {
                onNode(hasTestTag("expense_list"))
                    .performScrollToIndex(20)
                
                onNode(hasTestTag("expense_list"))
                    .performScrollToIndex(0)
            }
        }
    }

    @Test
    fun aiServiceCalls_dontBlockUserInterface() {
        composeTestRule.apply {
            // Test AI service calls don't freeze the UI
            onNodeWithText("Tasks")
                .performClick()
            
            // Trigger AI task breakdown
            onNode(hasTestTag("task_item"))
                .performClick()
            
            onNodeWithText("Break Down Task")
                .performClick()
            
            val startTime = System.currentTimeMillis()
            
            // UI should remain responsive while AI processes
            onNodeWithText("Tasks")
                .assertIsDisplayed()
            
            // Should show loading indicator
            onNode(hasTestTag("ai_loading"))
                .assertIsDisplayed()
            
            // Wait for AI response (with timeout)
            onNode(hasTestTag("ai_response"))
                .assertIsDisplayed()
            
            val responseTime = System.currentTimeMillis() - startTime
            
            // AI response should come within reasonable time
            assert(responseTime < 10000) { 
                "AI response time $responseTime ms exceeds 10 second timeout" 
            }
        }
    }

    @Test
    fun animationPerformance_maintainsFrameRate() {
        composeTestRule.apply {
            // Test that animations don't drop frames
            benchmarkRule.measureRepeated {
                // Trigger animations (e.g., screen transitions)
                onNodeWithText("Budget")
                    .performClick()
                
                waitForIdle()
                
                onNodeWithText("Dashboard")
                    .performClick()
                
                waitForIdle()
            }
        }
    }

    @Test
    fun backgroundTasks_dontImpactForegroundPerformance() {
        composeTestRule.apply {
            // Test that background operations don't slow down UI
            val startTime = System.currentTimeMillis()
            
            // Trigger background tasks (e.g., data sync, notifications)
            // This would involve triggering background services
            
            // UI should remain responsive
            onNodeWithText("Add Expense")
                .performClick()
            
            val responseTime = System.currentTimeMillis() - startTime
            
            // UI response should not be affected by background tasks
            assert(responseTime < 300) { 
                "UI response degraded by background tasks: $responseTime ms" 
            }
        }
    }

    @Test
    fun coldStart_meetsPerformanceRequirements() {
        // This test would measure app cold start time
        // Cold start should be under 3 seconds for good ADHD user experience
        
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        val packageManager = context.packageManager
        val intent = packageManager.getLaunchIntentForPackage(context.packageName)
        
        val startTime = System.currentTimeMillis()
        
        // Launch app
        context.startActivity(intent)
        
        composeTestRule.apply {
            // Wait for app to be fully loaded
            onNodeWithText("Safe to Spend")
                .assertIsDisplayed()
        }
        
        val coldStartTime = System.currentTimeMillis() - startTime
        
        // Cold start should be under 3 seconds for ADHD users
        assert(coldStartTime < 3000) { 
            "Cold start time $coldStartTime ms exceeds 3 second threshold" 
        }
    }

    @Test
    fun dataLoading_showsProgressAppropriately() {
        composeTestRule.apply {
            // Test that data loading states are handled well
            
            // Should show loading indicators for slow operations
            onNode(hasTestTag("loading_indicator"))
                .assertExists()
            
            // Loading should not block critical functionality
            onNodeWithText("Dashboard")
                .assertIsDisplayed()
            
            // Should transition smoothly from loading to loaded state
            onNodeWithText("Safe to Spend")
                .assertIsDisplayed()
        }
    }

    @Test
    fun errorRecovery_maintainsPerformance() {
        composeTestRule.apply {
            // Test that error states don't degrade performance
            
            // Trigger error condition (e.g., network failure)
            // This would involve mocking error conditions
            
            // Error handling should be fast
            val errorHandlingTime = measureTimeMillis {
                onNode(hasTestTag("error_message"))
                    .assertIsDisplayed()
                
                onNodeWithText("Try Again")
                    .performClick()
            }
            
            // Error recovery should be quick
            assert(errorHandlingTime < 1000) { 
                "Error recovery took $errorHandlingTime ms, too slow for ADHD users" 
            }
        }
    }

    @Test
    fun focusMode_maintainsOptimalPerformance() {
        composeTestRule.apply {
            // Test Focus Mode performance
            onNodeWithText("Tasks")
                .performClick()
            
            onNodeWithText("Focus Mode")
                .performClick()
            
            // Focus mode should load quickly
            val focusModeLoadTime = measureTimeMillis {
                onNodeWithText("Start Focus Session")
                    .assertIsDisplayed()
            }
            
            assert(focusModeLoadTime < 1000) { 
                "Focus mode load time $focusModeLoadTime ms too slow" 
            }
            
            // Timer should update smoothly
            onNodeWithText("Start Focus Session")
                .performClick()
            
            // Verify timer performance
            onNode(hasTestTag("focus_timer"))
                .assertIsDisplayed()
        }
    }
}
