package com.focusflow.data.repository

import com.focusflow.data.dao.HelpDao
import com.focusflow.data.model.*
import com.focusflow.service.CrashReportingManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for Help & Support system
 * Manages offline-first help content with search and analytics
 */
@Singleton
class HelpRepository @Inject constructor(
    private val helpDao: HelpDao,
    private val crashReportingManager: CrashReportingManager
) {
    
    companion object {
        private const val DEFAULT_USER_ID = "default_user"
    }
    
    // Categories
    fun getAllCategories(): Flow<List<HelpCategory>> {
        return helpDao.getAllCategories()
            .catch { e ->
                crashReportingManager.logException(e)
                emit(emptyList())
            }
    }
    
    suspend fun getCategoryById(categoryId: String): HelpCategory? {
        return try {
            helpDao.getCategoryById(categoryId)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            null
        }
    }
    
    // Articles
    fun getArticlesByCategory(categoryId: String): Flow<List<HelpArticle>> {
        return helpDao.getArticlesByCategory(categoryId)
            .catch { e ->
                crashReportingManager.logException(e)
                emit(emptyList())
            }
    }
    
    suspend fun getArticleById(articleId: String): HelpArticle? {
        return try {
            helpDao.getArticleById(articleId)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            null
        }
    }
    
    fun getFeaturedArticles(limit: Int = 5): Flow<List<HelpArticle>> {
        return helpDao.getFeaturedArticles(limit)
            .catch { e ->
                crashReportingManager.logException(e)
                emit(emptyList())
            }
    }
    
    // Search functionality
    suspend fun searchContent(query: HelpSearchQuery): HelpSearchResult {
        return try {
            val startTime = System.currentTimeMillis()
            
            val articles = if (query.categories.isEmpty() && query.contentTypes.isEmpty()) {
                helpDao.searchArticles(query.query)
            } else {
                helpDao.advancedSearchArticles(
                    query = query.query,
                    categoryId = query.categories.firstOrNull(),
                    contentType = query.contentTypes.firstOrNull()?.name,
                    difficulty = query.difficulty?.name
                )
            }
            
            val faqs = helpDao.searchFAQs(query.query)
            val searchTime = System.currentTimeMillis() - startTime
            
            HelpSearchResult(
                articles = articles,
                faqs = faqs,
                totalResults = articles.size + faqs.size,
                searchTime = searchTime,
                suggestions = generateSearchSuggestions(query.query)
            )
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            HelpSearchResult(totalResults = 0, searchTime = 0)
        }
    }
    
    private fun generateSearchSuggestions(query: String): List<String> {
        // Generate search suggestions based on common ADHD-related terms
        val adhdTerms = listOf(
            "focus", "attention", "distraction", "overwhelm", "procrastination",
            "impulse", "habit", "routine", "motivation", "organization",
            "budget", "spending", "debt", "savings", "planning"
        )
        
        return adhdTerms.filter { 
            it.contains(query, ignoreCase = true) && it != query.lowercase()
        }.take(5)
    }
    
    // Tutorials
    suspend fun getTutorialsByArticle(articleId: String): List<HelpTutorial> {
        return try {
            helpDao.getTutorialsByArticle(articleId)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            emptyList()
        }
    }
    
    suspend fun getTutorialById(tutorialId: String): HelpTutorial? {
        return try {
            helpDao.getTutorialById(tutorialId)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            null
        }
    }
    
    // FAQs
    fun getFAQsByCategory(categoryId: String): Flow<List<HelpFAQ>> {
        return helpDao.getFAQsByCategory(categoryId)
            .catch { e ->
                crashReportingManager.logException(e)
                emit(emptyList())
            }
    }
    
    fun getCommonFAQs(limit: Int = 10): Flow<List<HelpFAQ>> {
        return helpDao.getCommonFAQs(limit)
            .catch { e ->
                crashReportingManager.logException(e)
                emit(emptyList())
            }
    }
    
    // Quick Tips
    suspend fun getQuickTipsByCategory(category: String): List<HelpQuickTip> {
        return try {
            helpDao.getQuickTipsByCategory(category)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            emptyList()
        }
    }
    
    suspend fun getADHDSpecificTips(): List<HelpQuickTip> {
        return try {
            helpDao.getADHDSpecificTips()
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            emptyList()
        }
    }
    
    suspend fun getRandomTip(): HelpQuickTip? {
        return try {
            helpDao.getRandomTip()
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            null
        }
    }
    
    // User Progress
    suspend fun getUserProgress(userId: String = DEFAULT_USER_ID): HelpUserProgress {
        return try {
            helpDao.getUserProgress(userId) ?: HelpUserProgress(
                userId = userId,
                lastAccessDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).toString()
            )
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            HelpUserProgress(
                userId = userId,
                lastAccessDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).toString()
            )
        }
    }
    
    suspend fun markArticleAsCompleted(articleId: String, userId: String = DEFAULT_USER_ID) {
        try {
            val progress = getUserProgress(userId)
            val updatedCompleted = progress.completedArticles.toMutableList()
            if (!updatedCompleted.contains(articleId)) {
                updatedCompleted.add(articleId)
                helpDao.updateCompletedArticles(userId, updatedCompleted)
            }
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    suspend fun markTutorialAsCompleted(tutorialId: String, userId: String = DEFAULT_USER_ID) {
        try {
            val progress = getUserProgress(userId)
            val updatedCompleted = progress.completedTutorials.toMutableList()
            if (!updatedCompleted.contains(tutorialId)) {
                updatedCompleted.add(tutorialId)
                helpDao.updateCompletedTutorials(userId, updatedCompleted)
            }
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    suspend fun toggleBookmark(articleId: String, userId: String = DEFAULT_USER_ID) {
        try {
            val progress = getUserProgress(userId)
            val updatedBookmarks = progress.bookmarkedArticles.toMutableList()
            if (updatedBookmarks.contains(articleId)) {
                updatedBookmarks.remove(articleId)
            } else {
                updatedBookmarks.add(articleId)
            }
            helpDao.updateBookmarkedArticles(userId, updatedBookmarks)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    // Content management
    suspend fun initializeDefaultContent() {
        try {
            val categories = getDefaultCategories()
            val articles = getDefaultArticles()
            val tutorials = getDefaultTutorials()
            val faqs = getDefaultFAQs()
            val tips = getDefaultQuickTips()
            
            helpDao.refreshAllContent(categories, articles, tutorials, faqs, tips)
            crashReportingManager.log("Help content initialized successfully")
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    // Analytics
    suspend fun recordHelpSession(userId: String = DEFAULT_USER_ID) {
        try {
            val progress = getUserProgress(userId)
            val updatedProgress = progress.copy(
                helpSessionCount = progress.helpSessionCount + 1,
                lastAccessDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).toString()
            )
            helpDao.insertUserProgress(updatedProgress)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    suspend fun recordSearch(query: String, userId: String = DEFAULT_USER_ID) {
        try {
            val progress = getUserProgress(userId)
            val updatedHistory = progress.searchHistory.toMutableList()
            updatedHistory.add(0, query) // Add to beginning
            if (updatedHistory.size > 20) { // Keep only last 20 searches
                updatedHistory.removeAt(updatedHistory.size - 1)
            }
            
            val updatedProgress = progress.copy(searchHistory = updatedHistory)
            helpDao.insertUserProgress(updatedProgress)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    // Marketing content extraction
    suspend fun getMarketingContent(): MarketingContent {
        return try {
            val categories = helpDao.getAllCategories()
            val articles = helpDao.getFeaturedArticles(10)
            
            // This would extract marketing-ready content from help articles
            // Implementation would parse articles for marketing highlights
            MarketingContent(
                featureHighlights = extractFeatureHighlights(),
                userBenefits = extractUserBenefits(),
                adhdSpecificBenefits = extractADHDBenefits(),
                screenshots = extractMarketingScreenshots()
            )
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            MarketingContent(
                featureHighlights = emptyList(),
                userBenefits = emptyList(),
                adhdSpecificBenefits = emptyList(),
                screenshots = emptyList()
            )
        }
    }
    
    private fun extractFeatureHighlights(): List<FeatureHighlight> {
        // Extract feature highlights from help content for marketing
        return listOf(
            FeatureHighlight(
                title = "Safe-to-Spend Calculator",
                description = "Know exactly what you can spend without anxiety",
                benefits = listOf("Instant spending guidance", "Reduces financial stress", "Prevents overspending"),
                adhdBenefits = listOf("Eliminates decision paralysis", "Provides immediate clarity", "Reduces cognitive load"),
                category = "budgeting",
                priority = 1
            )
            // More highlights would be extracted from actual help content
        )
    }
    
    private fun extractUserBenefits(): List<String> {
        return listOf(
            "Reduce financial anxiety with clear spending guidance",
            "Build healthy money habits through gamification",
            "Get personalized ADHD-friendly financial coaching"
        )
    }
    
    private fun extractADHDBenefits(): List<String> {
        return listOf(
            "Designed specifically for ADHD cognitive patterns",
            "Reduces overwhelm with visual, intuitive interface",
            "Supports executive function with task breakdown and reminders"
        )
    }
    
    private fun extractMarketingScreenshots(): List<MarketingScreenshot> {
        return listOf(
            MarketingScreenshot(
                path = "screenshots/dashboard_hero.png",
                title = "ADHD-Friendly Dashboard",
                description = "Clear visual hierarchy with Safe-to-Spend prominently displayed",
                feature = "dashboard",
                overlayText = "See exactly what you can spend",
                isHeroImage = true
            )
        )
    }
    
    // Default content creation methods
    private fun getDefaultCategories(): List<HelpCategory> {
        return listOf(
            HelpCategory(
                id = "getting_started",
                title = "Getting Started",
                description = "Learn the basics of FocusFlow and set up your account",
                icon = "play_arrow",
                color = "#4CAF50",
                order = 1,
                adhdTips = "Start with just one feature at a time to avoid overwhelm"
            ),
            HelpCategory(
                id = "budgeting",
                title = "Visual Budgeting",
                description = "Master envelope-style budgeting designed for ADHD minds",
                icon = "account_balance",
                color = "#2196F3",
                order = 2,
                adhdTips = "Use colors and visual cues to make budgeting intuitive"
            ),
            HelpCategory(
                id = "spending",
                title = "Smart Spending",
                description = "Track expenses and control impulse purchases",
                icon = "shopping_cart",
                color = "#FF9800",
                order = 3,
                adhdTips = "Quick entry methods reduce friction and improve consistency"
            ),
            HelpCategory(
                id = "debt_management",
                title = "Debt Payoff",
                description = "Strategic debt elimination with visual progress tracking",
                icon = "credit_card",
                color = "#9C27B0",
                order = 4,
                adhdTips = "Visual progress and milestones maintain motivation"
            ),
            HelpCategory(
                id = "focus_productivity",
                title = "Focus & Tasks",
                description = "ADHD-friendly task management and focus tools",
                icon = "psychology",
                color = "#00BCD4",
                order = 5,
                adhdTips = "Break large tasks into smaller, manageable steps"
            ),
            HelpCategory(
                id = "habits_gamification",
                title = "Habits & Motivation",
                description = "Build financial habits through gamification",
                icon = "repeat",
                color = "#8BC34A",
                order = 6,
                adhdTips = "Positive reinforcement works better than punishment for ADHD"
            ),
            HelpCategory(
                id = "privacy_security",
                title = "Privacy & Security",
                description = "Understand how your data is protected",
                icon = "security",
                color = "#607D8B",
                order = 7,
                adhdTips = "Simple, clear privacy controls reduce anxiety"
            ),
            HelpCategory(
                id = "troubleshooting",
                title = "Troubleshooting",
                description = "Common issues and solutions",
                icon = "build",
                color = "#795548",
                order = 8,
                adhdTips = "Step-by-step solutions prevent frustration"
            )
        )
    }

    private fun getDefaultFAQs(): List<HelpFAQ> {
        return listOf(
            HelpFAQ(
                id = "faq_data_privacy",
                categoryId = "privacy_security",
                question = "Is my financial data safe and private?",
                answer = """
Yes! Your financial data is protected with bank-level security:

• **Local Storage First**: Most data stays on your device
• **AES-256 Encryption**: Military-grade encryption for sensitive data
• **No Data Selling**: We never sell your personal information
• **GDPR Compliant**: Full control over your data with easy export/deletion
• **Biometric Protection**: Fingerprint and face unlock support

You can review our complete privacy policy in the app settings.
                """.trimIndent(),
                isCommon = true,
                tags = listOf("privacy", "security", "data", "encryption"),
                lastUpdated = getCurrentTimestamp()
            ),
            HelpFAQ(
                id = "faq_adhd_specific",
                categoryId = "getting_started",
                question = "How is FocusFlow different from other budgeting apps?",
                answer = """
FocusFlow is specifically designed for ADHD minds:

• **Reduced Cognitive Load**: Clean, uncluttered interface
• **Visual Hierarchy**: Important information stands out clearly
• **Immediate Feedback**: Instant responses to keep you engaged
• **Positive Reinforcement**: Encouraging messages, never shame-based
• **Impulse Control Tools**: Built-in support for ADHD spending challenges
• **Task Breakdown**: Complex financial tasks made manageable
• **Gamification**: Virtual pet and achievements for motivation

Every feature considers how ADHD affects financial decision-making.
                """.trimIndent(),
                isCommon = true,
                tags = listOf("adhd", "features", "design", "difference"),
                lastUpdated = getCurrentTimestamp()
            ),
            HelpFAQ(
                id = "faq_getting_started",
                categoryId = "getting_started",
                question = "I'm overwhelmed - where should I start?",
                answer = """
Start small! Here's the ADHD-friendly approach:

**Week 1**: Just set up your Safe-to-Spend calculator
**Week 2**: Add expense tracking for one category (like dining out)
**Week 3**: Create a simple 3-category budget
**Week 4**: Explore one additional feature that interests you

Remember:
• You don't need to use every feature immediately
• Focus on building one habit at a time
• Celebrate small wins along the way
• The app will still be here when you're ready for more!
                """.trimIndent(),
                isCommon = true,
                tags = listOf("overwhelm", "getting started", "adhd", "tips"),
                lastUpdated = getCurrentTimestamp()
            )
        )
    }

    private fun getDefaultQuickTips(): List<HelpQuickTip> {
        return listOf(
            HelpQuickTip(
                id = "tip_daily_check",
                title = "Daily Safe-to-Spend Check",
                content = "Start each day by checking your Safe-to-Spend amount. This 5-second habit can prevent overspending and reduce financial anxiety throughout the day.",
                category = "budgeting",
                isADHDSpecific = true,
                icon = "lightbulb",
                color = "#4CAF50",
                showFrequency = TipFrequency.DAILY
            ),
            HelpQuickTip(
                id = "tip_expense_timing",
                title = "Log Expenses Immediately",
                content = "Track expenses right after spending while the details are fresh in your ADHD brain. Use voice notes or quick photos if typing feels like too much work.",
                category = "spending",
                isADHDSpecific = true,
                icon = "psychology",
                color = "#2196F3",
                showFrequency = TipFrequency.WEEKLY
            ),
            HelpQuickTip(
                id = "tip_visual_cues",
                title = "Customize Your Colors",
                content = "Set up category colors that make intuitive sense to you. Your ADHD brain processes visual information faster than text, so make colors work for you!",
                category = "budgeting",
                isADHDSpecific = true,
                icon = "tips",
                color = "#FF9800",
                showFrequency = TipFrequency.WEEKLY
            )
        )
    }

    private fun getCurrentTimestamp(): String {
        return Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).toString()
    }
}
