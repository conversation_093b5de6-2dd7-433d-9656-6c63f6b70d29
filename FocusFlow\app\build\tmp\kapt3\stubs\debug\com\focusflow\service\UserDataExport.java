package com.focusflow.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bs\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0007\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0007\u0012\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0007\u0012\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0007\u0012\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0007\u0012\u0006\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\u0002\u0010\u0015J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u0010%\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u00c6\u0003J\u000f\u0010&\u001a\b\u0012\u0004\u0012\u00020\n0\u0007H\u00c6\u0003J\u000f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\f0\u0007H\u00c6\u0003J\u000f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0007H\u00c6\u0003J\u000f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00100\u0007H\u00c6\u0003J\u000f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00120\u0007H\u00c6\u0003J\t\u0010+\u001a\u00020\u0014H\u00c6\u0003J\u0089\u0001\u0010,\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00072\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00072\u000e\b\u0002\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u00072\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00072\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u00072\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u00c6\u0001J\u0013\u0010-\u001a\u00020.2\b\u0010/\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00100\u001a\u000201H\u00d6\u0001J\t\u00102\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0017R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0017R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0017R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0017\u00a8\u00063"}, d2 = {"Lcom/focusflow/service/UserDataExport;", "", "exportDate", "", "userPreferences", "Lcom/focusflow/data/model/UserPreferences;", "expenses", "", "Lcom/focusflow/data/model/Expense;", "budgetCategories", "Lcom/focusflow/data/model/BudgetCategory;", "creditCards", "Lcom/focusflow/data/model/CreditCard;", "tasks", "Lcom/focusflow/data/model/Task;", "habits", "error/NonExistentClass", "wishlistItems", "Lcom/focusflow/data/model/WishlistItem;", "consentStatus", "Lcom/focusflow/service/ConsentStatus;", "(Ljava/lang/String;Lcom/focusflow/data/model/UserPreferences;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lcom/focusflow/service/ConsentStatus;)V", "getBudgetCategories", "()Ljava/util/List;", "getConsentStatus", "()Lcom/focusflow/service/ConsentStatus;", "getCreditCards", "getExpenses", "getExportDate", "()Ljava/lang/String;", "getHabits", "getTasks", "getUserPreferences", "()Lcom/focusflow/data/model/UserPreferences;", "getWishlistItems", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class UserDataExport {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String exportDate = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.UserPreferences userPreferences = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.Expense> expenses = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.CreditCard> creditCards = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.Task> tasks = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<error.NonExistentClass> habits = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.WishlistItem> wishlistItems = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.ConsentStatus consentStatus = null;
    
    public UserDataExport(@org.jetbrains.annotations.NotNull
    java.lang.String exportDate, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.UserPreferences userPreferences, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Expense> expenses, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Task> tasks, @org.jetbrains.annotations.NotNull
    java.util.List<error.NonExistentClass> habits, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.WishlistItem> wishlistItems, @org.jetbrains.annotations.NotNull
    com.focusflow.service.ConsentStatus consentStatus) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getExportDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.UserPreferences getUserPreferences() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.Expense> getExpenses() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetCategory> getBudgetCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.CreditCard> getCreditCards() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.Task> getTasks() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<error.NonExistentClass> getHabits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.WishlistItem> getWishlistItems() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.service.ConsentStatus getConsentStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.UserPreferences component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.Expense> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetCategory> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.CreditCard> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.Task> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<error.NonExistentClass> component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.WishlistItem> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.service.ConsentStatus component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.service.UserDataExport copy(@org.jetbrains.annotations.NotNull
    java.lang.String exportDate, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.UserPreferences userPreferences, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Expense> expenses, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Task> tasks, @org.jetbrains.annotations.NotNull
    java.util.List<error.NonExistentClass> habits, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.WishlistItem> wishlistItems, @org.jetbrains.annotations.NotNull
    com.focusflow.service.ConsentStatus consentStatus) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}