package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.TaskRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.service.GamificationService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import error.NonExistentClass;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FocusModeViewModel_Factory implements Factory<FocusModeViewModel> {
  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<GamificationService> gamificationServiceProvider;

  private final Provider<NonExistentClass> notificationManagerProvider;

  public FocusModeViewModel_Factory(Provider<TaskRepository> taskRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider,
      Provider<NonExistentClass> notificationManagerProvider) {
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.gamificationServiceProvider = gamificationServiceProvider;
    this.notificationManagerProvider = notificationManagerProvider;
  }

  @Override
  public FocusModeViewModel get() {
    return newInstance(taskRepositoryProvider.get(), userPreferencesRepositoryProvider.get(), gamificationServiceProvider.get(), notificationManagerProvider.get());
  }

  public static FocusModeViewModel_Factory create(Provider<TaskRepository> taskRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider,
      Provider<NonExistentClass> notificationManagerProvider) {
    return new FocusModeViewModel_Factory(taskRepositoryProvider, userPreferencesRepositoryProvider, gamificationServiceProvider, notificationManagerProvider);
  }

  public static FocusModeViewModel newInstance(TaskRepository taskRepository,
      UserPreferencesRepository userPreferencesRepository, GamificationService gamificationService,
      NonExistentClass notificationManager) {
    return new FocusModeViewModel(taskRepository, userPreferencesRepository, gamificationService, notificationManager);
  }
}
