package com.focusflow.service

import android.content.Context
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.repository.ExpenseRepository
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.CreditCardRepository
import com.focusflow.data.repository.TaskRepository
import com.focusflow.data.repository.HabitRepository
import com.focusflow.data.repository.WishlistRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * GDPR Compliance Manager for FocusFlow
 * Handles user data rights, consent management, and privacy compliance
 * Designed with ADHD user needs in mind - clear, simple, non-overwhelming
 */
@Singleton
class GDPRComplianceManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val securityManager: SecurityManager,
    private val crashReportingManager: CrashReportingManager,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val expenseRepository: ExpenseRepository,
    private val budgetCategoryRepository: BudgetCategoryRepository,
    private val creditCardRepository: CreditCardRepository,
    private val taskRepository: TaskRepository,
    private val habitRepository: HabitRepository,
    private val wishlistRepository: WishlistRepository
) {
    
    private val complianceScope = CoroutineScope(Dispatchers.IO)
    
    companion object {
        private const val CONSENT_VERSION = "1.0"
        private const val DATA_RETENTION_DAYS = 2555 // 7 years for financial data
        private const val CONSENT_KEY = "gdpr_consent"
        private const val CONSENT_DATE_KEY = "gdpr_consent_date"
        private const val DATA_PROCESSING_CONSENT_KEY = "data_processing_consent"
        private const val ANALYTICS_CONSENT_KEY = "analytics_consent"
        private const val MARKETING_CONSENT_KEY = "marketing_consent"
    }
    
    /**
     * Check if user has provided valid GDPR consent
     */
    fun hasValidConsent(): Boolean {
        return try {
            val consent = securityManager.getSecureData(CONSENT_KEY)
            val consentDate = securityManager.getSecureData(CONSENT_DATE_KEY)
            
            consent == CONSENT_VERSION && consentDate != null
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            false
        }
    }
    
    /**
     * Record user consent with timestamp
     */
    fun recordConsent(
        dataProcessing: Boolean,
        analytics: Boolean,
        marketing: Boolean
    ): Boolean {
        return try {
            val currentTime = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            
            securityManager.storeSecureData(CONSENT_KEY, CONSENT_VERSION)
            securityManager.storeSecureData(CONSENT_DATE_KEY, currentTime.toString())
            securityManager.storeSecureData(DATA_PROCESSING_CONSENT_KEY, dataProcessing.toString())
            securityManager.storeSecureData(ANALYTICS_CONSENT_KEY, analytics.toString())
            securityManager.storeSecureData(MARKETING_CONSENT_KEY, marketing.toString())
            
            crashReportingManager.log("GDPR consent recorded: processing=$dataProcessing, analytics=$analytics, marketing=$marketing")
            true
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            false
        }
    }
    
    /**
     * Get current consent status
     */
    fun getConsentStatus(): ConsentStatus {
        return try {
            ConsentStatus(
                hasConsent = hasValidConsent(),
                dataProcessing = securityManager.getSecureData(DATA_PROCESSING_CONSENT_KEY)?.toBoolean() ?: false,
                analytics = securityManager.getSecureData(ANALYTICS_CONSENT_KEY)?.toBoolean() ?: false,
                marketing = securityManager.getSecureData(MARKETING_CONSENT_KEY)?.toBoolean() ?: false,
                consentDate = securityManager.getSecureData(CONSENT_DATE_KEY)
            )
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            ConsentStatus()
        }
    }
    
    /**
     * Update specific consent preferences
     */
    fun updateConsent(
        dataProcessing: Boolean? = null,
        analytics: Boolean? = null,
        marketing: Boolean? = null
    ): Boolean {
        return try {
            dataProcessing?.let { 
                securityManager.storeSecureData(DATA_PROCESSING_CONSENT_KEY, it.toString())
            }
            analytics?.let { 
                securityManager.storeSecureData(ANALYTICS_CONSENT_KEY, it.toString())
            }
            marketing?.let { 
                securityManager.storeSecureData(MARKETING_CONSENT_KEY, it.toString())
            }
            
            crashReportingManager.log("GDPR consent updated")
            true
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            false
        }
    }
    
    /**
     * Export all user data (Right to Data Portability)
     */
    suspend fun exportUserData(): DataExportResult {
        return try {
            crashReportingManager.log("Starting user data export")
            
            val userData = UserDataExport(
                exportDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).toString(),
                userPreferences = userPreferencesRepository.getUserPreferencesSync(),
                expenses = expenseRepository.getAllExpenses().first(),
                budgetCategories = budgetCategoryRepository.getAllBudgetCategories().first(),
                creditCards = creditCardRepository.getAllCreditCards().first(),
                tasks = taskRepository.getAllTasks().first(),
                habits = habitRepository.getAllHabits().first(),
                wishlistItems = wishlistRepository.getAllWishlistItems().first(),
                consentStatus = getConsentStatus()
            )
            
            crashReportingManager.log("User data export completed successfully")
            DataExportResult.Success(userData)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            DataExportResult.Error("Failed to export data: ${e.message}")
        }
    }
    
    /**
     * Delete all user data (Right to Erasure)
     */
    suspend fun deleteAllUserData(): DataDeletionResult {
        return try {
            crashReportingManager.log("Starting complete user data deletion")
            
            // Delete all data from repositories
            expenseRepository.deleteAllExpenses()
            budgetCategoryRepository.deleteAllBudgetCategories()
            creditCardRepository.deleteAllCreditCards()
            taskRepository.deleteAllTasks()
            habitRepository.deleteAllHabits()
            wishlistRepository.deleteAllWishlistItems()
            userPreferencesRepository.deleteUserPreferences()
            
            // Clear all secure storage
            securityManager.clearAllSecureData()
            
            crashReportingManager.log("Complete user data deletion completed")
            DataDeletionResult.Success
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            DataDeletionResult.Error("Failed to delete data: ${e.message}")
        }
    }
    
    /**
     * Delete specific categories of data
     */
    suspend fun deleteSpecificData(categories: List<DataCategory>): DataDeletionResult {
        return try {
            crashReportingManager.log("Starting selective data deletion: ${categories.joinToString()}")
            
            categories.forEach { category ->
                when (category) {
                    DataCategory.EXPENSES -> expenseRepository.deleteAllExpenses()
                    DataCategory.BUDGET -> budgetCategoryRepository.deleteAllBudgetCategories()
                    DataCategory.CREDIT_CARDS -> creditCardRepository.deleteAllCreditCards()
                    DataCategory.TASKS -> taskRepository.deleteAllTasks()
                    DataCategory.HABITS -> habitRepository.deleteAllHabits()
                    DataCategory.WISHLIST -> wishlistRepository.deleteAllWishlistItems()
                    DataCategory.PREFERENCES -> userPreferencesRepository.deleteUserPreferences()
                    DataCategory.SECURITY_DATA -> securityManager.clearAllSecureData()
                }
            }
            
            crashReportingManager.log("Selective data deletion completed")
            DataDeletionResult.Success
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            DataDeletionResult.Error("Failed to delete specific data: ${e.message}")
        }
    }
    
    /**
     * Get data processing information (Right to Information)
     */
    fun getDataProcessingInfo(): DataProcessingInfo {
        return DataProcessingInfo(
            dataCollected = listOf(
                "Financial transactions and expenses",
                "Budget categories and allocations",
                "Credit card information (encrypted)",
                "Task and habit tracking data",
                "User preferences and settings",
                "App usage analytics (if consented)"
            ),
            purposeOfProcessing = listOf(
                "Provide personal finance management features",
                "Track expenses and budgets",
                "Offer ADHD-friendly financial coaching",
                "Improve app performance and user experience",
                "Provide personalized insights and recommendations"
            ),
            legalBasis = "Consent (Article 6(1)(a) GDPR) and Legitimate Interest (Article 6(1)(f) GDPR)",
            dataRetentionPeriod = "$DATA_RETENTION_DAYS days (7 years for financial records)",
            dataRecipients = listOf(
                "Local device storage only",
                "Firebase services (if analytics consented)",
                "AI service providers (for coaching features, if consented)"
            ),
            userRights = listOf(
                "Right to access your data",
                "Right to rectify incorrect data",
                "Right to erase your data",
                "Right to restrict processing",
                "Right to data portability",
                "Right to object to processing",
                "Right to withdraw consent"
            )
        )
    }
    
    /**
     * Check if data should be automatically deleted due to retention period
     */
    suspend fun checkDataRetention(): List<String> {
        val warnings = mutableListOf<String>()
        
        try {
            val consentDate = securityManager.getSecureData(CONSENT_DATE_KEY)
            if (consentDate != null) {
                // Check if data is approaching retention limit
                // This would involve checking timestamps on data entries
                // and warning about upcoming automatic deletion
            }
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            warnings.add("Unable to check data retention status")
        }
        
        return warnings
    }
    
    /**
     * Withdraw all consent and stop data processing
     */
    suspend fun withdrawConsent(): Boolean {
        return try {
            // Update consent status
            updateConsent(
                dataProcessing = false,
                analytics = false,
                marketing = false
            )
            
            // Stop any ongoing data processing
            // This would involve stopping analytics, AI services, etc.
            
            crashReportingManager.log("User consent withdrawn")
            true
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            false
        }
    }
}

/**
 * Data classes for GDPR compliance
 */
data class ConsentStatus(
    val hasConsent: Boolean = false,
    val dataProcessing: Boolean = false,
    val analytics: Boolean = false,
    val marketing: Boolean = false,
    val consentDate: String? = null
)

data class UserDataExport(
    val exportDate: String,
    val userPreferences: com.focusflow.data.model.UserPreferences?,
    val expenses: List<com.focusflow.data.model.Expense>,
    val budgetCategories: List<com.focusflow.data.model.BudgetCategory>,
    val creditCards: List<com.focusflow.data.model.CreditCard>,
    val tasks: List<com.focusflow.data.model.Task>,
    val habits: List<com.focusflow.data.model.Habit>,
    val wishlistItems: List<com.focusflow.data.model.WishlistItem>,
    val consentStatus: ConsentStatus
)

sealed class DataExportResult {
    data class Success(val data: UserDataExport) : DataExportResult()
    data class Error(val message: String) : DataExportResult()
}

sealed class DataDeletionResult {
    object Success : DataDeletionResult()
    data class Error(val message: String) : DataDeletionResult()
}

enum class DataCategory {
    EXPENSES,
    BUDGET,
    CREDIT_CARDS,
    TASKS,
    HABITS,
    WISHLIST,
    PREFERENCES,
    SECURITY_DATA
}

data class DataProcessingInfo(
    val dataCollected: List<String>,
    val purposeOfProcessing: List<String>,
    val legalBasis: String,
    val dataRetentionPeriod: String,
    val dataRecipients: List<String>,
    val userRights: List<String>
)
