package com.focusflow.service;

/**
 * ADHD-specific crash reporting events
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/focusflow/service/ADHDCrashEvents;", "", "()V", "AI_SERVICE_TIMEOUT", "", "BUDGET_CALCULATION_ERROR", "DATA_SYNC_FAILED", "FOCUS_SESSION_INTERRUPTED", "GAMIFICATION_ERROR", "HABIT_TRACKING_ERROR", "IMPULSE_CONTROL_TRIGGERED", "NOTIFICATION_DELIVERY_FAILED", "PROCRASTINATION_DETECTION_ERROR", "TASK_BREAKDOWN_FAILED", "app_debug"})
public final class ADHDCrashEvents {
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String TASK_BREAKDOWN_FAILED = "task_breakdown_failed";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String FOCUS_SESSION_INTERRUPTED = "focus_session_interrupted";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String IMPULSE_CONTROL_TRIGGERED = "impulse_control_triggered";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String BUDGET_CALCULATION_ERROR = "budget_calculation_error";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String AI_SERVICE_TIMEOUT = "ai_service_timeout";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String NOTIFICATION_DELIVERY_FAILED = "notification_delivery_failed";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String DATA_SYNC_FAILED = "data_sync_failed";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String GAMIFICATION_ERROR = "gamification_error";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String HABIT_TRACKING_ERROR = "habit_tracking_error";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String PROCRASTINATION_DETECTION_ERROR = "procrastination_detection_error";
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.ADHDCrashEvents INSTANCE = null;
    
    private ADHDCrashEvents() {
        super();
    }
}