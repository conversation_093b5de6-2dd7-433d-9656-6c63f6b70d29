package com.focusflow.ui.components.help;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000B\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\u001aR\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a(\u0010\r\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a\u001a\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u001a4\u0010\u0012\u001a\u00020\u00012\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00030\u00142\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001aV\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u00052\u0006\u0010\u001a\u001a\u00020\u00052\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\u000e\u0010\u001d\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\t2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0003\u00a8\u0006\u001e"}, d2 = {"InteractiveTutorialOverlay", "", "tutorial", "Lcom/focusflow/data/model/HelpTutorial;", "currentStepIndex", "", "onStepComplete", "Lkotlin/Function1;", "onTutorialComplete", "Lkotlin/Function0;", "onTutorialSkip", "modifier", "Landroidx/compose/ui/Modifier;", "TutorialCard", "onClick", "TutorialHighlight", "targetId", "", "TutorialLauncher", "tutorials", "", "onTutorialSelected", "TutorialStepCard", "step", "Lcom/focusflow/data/model/TutorialStep;", "stepNumber", "totalSteps", "onNext", "onSkip", "onPrevious", "app_debug"})
public final class InteractiveTutorialKt {
    
    /**
     * Interactive tutorial system for FocusFlow
     * ADHD-friendly step-by-step guidance with visual highlights and progress tracking
     */
    @androidx.compose.runtime.Composable
    public static final void InteractiveTutorialOverlay(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpTutorial tutorial, int currentStepIndex, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onStepComplete, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onTutorialComplete, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onTutorialSkip, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void TutorialStepCard(com.focusflow.data.model.TutorialStep step, int stepNumber, int totalSteps, kotlin.jvm.functions.Function0<kotlin.Unit> onNext, kotlin.jvm.functions.Function0<kotlin.Unit> onSkip, kotlin.jvm.functions.Function0<kotlin.Unit> onPrevious, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void TutorialHighlight(java.lang.String targetId, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TutorialLauncher(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpTutorial> tutorials, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.HelpTutorial, kotlin.Unit> onTutorialSelected, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void TutorialCard(com.focusflow.data.model.HelpTutorial tutorial, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, androidx.compose.ui.Modifier modifier) {
    }
}