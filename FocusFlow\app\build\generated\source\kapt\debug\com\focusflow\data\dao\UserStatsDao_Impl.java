package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.UserStats;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserStatsDao_Impl implements UserStatsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserStats> __insertionAdapterOfUserStats;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<UserStats> __updateAdapterOfUserStats;

  private final SharedSQLiteStatement __preparedStmtOfAddPoints;

  private final SharedSQLiteStatement __preparedStmtOfUpdateExpenseLoggingStreak;

  private final SharedSQLiteStatement __preparedStmtOfUpdateBudgetAdherenceStreak;

  private final SharedSQLiteStatement __preparedStmtOfIncrementExpensesLogged;

  private final SharedSQLiteStatement __preparedStmtOfAddDebtPaid;

  private final SharedSQLiteStatement __preparedStmtOfIncrementAchievementsUnlocked;

  public UserStatsDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserStats = new EntityInsertionAdapter<UserStats>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_stats` (`id`,`totalPoints`,`currentLevel`,`expenseLoggingStreak`,`budgetAdherenceStreak`,`totalExpensesLogged`,`totalDebtPaid`,`achievementsUnlocked`,`lastActivityDate`) VALUES (?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserStats entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTotalPoints());
        statement.bindLong(3, entity.getCurrentLevel());
        statement.bindLong(4, entity.getExpenseLoggingStreak());
        statement.bindLong(5, entity.getBudgetAdherenceStreak());
        statement.bindLong(6, entity.getTotalExpensesLogged());
        statement.bindDouble(7, entity.getTotalDebtPaid());
        statement.bindLong(8, entity.getAchievementsUnlocked());
        final String _tmp = __converters.fromLocalDateTime(entity.getLastActivityDate());
        if (_tmp == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp);
        }
      }
    };
    this.__updateAdapterOfUserStats = new EntityDeletionOrUpdateAdapter<UserStats>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `user_stats` SET `id` = ?,`totalPoints` = ?,`currentLevel` = ?,`expenseLoggingStreak` = ?,`budgetAdherenceStreak` = ?,`totalExpensesLogged` = ?,`totalDebtPaid` = ?,`achievementsUnlocked` = ?,`lastActivityDate` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserStats entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTotalPoints());
        statement.bindLong(3, entity.getCurrentLevel());
        statement.bindLong(4, entity.getExpenseLoggingStreak());
        statement.bindLong(5, entity.getBudgetAdherenceStreak());
        statement.bindLong(6, entity.getTotalExpensesLogged());
        statement.bindDouble(7, entity.getTotalDebtPaid());
        statement.bindLong(8, entity.getAchievementsUnlocked());
        final String _tmp = __converters.fromLocalDateTime(entity.getLastActivityDate());
        if (_tmp == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp);
        }
        statement.bindLong(10, entity.getId());
      }
    };
    this.__preparedStmtOfAddPoints = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_stats SET totalPoints = totalPoints + ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateExpenseLoggingStreak = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_stats SET expenseLoggingStreak = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateBudgetAdherenceStreak = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_stats SET budgetAdherenceStreak = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementExpensesLogged = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_stats SET totalExpensesLogged = totalExpensesLogged + 1 WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfAddDebtPaid = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_stats SET totalDebtPaid = totalDebtPaid + ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementAchievementsUnlocked = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_stats SET achievementsUnlocked = achievementsUnlocked + 1 WHERE id = 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertUserStats(final UserStats userStats,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserStats.insert(userStats);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserStats(final UserStats userStats,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserStats.handle(userStats);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object addPoints(final int points, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfAddPoints.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, points);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfAddPoints.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateExpenseLoggingStreak(final int streak,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateExpenseLoggingStreak.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, streak);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateExpenseLoggingStreak.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateBudgetAdherenceStreak(final int streak,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateBudgetAdherenceStreak.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, streak);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateBudgetAdherenceStreak.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object incrementExpensesLogged(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementExpensesLogged.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementExpensesLogged.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object addDebtPaid(final double amount, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfAddDebtPaid.acquire();
        int _argIndex = 1;
        _stmt.bindDouble(_argIndex, amount);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfAddDebtPaid.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object incrementAchievementsUnlocked(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementAchievementsUnlocked.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementAchievementsUnlocked.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<UserStats> getUserStats() {
    final String _sql = "SELECT `user_stats`.`id` AS `id`, `user_stats`.`totalPoints` AS `totalPoints`, `user_stats`.`currentLevel` AS `currentLevel`, `user_stats`.`expenseLoggingStreak` AS `expenseLoggingStreak`, `user_stats`.`budgetAdherenceStreak` AS `budgetAdherenceStreak`, `user_stats`.`totalExpensesLogged` AS `totalExpensesLogged`, `user_stats`.`totalDebtPaid` AS `totalDebtPaid`, `user_stats`.`achievementsUnlocked` AS `achievementsUnlocked`, `user_stats`.`lastActivityDate` AS `lastActivityDate` FROM user_stats WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"user_stats"}, new Callable<UserStats>() {
      @Override
      @Nullable
      public UserStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTotalPoints = 1;
          final int _cursorIndexOfCurrentLevel = 2;
          final int _cursorIndexOfExpenseLoggingStreak = 3;
          final int _cursorIndexOfBudgetAdherenceStreak = 4;
          final int _cursorIndexOfTotalExpensesLogged = 5;
          final int _cursorIndexOfTotalDebtPaid = 6;
          final int _cursorIndexOfAchievementsUnlocked = 7;
          final int _cursorIndexOfLastActivityDate = 8;
          final UserStats _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpTotalPoints;
            _tmpTotalPoints = _cursor.getInt(_cursorIndexOfTotalPoints);
            final int _tmpCurrentLevel;
            _tmpCurrentLevel = _cursor.getInt(_cursorIndexOfCurrentLevel);
            final int _tmpExpenseLoggingStreak;
            _tmpExpenseLoggingStreak = _cursor.getInt(_cursorIndexOfExpenseLoggingStreak);
            final int _tmpBudgetAdherenceStreak;
            _tmpBudgetAdherenceStreak = _cursor.getInt(_cursorIndexOfBudgetAdherenceStreak);
            final int _tmpTotalExpensesLogged;
            _tmpTotalExpensesLogged = _cursor.getInt(_cursorIndexOfTotalExpensesLogged);
            final double _tmpTotalDebtPaid;
            _tmpTotalDebtPaid = _cursor.getDouble(_cursorIndexOfTotalDebtPaid);
            final int _tmpAchievementsUnlocked;
            _tmpAchievementsUnlocked = _cursor.getInt(_cursorIndexOfAchievementsUnlocked);
            final LocalDateTime _tmpLastActivityDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfLastActivityDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfLastActivityDate);
            }
            _tmpLastActivityDate = __converters.toLocalDateTime(_tmp);
            _result = new UserStats(_tmpId,_tmpTotalPoints,_tmpCurrentLevel,_tmpExpenseLoggingStreak,_tmpBudgetAdherenceStreak,_tmpTotalExpensesLogged,_tmpTotalDebtPaid,_tmpAchievementsUnlocked,_tmpLastActivityDate);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUserStatsSync(final Continuation<? super UserStats> $completion) {
    final String _sql = "SELECT `user_stats`.`id` AS `id`, `user_stats`.`totalPoints` AS `totalPoints`, `user_stats`.`currentLevel` AS `currentLevel`, `user_stats`.`expenseLoggingStreak` AS `expenseLoggingStreak`, `user_stats`.`budgetAdherenceStreak` AS `budgetAdherenceStreak`, `user_stats`.`totalExpensesLogged` AS `totalExpensesLogged`, `user_stats`.`totalDebtPaid` AS `totalDebtPaid`, `user_stats`.`achievementsUnlocked` AS `achievementsUnlocked`, `user_stats`.`lastActivityDate` AS `lastActivityDate` FROM user_stats WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserStats>() {
      @Override
      @Nullable
      public UserStats call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTotalPoints = 1;
          final int _cursorIndexOfCurrentLevel = 2;
          final int _cursorIndexOfExpenseLoggingStreak = 3;
          final int _cursorIndexOfBudgetAdherenceStreak = 4;
          final int _cursorIndexOfTotalExpensesLogged = 5;
          final int _cursorIndexOfTotalDebtPaid = 6;
          final int _cursorIndexOfAchievementsUnlocked = 7;
          final int _cursorIndexOfLastActivityDate = 8;
          final UserStats _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpTotalPoints;
            _tmpTotalPoints = _cursor.getInt(_cursorIndexOfTotalPoints);
            final int _tmpCurrentLevel;
            _tmpCurrentLevel = _cursor.getInt(_cursorIndexOfCurrentLevel);
            final int _tmpExpenseLoggingStreak;
            _tmpExpenseLoggingStreak = _cursor.getInt(_cursorIndexOfExpenseLoggingStreak);
            final int _tmpBudgetAdherenceStreak;
            _tmpBudgetAdherenceStreak = _cursor.getInt(_cursorIndexOfBudgetAdherenceStreak);
            final int _tmpTotalExpensesLogged;
            _tmpTotalExpensesLogged = _cursor.getInt(_cursorIndexOfTotalExpensesLogged);
            final double _tmpTotalDebtPaid;
            _tmpTotalDebtPaid = _cursor.getDouble(_cursorIndexOfTotalDebtPaid);
            final int _tmpAchievementsUnlocked;
            _tmpAchievementsUnlocked = _cursor.getInt(_cursorIndexOfAchievementsUnlocked);
            final LocalDateTime _tmpLastActivityDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfLastActivityDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfLastActivityDate);
            }
            _tmpLastActivityDate = __converters.toLocalDateTime(_tmp);
            _result = new UserStats(_tmpId,_tmpTotalPoints,_tmpCurrentLevel,_tmpExpenseLoggingStreak,_tmpBudgetAdherenceStreak,_tmpTotalExpensesLogged,_tmpTotalDebtPaid,_tmpAchievementsUnlocked,_tmpLastActivityDate);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
