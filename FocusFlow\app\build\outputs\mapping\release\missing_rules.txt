# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn com.google.errorprone.annotations.CanIgnoreReturnValue
-dontwarn com.google.errorprone.annotations.CheckReturnValue
-dontwarn com.google.errorprone.annotations.Immutable
-dontwarn com.google.errorprone.annotations.RestrictedApi
-dontwarn kotlinx.serialization.DeserializationStrategy
-dontwarn kotlinx.serialization.InternalSerializationApi
-dontwarn kotlinx.serialization.KSerializer
-dontwarn kotlinx.serialization.MissingFieldException
-dontwarn kotlinx.serialization.SealedClassSerializer
-dontwarn kotlinx.serialization.Serializable
-dontwarn kotlinx.serialization.SerializationException
-dontwarn kotlinx.serialization.SerializationStrategy
-dontwarn kotlinx.serialization.UnknownFieldException
-dontwarn kotlinx.serialization.descriptors.ClassSerialDescriptorBuilder
-dontwarn kotlinx.serialization.descriptors.PrimitiveKind$STRING
-dontwarn kotlinx.serialization.descriptors.PrimitiveKind
-dontwarn kotlinx.serialization.descriptors.SerialDescriptor
-dontwarn kotlinx.serialization.descriptors.SerialDescriptorsKt
-dontwarn kotlinx.serialization.encoding.CompositeDecoder
-dontwarn kotlinx.serialization.encoding.CompositeEncoder
-dontwarn kotlinx.serialization.encoding.Decoder
-dontwarn kotlinx.serialization.encoding.Encoder
-dontwarn kotlinx.serialization.internal.AbstractPolymorphicSerializer
-dontwarn kotlinx.serialization.internal.EnumSerializer
-dontwarn kotlinx.serialization.internal.IntSerializer
-dontwarn kotlinx.serialization.internal.LongSerializer
-dontwarn kotlinx.serialization.internal.ShortSerializer