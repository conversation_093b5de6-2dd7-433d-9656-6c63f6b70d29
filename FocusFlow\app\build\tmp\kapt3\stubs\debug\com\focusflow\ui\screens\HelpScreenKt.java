package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000p\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a@\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\u0080\u0001\u0010\t\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u000b2\b\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\u001a\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0003\u001a2\u0010\u001a\u001a\u00020\u00012\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\u001a\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020 2\b\b\u0002\u0010!\u001a\u00020\"H\u0007\u001a@\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\u0006\u0010\u0011\u001a\u00020\u00122\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a*\u0010&\u001a\u00020\u00012\u0006\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020\u00032\u0006\u0010*\u001a\u00020+H\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b,\u0010-\u001a0\u0010.\u001a\u00020\u00012\u0006\u0010/\u001a\u0002002\u0006\u00101\u001a\u00020\u00032\f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\u0003\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00063"}, d2 = {"HelpHeader", "", "searchQuery", "", "onSearchQueryChange", "Lkotlin/Function1;", "onSearch", "Lkotlin/Function0;", "onClearSearch", "HelpMainContent", "categories", "", "Lcom/focusflow/data/model/HelpCategory;", "featuredArticles", "Lcom/focusflow/data/model/HelpArticle;", "quickTip", "Lcom/focusflow/data/model/HelpQuickTip;", "userProgress", "Lcom/focusflow/data/model/HelpUserProgress;", "onCategoryClick", "onArticleClick", "onBookmarkClick", "onDismissTip", "HelpProgressSummary", "modifier", "Landroidx/compose/ui/Modifier;", "HelpQuickActions", "onGetStartedClick", "onFAQClick", "onContactSupportClick", "HelpScreen", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/focusflow/viewmodel/HelpViewModel;", "HelpSearchResults", "searchResult", "Lcom/focusflow/data/model/HelpSearchResult;", "ProgressStat", "value", "", "label", "color", "Landroidx/compose/ui/graphics/Color;", "ProgressStat-mxwnekA", "(ILjava/lang/String;J)V", "QuickActionButton", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "text", "onClick", "app_debug"})
public final class HelpScreenKt {
    
    /**
     * Main Help & Support screen for FocusFlow
     * ADHD-friendly design with clear navigation and progressive disclosure
     */
    @androidx.compose.runtime.Composable
    public static final void HelpScreen(@org.jetbrains.annotations.NotNull
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull
    com.focusflow.viewmodel.HelpViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void HelpHeader(java.lang.String searchQuery, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSearchQueryChange, kotlin.jvm.functions.Function0<kotlin.Unit> onSearch, kotlin.jvm.functions.Function0<kotlin.Unit> onClearSearch) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void HelpMainContent(java.util.List<com.focusflow.data.model.HelpCategory> categories, java.util.List<com.focusflow.data.model.HelpArticle> featuredArticles, com.focusflow.data.model.HelpQuickTip quickTip, com.focusflow.data.model.HelpUserProgress userProgress, kotlin.jvm.functions.Function1<? super com.focusflow.data.model.HelpCategory, kotlin.Unit> onCategoryClick, kotlin.jvm.functions.Function1<? super com.focusflow.data.model.HelpArticle, kotlin.Unit> onArticleClick, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onBookmarkClick, kotlin.jvm.functions.Function0<kotlin.Unit> onDismissTip) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void HelpQuickActions(kotlin.jvm.functions.Function0<kotlin.Unit> onGetStartedClick, kotlin.jvm.functions.Function0<kotlin.Unit> onFAQClick, kotlin.jvm.functions.Function0<kotlin.Unit> onContactSupportClick) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void QuickActionButton(androidx.compose.ui.graphics.vector.ImageVector icon, java.lang.String text, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void HelpProgressSummary(com.focusflow.data.model.HelpUserProgress userProgress, androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void HelpSearchResults(com.focusflow.data.model.HelpSearchResult searchResult, com.focusflow.data.model.HelpUserProgress userProgress, kotlin.jvm.functions.Function1<? super com.focusflow.data.model.HelpArticle, kotlin.Unit> onArticleClick, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onBookmarkClick) {
    }
}