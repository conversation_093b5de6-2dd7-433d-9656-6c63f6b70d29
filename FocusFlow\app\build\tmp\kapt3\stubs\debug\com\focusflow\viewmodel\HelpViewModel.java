package com.focusflow.viewmodel;

/**
 * ViewModel for Help & Support system
 * Manages help content, search, and user progress with ADHD-friendly features
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0012\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0012\u001a\u00020\u0013J\u0006\u0010\u0014\u001a\u00020\u0013J\b\u0010\u0015\u001a\u00020\u0013H\u0002J\u0006\u0010\u0016\u001a\u00020\u0013J\u0006\u0010\u0017\u001a\u00020\u0013J\u000e\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0019\u001a\u00020\tJ\u000e\u0010\u001a\u001a\u00020\u00132\u0006\u0010\u001b\u001a\u00020\tJ\u0006\u0010\u001c\u001a\u00020\u0013J\u000e\u0010\u001d\u001a\u00020\u00132\u0006\u0010\u0019\u001a\u00020\tJ\u000e\u0010\u001e\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020\tJ\u0006\u0010 \u001a\u00020\u0013J\u0006\u0010!\u001a\u00020\u0013J\u000e\u0010\"\u001a\u00020\u00132\u0006\u0010\u0019\u001a\u00020\tJ\u000e\u0010#\u001a\u00020\u00132\u0006\u0010$\u001a\u00020\tR\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000f\u00a8\u0006%"}, d2 = {"Lcom/focusflow/viewmodel/HelpViewModel;", "Landroidx/lifecycle/ViewModel;", "helpRepository", "Lcom/focusflow/data/repository/HelpRepository;", "crashReportingManager", "Lcom/focusflow/service/CrashReportingManager;", "(Lcom/focusflow/data/repository/HelpRepository;Lcom/focusflow/service/CrashReportingManager;)V", "_searchQuery", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_uiState", "Lcom/focusflow/viewmodel/HelpUiState;", "searchQuery", "Lkotlinx/coroutines/flow/StateFlow;", "getSearchQuery", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "clearError", "", "clearSearch", "clearSearchResults", "dismissDailyTip", "getADHDSpecificTips", "loadArticleContent", "articleId", "loadCategoryContent", "categoryId", "loadHelpContent", "markArticleAsCompleted", "markTutorialAsCompleted", "tutorialId", "performSearch", "recordHelpSession", "toggleBookmark", "updateSearchQuery", "query", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class HelpViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.HelpRepository helpRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.CrashReportingManager crashReportingManager = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.viewmodel.HelpUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.viewmodel.HelpUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _searchQuery = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> searchQuery = null;
    
    @javax.inject.Inject
    public HelpViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.HelpRepository helpRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.CrashReportingManager crashReportingManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.viewmodel.HelpUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getSearchQuery() {
        return null;
    }
    
    public final void loadHelpContent() {
    }
    
    public final void updateSearchQuery(@org.jetbrains.annotations.NotNull
    java.lang.String query) {
    }
    
    public final void performSearch() {
    }
    
    public final void clearSearch() {
    }
    
    private final void clearSearchResults() {
    }
    
    public final void toggleBookmark(@org.jetbrains.annotations.NotNull
    java.lang.String articleId) {
    }
    
    public final void markArticleAsCompleted(@org.jetbrains.annotations.NotNull
    java.lang.String articleId) {
    }
    
    public final void markTutorialAsCompleted(@org.jetbrains.annotations.NotNull
    java.lang.String tutorialId) {
    }
    
    public final void dismissDailyTip() {
    }
    
    public final void recordHelpSession() {
    }
    
    public final void loadCategoryContent(@org.jetbrains.annotations.NotNull
    java.lang.String categoryId) {
    }
    
    public final void loadArticleContent(@org.jetbrains.annotations.NotNull
    java.lang.String articleId) {
    }
    
    public final void getADHDSpecificTips() {
    }
    
    public final void clearError() {
    }
}