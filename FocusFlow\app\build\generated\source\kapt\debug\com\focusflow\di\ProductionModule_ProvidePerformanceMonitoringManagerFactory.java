package com.focusflow.di;

import android.content.Context;
import com.focusflow.service.CrashReportingManager;
import com.focusflow.service.PerformanceMonitoringManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductionModule_ProvidePerformanceMonitoringManagerFactory implements Factory<PerformanceMonitoringManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CrashReportingManager> crashReportingManagerProvider;

  public ProductionModule_ProvidePerformanceMonitoringManagerFactory(
      Provider<Context> contextProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    this.contextProvider = contextProvider;
    this.crashReportingManagerProvider = crashReportingManagerProvider;
  }

  @Override
  public PerformanceMonitoringManager get() {
    return providePerformanceMonitoringManager(contextProvider.get(), crashReportingManagerProvider.get());
  }

  public static ProductionModule_ProvidePerformanceMonitoringManagerFactory create(
      Provider<Context> contextProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    return new ProductionModule_ProvidePerformanceMonitoringManagerFactory(contextProvider, crashReportingManagerProvider);
  }

  public static PerformanceMonitoringManager providePerformanceMonitoringManager(Context context,
      CrashReportingManager crashReportingManager) {
    return Preconditions.checkNotNullFromProvides(ProductionModule.INSTANCE.providePerformanceMonitoringManager(context, crashReportingManager));
  }
}
