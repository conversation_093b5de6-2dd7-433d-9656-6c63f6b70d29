package com.focusflow.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

sealed class Screen(val route: String, val title: String, val icon: ImageVector) {
    object Dashboard : Screen("dashboard", "Dashboard", Icons.Default.Home)
    object Expenses : Screen("expenses", "Expenses", Icons.Default.ShoppingCart)
    object Debt : Screen("debt", "Debt", Icons.Default.Star)
    object Budget : Screen("budget", "Budget", Icons.Default.List)
    object Habits : Screen("habits", "Habits", Icons.Default.Favorite)
    object Tasks : Screen("tasks", "Tasks", Icons.Default.CheckCircle)
    object AICoach : Screen("ai_coach", "AI Coach", Icons.Default.Person)
    object Settings : Screen("settings", "Settings", Icons.Default.Settings)
    object PayoffPlanner : Screen("payoff_planner", "Payoff Planner", Icons.Default.List)
    object FocusMode : Screen("focus_mode", "Focus Mode", Icons.Default.PlayArrow)
}

val bottomNavItems = listOf(
    Screen.Dashboard,
    Screen.Expenses,
    Screen.Debt,
    Screen.Habits,
    Screen.Tasks,
    Screen.AICoach
)

