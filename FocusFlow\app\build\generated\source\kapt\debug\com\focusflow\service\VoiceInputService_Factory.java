package com.focusflow.service;

import android.content.Context;
import com.focusflow.data.dao.VoiceCommandDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VoiceInputService_Factory implements Factory<VoiceInputService> {
  private final Provider<Context> contextProvider;

  private final Provider<VoiceCommandDao> voiceCommandDaoProvider;

  public VoiceInputService_Factory(Provider<Context> contextProvider,
      Provider<VoiceCommandDao> voiceCommandDaoProvider) {
    this.contextProvider = contextProvider;
    this.voiceCommandDaoProvider = voiceCommandDaoProvider;
  }

  @Override
  public VoiceInputService get() {
    return newInstance(contextProvider.get(), voiceCommandDaoProvider.get());
  }

  public static VoiceInputService_Factory create(Provider<Context> contextProvider,
      Provider<VoiceCommandDao> voiceCommandDaoProvider) {
    return new VoiceInputService_Factory(contextProvider, voiceCommandDaoProvider);
  }

  public static VoiceInputService newInstance(Context context, VoiceCommandDao voiceCommandDao) {
    return new VoiceInputService(context, voiceCommandDao);
  }
}
