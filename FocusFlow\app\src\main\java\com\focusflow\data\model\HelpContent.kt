package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * Data models for the FocusFlow Help & Support system
 * Designed for offline-first operation with ADHD-friendly content structure
 */

@Entity(tableName = "help_categories")
@Serializable
data class HelpCategory(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val icon: String, // Icon resource name
    val color: String, // Hex color for category theming
    val order: Int,
    val isVisible: Boolean = true,
    val adhdTips: String? = null // Special ADHD considerations for this category
)

@Entity(tableName = "help_articles")
@Serializable
data class HelpArticle(
    @PrimaryKey
    val id: String,
    val categoryId: String,
    val title: String,
    val subtitle: String? = null,
    val content: String, // Markdown content
    val contentType: HelpContentType,
    val difficulty: DifficultyLevel,
    val estimatedReadTime: Int, // Minutes
    val order: Int,
    val tags: List<String> = emptyList(),
    val isVisible: Boolean = true,
    val isFeatured: Boolean = false,
    val lastUpdated: String,
    val adhdFriendlyTips: List<String> = emptyList(),
    val troubleshootingSteps: List<TroubleshootingStep> = emptyList(),
    val relatedArticles: List<String> = emptyList(), // Article IDs
    val marketingHighlights: List<String> = emptyList() // Key points for marketing
)

@Serializable
data class TroubleshootingStep(
    val problem: String,
    val solution: String,
    val isCommon: Boolean = false,
    val adhdSpecific: Boolean = false
)

@Entity(tableName = "help_tutorials")
@Serializable
data class HelpTutorial(
    @PrimaryKey
    val id: String,
    val articleId: String,
    val title: String,
    val description: String,
    val steps: List<TutorialStep>,
    val isInteractive: Boolean = true,
    val estimatedDuration: Int, // Minutes
    val prerequisites: List<String> = emptyList(),
    val completionReward: String? = null // Achievement or badge
)

@Serializable
data class TutorialStep(
    val id: String,
    val title: String,
    val description: String,
    val instruction: String,
    val targetElement: String? = null, // UI element to highlight
    val screenshot: String? = null, // Screenshot filename
    val videoUrl: String? = null,
    val isOptional: Boolean = false,
    val adhdTip: String? = null,
    val validationCriteria: String? = null // How to verify step completion
)

@Entity(tableName = "help_faqs")
@Serializable
data class HelpFAQ(
    @PrimaryKey
    val id: String,
    val categoryId: String,
    val question: String,
    val answer: String,
    val isCommon: Boolean = false,
    val tags: List<String> = emptyList(),
    val relatedArticles: List<String> = emptyList(),
    val lastUpdated: String
)

@Entity(tableName = "help_quick_tips")
@Serializable
data class HelpQuickTip(
    @PrimaryKey
    val id: String,
    val title: String,
    val content: String,
    val category: String,
    val isADHDSpecific: Boolean = true,
    val icon: String,
    val color: String,
    val showFrequency: TipFrequency = TipFrequency.WEEKLY
)

@Entity(tableName = "help_user_progress")
@Serializable
data class HelpUserProgress(
    @PrimaryKey
    val userId: String,
    val completedArticles: List<String> = emptyList(),
    val completedTutorials: List<String> = emptyList(),
    val bookmarkedArticles: List<String> = emptyList(),
    val searchHistory: List<String> = emptyList(),
    val preferredCategories: List<String> = emptyList(),
    val helpSessionCount: Int = 0,
    val lastAccessDate: String,
    val feedbackGiven: List<String> = emptyList()
)

@Serializable
enum class HelpContentType {
    QUICK_START,
    HOW_TO_GUIDE,
    TROUBLESHOOTING,
    BEST_PRACTICES,
    ADHD_TIPS,
    FAQ,
    VIDEO_TUTORIAL,
    INTERACTIVE_DEMO,
    FEATURE_OVERVIEW
}

@Serializable
enum class DifficultyLevel {
    BEGINNER,
    INTERMEDIATE,
    ADVANCED
}

@Serializable
enum class TipFrequency {
    DAILY,
    WEEKLY,
    MONTHLY,
    ON_DEMAND
}

/**
 * Search and filtering models
 */
@Serializable
data class HelpSearchQuery(
    val query: String,
    val categories: List<String> = emptyList(),
    val contentTypes: List<HelpContentType> = emptyList(),
    val difficulty: DifficultyLevel? = null,
    val adhdSpecificOnly: Boolean = false,
    val includeCompleted: Boolean = true
)

@Serializable
data class HelpSearchResult(
    val articles: List<HelpArticle> = emptyList(),
    val faqs: List<HelpFAQ> = emptyList(),
    val tutorials: List<HelpTutorial> = emptyList(),
    val totalResults: Int,
    val searchTime: Long,
    val suggestions: List<String> = emptyList()
)

/**
 * Analytics and feedback models
 */
@Serializable
data class HelpAnalytics(
    val articleId: String,
    val viewCount: Int = 0,
    val completionRate: Float = 0f,
    val averageTimeSpent: Int = 0, // Seconds
    val helpfulVotes: Int = 0,
    val notHelpfulVotes: Int = 0,
    val commonExitPoints: List<String> = emptyList(),
    val userFeedback: List<HelpFeedback> = emptyList()
)

@Serializable
data class HelpFeedback(
    val id: String,
    val articleId: String,
    val userId: String,
    val rating: Int, // 1-5 stars
    val comment: String? = null,
    val isHelpful: Boolean,
    val improvementSuggestions: List<String> = emptyList(),
    val timestamp: String,
    val isADHDUser: Boolean = false
)

/**
 * Marketing content extraction models
 */
@Serializable
data class MarketingContent(
    val featureHighlights: List<FeatureHighlight>,
    val userBenefits: List<String>,
    val adhdSpecificBenefits: List<String>,
    val screenshots: List<MarketingScreenshot>,
    val testimonials: List<UserTestimonial> = emptyList()
)

@Serializable
data class FeatureHighlight(
    val title: String,
    val description: String,
    val benefits: List<String>,
    val adhdBenefits: List<String>,
    val screenshotPath: String? = null,
    val category: String,
    val priority: Int // For ordering in marketing materials
)

@Serializable
data class MarketingScreenshot(
    val path: String,
    val title: String,
    val description: String,
    val feature: String,
    val overlayText: String? = null,
    val isHeroImage: Boolean = false
)

@Serializable
data class UserTestimonial(
    val id: String,
    val quote: String,
    val author: String, // Anonymized
    val feature: String,
    val isADHDUser: Boolean,
    val rating: Int,
    val isVerified: Boolean = false
)

/**
 * Help system configuration
 */
@Serializable
data class HelpSystemConfig(
    val version: String,
    val lastContentUpdate: String,
    val supportedLanguages: List<String>,
    val offlineContentVersion: String,
    val adhdOptimizations: ADHDOptimizations,
    val searchConfig: SearchConfig,
    val analyticsEnabled: Boolean = true
)

@Serializable
data class ADHDOptimizations(
    val maxContentLength: Int = 500, // Words per section
    val useProgressiveDisclosure: Boolean = true,
    val includeVisualAids: Boolean = true,
    val enableBreakReminders: Boolean = true,
    val simplifyNavigation: Boolean = true,
    val usePositiveLanguage: Boolean = true,
    val provideClearNextSteps: Boolean = true
)

@Serializable
data class SearchConfig(
    val enableFuzzySearch: Boolean = true,
    val maxResults: Int = 20,
    val enableAutoComplete: Boolean = true,
    val enableSearchSuggestions: Boolean = true,
    val indexADHDTerms: Boolean = true,
    val prioritizeRecentContent: Boolean = true
)
