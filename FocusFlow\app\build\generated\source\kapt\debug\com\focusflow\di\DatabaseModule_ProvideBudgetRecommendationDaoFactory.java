package com.focusflow.di;

import com.focusflow.data.dao.BudgetRecommendationDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideBudgetRecommendationDaoFactory implements Factory<BudgetRecommendationDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvideBudgetRecommendationDaoFactory(
      Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public BudgetRecommendationDao get() {
    return provideBudgetRecommendationDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideBudgetRecommendationDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvideBudgetRecommendationDaoFactory(databaseProvider);
  }

  public static BudgetRecommendationDao provideBudgetRecommendationDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideBudgetRecommendationDao(database));
  }
}
