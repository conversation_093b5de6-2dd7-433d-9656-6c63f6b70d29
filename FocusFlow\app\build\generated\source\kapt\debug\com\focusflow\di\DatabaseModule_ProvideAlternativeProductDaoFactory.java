package com.focusflow.di;

import com.focusflow.data.dao.AlternativeProductDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideAlternativeProductDaoFactory implements Factory<AlternativeProductDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvideAlternativeProductDaoFactory(
      Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public AlternativeProductDao get() {
    return provideAlternativeProductDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideAlternativeProductDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvideAlternativeProductDaoFactory(databaseProvider);
  }

  public static AlternativeProductDao provideAlternativeProductDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideAlternativeProductDao(database));
  }
}
