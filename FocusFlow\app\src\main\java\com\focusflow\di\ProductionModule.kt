package com.focusflow.di

import android.content.Context
import com.focusflow.service.CrashReportingManager
import com.focusflow.service.PerformanceMonitoringManager
import com.focusflow.service.SecurityManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Dependency injection module for production infrastructure services
 * Provides crash reporting, performance monitoring, and security services
 */
@Module
@InstallIn(SingletonComponent::class)
object ProductionModule {

    @Provides
    @Singleton
    fun provideCrashReportingManager(
        @ApplicationContext context: Context
    ): CrashReportingManager {
        return CrashReportingManager(context)
    }

    @Provides
    @Singleton
    fun provideSecurityManager(
        @ApplicationContext context: Context,
        crashReportingManager: CrashReportingManager
    ): SecurityManager {
        return SecurityManager(context, crashReportingManager)
    }

    @Provides
    @Singleton
    fun providePerformanceMonitoringManager(
        @ApplicationContext context: Context,
        crashReportingManager: CrashReportingManager
    ): PerformanceMonitoringManager {
        return PerformanceMonitoringManager(context, crashReportingManager)
    }
}
