package com.focusflow.data.dao;

/**
 * Data Access Object for Help & Support system
 * Provides offline-first access to help content with search capabilities
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u001b\bg\u0018\u00002\u00020\u0001JJ\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0010\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0011\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0012\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u000e\u0010\u0013\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00030\u0017H\'J\u0018\u0010\u0019\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u001a\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00030\u00172\u0006\u0010\u0007\u001a\u00020\u0006H\'J\u001a\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u000b0\u001eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0018\u0010\u001f\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0007\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001e\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\u00030\u00172\b\b\u0002\u0010\n\u001a\u00020\u000bH\'J\u001a\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u000b0\u001eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u001c\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\u00030\u00172\u0006\u0010\u0007\u001a\u00020\u0006H\'J\u001e\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00030\u00172\b\b\u0002\u0010\n\u001a\u00020\u000bH\'J\u001c\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00150\u00032\u0006\u0010&\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u0010\u0010\'\u001a\u0004\u0018\u00010\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0018\u0010(\u001a\u0004\u0018\u00010)2\u0006\u0010*\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010+\u001a\b\u0012\u0004\u0012\u00020)0\u00032\u0006\u0010\u001a\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u0018\u0010,\u001a\u0004\u0018\u00010-2\u0006\u0010.\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010/\u001a\u00020\u000e2\f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u00101J\u001c\u00102\u001a\u00020\u000e2\f\u00103\u001a\b\u0012\u0004\u0012\u00020\u00180\u0003H\u00a7@\u00a2\u0006\u0002\u00101J\u001c\u00104\u001a\u00020\u000e2\f\u00105\u001a\b\u0012\u0004\u0012\u00020!0\u0003H\u00a7@\u00a2\u0006\u0002\u00101J\u001c\u00106\u001a\u00020\u000e2\f\u00107\u001a\b\u0012\u0004\u0012\u00020\u00150\u0003H\u00a7@\u00a2\u0006\u0002\u00101J\u001c\u00108\u001a\u00020\u000e2\f\u00109\u001a\b\u0012\u0004\u0012\u00020)0\u0003H\u00a7@\u00a2\u0006\u0002\u00101J\u0016\u0010:\u001a\u00020\u000e2\u0006\u0010;\u001a\u00020-H\u00a7@\u00a2\u0006\u0002\u0010<JT\u0010=\u001a\u00020\u000e2\f\u00103\u001a\b\u0012\u0004\u0012\u00020\u00180\u00032\f\u00100\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\f\u00109\u001a\b\u0012\u0004\u0012\u00020)0\u00032\f\u00105\u001a\b\u0012\u0004\u0012\u00020!0\u00032\f\u00107\u001a\b\u0012\u0004\u0012\u00020\u00150\u0003H\u0097@\u00a2\u0006\u0002\u0010>J\u001c\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010@\u001a\b\u0012\u0004\u0012\u00020!0\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ$\u0010A\u001a\u00020\u000e2\u0006\u0010.\u001a\u00020\u00062\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00a7@\u00a2\u0006\u0002\u0010CJ$\u0010D\u001a\u00020\u000e2\u0006\u0010.\u001a\u00020\u00062\f\u0010E\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00a7@\u00a2\u0006\u0002\u0010CJ$\u0010F\u001a\u00020\u000e2\u0006\u0010.\u001a\u00020\u00062\f\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00a7@\u00a2\u0006\u0002\u0010C\u00a8\u0006H"}, d2 = {"Lcom/focusflow/data/dao/HelpDao;", "", "advancedSearchArticles", "", "Lcom/focusflow/data/model/HelpArticle;", "query", "", "categoryId", "contentType", "difficulty", "limit", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearArticles", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearCategories", "clearFAQs", "clearQuickTips", "clearTutorials", "getADHDSpecificTips", "Lcom/focusflow/data/model/HelpQuickTip;", "getAllCategories", "Lkotlinx/coroutines/flow/Flow;", "Lcom/focusflow/data/model/HelpCategory;", "getArticleById", "articleId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getArticlesByCategory", "getCategoryArticleCounts", "", "getCategoryById", "getCommonFAQs", "Lcom/focusflow/data/model/HelpFAQ;", "getContentTypeDistribution", "getFAQsByCategory", "getFeaturedArticles", "getQuickTipsByCategory", "category", "getRandomTip", "getTutorialById", "Lcom/focusflow/data/model/HelpTutorial;", "tutorialId", "getTutorialsByArticle", "getUserProgress", "Lcom/focusflow/data/model/HelpUserProgress;", "userId", "insertArticles", "articles", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertCategories", "categories", "insertFAQs", "faqs", "insertQuickTips", "tips", "insertTutorials", "tutorials", "insertUserProgress", "progress", "(Lcom/focusflow/data/model/HelpUserProgress;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshAllContent", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchArticles", "searchFAQs", "updateBookmarkedArticles", "bookmarkedArticles", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCompletedArticles", "completedArticles", "updateCompletedTutorials", "completedTutorials", "app_debug"})
@androidx.room.Dao
public abstract interface HelpDao {
    
    @androidx.room.Query(value = "SELECT * FROM help_categories WHERE isVisible = 1 ORDER BY `order` ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HelpCategory>> getAllCategories();
    
    @androidx.room.Query(value = "SELECT * FROM help_categories WHERE id = :categoryId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCategoryById(@org.jetbrains.annotations.NotNull
    java.lang.String categoryId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.HelpCategory> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertCategories(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpCategory> categories, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        SELECT * FROM help_articles \n        WHERE categoryId = :categoryId AND isVisible = 1 \n        ORDER BY `order` ASC\n    ")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HelpArticle>> getArticlesByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryId);
    
    @androidx.room.Query(value = "SELECT * FROM help_articles WHERE id = :articleId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getArticleById(@org.jetbrains.annotations.NotNull
    java.lang.String articleId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.HelpArticle> $completion);
    
    @androidx.room.Query(value = "\n        SELECT * FROM help_articles \n        WHERE isFeatured = 1 AND isVisible = 1 \n        ORDER BY `order` ASC \n        LIMIT :limit\n    ")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HelpArticle>> getFeaturedArticles(int limit);
    
    @androidx.room.Query(value = "\n        SELECT * FROM help_articles \n        WHERE isVisible = 1 AND (\n            title LIKE \'%\' || :query || \'%\' OR \n            content LIKE \'%\' || :query || \'%\' OR\n            tags LIKE \'%\' || :query || \'%\'\n        )\n        ORDER BY \n            CASE WHEN title LIKE \'%\' || :query || \'%\' THEN 1 ELSE 2 END,\n            `order` ASC\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object searchArticles(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.HelpArticle>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertArticles(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> articles, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM help_tutorials WHERE articleId = :articleId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTutorialsByArticle(@org.jetbrains.annotations.NotNull
    java.lang.String articleId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.HelpTutorial>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM help_tutorials WHERE id = :tutorialId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTutorialById(@org.jetbrains.annotations.NotNull
    java.lang.String tutorialId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.HelpTutorial> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertTutorials(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpTutorial> tutorials, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        SELECT * FROM help_faqs \n        WHERE categoryId = :categoryId \n        ORDER BY isCommon DESC, question ASC\n    ")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HelpFAQ>> getFAQsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryId);
    
    @androidx.room.Query(value = "\n        SELECT * FROM help_faqs \n        WHERE isCommon = 1 \n        ORDER BY question ASC \n        LIMIT :limit\n    ")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HelpFAQ>> getCommonFAQs(int limit);
    
    @androidx.room.Query(value = "\n        SELECT * FROM help_faqs \n        WHERE question LIKE \'%\' || :query || \'%\' OR \n              answer LIKE \'%\' || :query || \'%\' OR\n              tags LIKE \'%\' || :query || \'%\'\n        ORDER BY isCommon DESC\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object searchFAQs(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.HelpFAQ>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertFAQs(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFAQ> faqs, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM help_quick_tips WHERE category = :category")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getQuickTipsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.HelpQuickTip>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM help_quick_tips WHERE isADHDSpecific = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getADHDSpecificTips(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.HelpQuickTip>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM help_quick_tips ORDER BY RANDOM() LIMIT 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getRandomTip(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.HelpQuickTip> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertQuickTips(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpQuickTip> tips, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM help_user_progress WHERE userId = :userId")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserProgress(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.HelpUserProgress> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertUserProgress(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpUserProgress progress, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE help_user_progress \n        SET completedArticles = :completedArticles \n        WHERE userId = :userId\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCompletedArticles(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> completedArticles, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE help_user_progress \n        SET completedTutorials = :completedTutorials \n        WHERE userId = :userId\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCompletedTutorials(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> completedTutorials, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        UPDATE help_user_progress \n        SET bookmarkedArticles = :bookmarkedArticles \n        WHERE userId = :userId\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateBookmarkedArticles(@org.jetbrains.annotations.NotNull
    java.lang.String userId, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> bookmarkedArticles, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "\n        SELECT * FROM help_articles \n        WHERE isVisible = 1 \n        AND (:categoryId IS NULL OR categoryId = :categoryId)\n        AND (:contentType IS NULL OR contentType = :contentType)\n        AND (:difficulty IS NULL OR difficulty = :difficulty)\n        AND (\n            title LIKE \'%\' || :query || \'%\' OR \n            content LIKE \'%\' || :query || \'%\' OR\n            tags LIKE \'%\' || :query || \'%\' OR\n            adhdFriendlyTips LIKE \'%\' || :query || \'%\'\n        )\n        ORDER BY \n            CASE WHEN title LIKE \'%\' || :query || \'%\' THEN 1 ELSE 2 END,\n            isFeatured DESC,\n            `order` ASC\n        LIMIT :limit\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object advancedSearchArticles(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.Nullable
    java.lang.String categoryId, @org.jetbrains.annotations.Nullable
    java.lang.String contentType, @org.jetbrains.annotations.Nullable
    java.lang.String difficulty, int limit, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.HelpArticle>> $completion);
    
    @androidx.room.Query(value = "\n        SELECT categoryId, COUNT(*) as articleCount \n        FROM help_articles \n        WHERE isVisible = 1 \n        GROUP BY categoryId\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCategoryArticleCounts(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, java.lang.Integer>> $completion);
    
    @androidx.room.Query(value = "\n        SELECT contentType, COUNT(*) as count \n        FROM help_articles \n        WHERE isVisible = 1 \n        GROUP BY contentType\n    ")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getContentTypeDistribution(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.Map<java.lang.String, java.lang.Integer>> $completion);
    
    @androidx.room.Query(value = "DELETE FROM help_categories")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearCategories(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM help_articles")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearArticles(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM help_tutorials")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearTutorials(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM help_faqs")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearFAQs(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM help_quick_tips")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object clearQuickTips(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Transaction
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object refreshAllContent(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpCategory> categories, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> articles, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpTutorial> tutorials, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFAQ> faqs, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpQuickTip> tips, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Data Access Object for Help & Support system
     * Provides offline-first access to help content with search capabilities
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
        
        @androidx.room.Transaction
        @org.jetbrains.annotations.Nullable
        public static java.lang.Object refreshAllContent(@org.jetbrains.annotations.NotNull
        com.focusflow.data.dao.HelpDao $this, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.data.model.HelpCategory> categories, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.data.model.HelpArticle> articles, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.data.model.HelpTutorial> tutorials, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.data.model.HelpFAQ> faqs, @org.jetbrains.annotations.NotNull
        java.util.List<com.focusflow.data.model.HelpQuickTip> tips, @org.jetbrains.annotations.NotNull
        kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
            return null;
        }
    }
}