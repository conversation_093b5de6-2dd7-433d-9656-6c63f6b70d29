# FocusFlow App Icon Specifications

## Design Concept

The FocusFlow app icon should embody the core principles of ADHD-friendly design while representing personal finance management. The icon needs to be instantly recognizable, calming yet energizing, and convey both focus and financial wellness.

## Core Design Elements

### Primary Symbol: Brain + Flow
- **Brain silhouette** representing ADHD awareness and neurodiversity
- **Flow lines or waves** representing the smooth flow of financial management
- **Circular or rounded elements** to create a sense of completeness and calm

### Color Palette
- **Primary**: Deep Purple (#6200EA) - represents focus, wisdom, and premium quality
- **Secondary**: Teal/Turquoise (#00BCD4) - represents calm, clarity, and financial growth
- **Accent**: Warm Orange (#FF9800) - represents energy, motivation, and positive action
- **Background**: Clean White or subtle gradient

### Typography (if text is included)
- Clean, modern sans-serif
- High contrast for readability
- Avoid overly complex or decorative fonts

## Required Icon Sizes

### Android App Icons
- **512x512px** - Google Play Store listing (PNG, 32-bit)
- **192x192px** - xxxhdpi launcher icon
- **144x144px** - xxhdpi launcher icon
- **96x96px** - xhdpi launcher icon
- **72x72px** - hdpi launcher icon
- **48x48px** - mdpi launcher icon

### Adaptive Icons (Android 8.0+)
- **Foreground**: 108x108dp safe area (72x72dp visible)
- **Background**: 108x108dp solid color or simple pattern
- **Format**: Vector drawable (XML) preferred

### Additional Formats
- **Round Icon**: 512x512px for devices that use round icons
- **Monochrome**: Black and white version for themed icons (Android 13+)

## Design Guidelines

### ADHD-Friendly Considerations
1. **High Contrast**: Ensure the icon is clearly visible against various backgrounds
2. **Simple Shapes**: Avoid overly complex details that might be overwhelming
3. **Calming Colors**: Use colors that promote focus rather than anxiety
4. **Clear Hierarchy**: Main element should be immediately recognizable

### Technical Requirements
1. **Scalability**: Icon must be legible at 48x48px and impressive at 512x512px
2. **No Text**: Avoid text in the icon as it becomes unreadable at small sizes
3. **Consistent Style**: Maintain visual consistency across all sizes
4. **Platform Guidelines**: Follow Material Design icon guidelines

## Icon Concepts

### Concept 1: Brain Flow
- Stylized brain silhouette with flowing lines
- Purple brain with teal flow lines
- Circular background with subtle gradient

### Concept 2: Focus Target
- Circular target/bullseye representing focus
- Dollar sign or financial symbol in center
- Purple and teal color scheme

### Concept 3: Balanced Scales
- Modern interpretation of balance scales
- Brain icon on one side, money/growth symbol on other
- Represents balance between ADHD management and financial health

### Concept 4: Flowing River
- Abstract river or stream representing flow
- Incorporates brain-like curves
- Gradient from purple to teal

## File Naming Convention

```
focusflow_icon_[size]_[density].png
focusflow_icon_512_store.png
focusflow_icon_192_xxxhdpi.png
focusflow_icon_144_xxhdpi.png
focusflow_icon_96_xhdpi.png
focusflow_icon_72_hdpi.png
focusflow_icon_48_mdpi.png

focusflow_adaptive_foreground.xml
focusflow_adaptive_background.xml
focusflow_round_512.png
focusflow_monochrome_512.png
```

## Quality Checklist

### Visual Quality
- [ ] Icon is crisp and clear at all required sizes
- [ ] Colors are vibrant but not overwhelming
- [ ] Design is unique and memorable
- [ ] Represents the app's purpose clearly

### Technical Quality
- [ ] All required sizes generated
- [ ] PNG files are optimized for size
- [ ] Adaptive icon components created
- [ ] Monochrome version available
- [ ] Files properly named and organized

### ADHD Accessibility
- [ ] High contrast against common backgrounds
- [ ] Simple, uncluttered design
- [ ] Calming color palette
- [ ] Instantly recognizable shape

### Brand Consistency
- [ ] Aligns with app's visual identity
- [ ] Consistent with marketing materials
- [ ] Reflects ADHD-friendly design principles
- [ ] Professional and trustworthy appearance

## Implementation Notes

### Android Resources
Place icons in appropriate drawable folders:
```
app/src/main/res/
├── drawable-mdpi/ic_launcher.png (48x48)
├── drawable-hdpi/ic_launcher.png (72x72)
├── drawable-xhdpi/ic_launcher.png (96x96)
├── drawable-xxhdpi/ic_launcher.png (144x144)
├── drawable-xxxhdpi/ic_launcher.png (192x192)
├── drawable/ic_launcher_background.xml
├── drawable/ic_launcher_foreground.xml
└── mipmap-anydpi-v26/ic_launcher.xml
```

### Adaptive Icon XML
```xml
<?xml version="1.0" encoding="utf-8"?>
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
    <background android:drawable="@drawable/ic_launcher_background" />
    <foreground android:drawable="@drawable/ic_launcher_foreground" />
    <monochrome android:drawable="@drawable/ic_launcher_monochrome" />
</adaptive-icon>
```

## Testing

### Visual Testing
- Test icon visibility on light and dark backgrounds
- Verify readability at smallest size (48x48px)
- Check appearance on various Android launchers
- Test with different system themes

### User Testing
- Show icon to ADHD individuals for feedback
- Verify it conveys the app's purpose
- Ensure it feels welcoming and non-intimidating
- Confirm it stands out among other finance apps

## Approval Process

1. **Design Review**: Internal team reviews concepts
2. **ADHD Community Feedback**: Get input from target users
3. **Technical Validation**: Ensure all technical requirements met
4. **Final Approval**: Stakeholder sign-off
5. **Implementation**: Generate all required assets
6. **Testing**: Verify appearance across devices and launchers

## Delivery

### Final Deliverables
- All PNG files in required sizes
- Adaptive icon XML files
- Monochrome version
- Source files (AI/PSD/Sketch)
- Usage guidelines document

### Timeline
- Concept development: 3-5 days
- Feedback and revisions: 2-3 days
- Final asset generation: 1-2 days
- Testing and validation: 1-2 days

**Total estimated time: 7-12 days**
