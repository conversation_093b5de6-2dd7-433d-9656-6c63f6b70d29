<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="ScheduleExactAlarm">
        <location id="0"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/NotificationManager.kt"
            line="155"
            column="17"
            startOffset="6079"
            endLine="159"
            endColumn="18"
            endOffset="6249"/>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.focusflow.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.adhd_calm_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="62"
            column="12"
            startOffset="2633"
            endLine="62"
            endColumn="33"
            endOffset="2654"/>
        <location id="R.color.adhd_error_red"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="65"
            column="12"
            startOffset="2795"
            endLine="65"
            endColumn="33"
            endOffset="2816"/>
        <location id="R.color.adhd_focus_green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="63"
            column="12"
            startOffset="2685"
            endLine="63"
            endColumn="35"
            endOffset="2708"/>
        <location id="R.color.adhd_success_green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="66"
            column="12"
            startOffset="2847"
            endLine="66"
            endColumn="37"
            endOffset="2872"/>
        <location id="R.color.adhd_warning_amber"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="64"
            column="12"
            startOffset="2739"
            endLine="64"
            endColumn="37"
            endOffset="2764"/>
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="19"
            column="12"
            startOffset="710"
            endLine="19"
            endColumn="24"
            endOffset="722"/>
        <location id="R.color.dark_background_soft"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="42"
            column="12"
            startOffset="1693"
            endLine="42"
            endColumn="39"
            endOffset="1720"/>
        <location id="R.color.dark_on_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="44"
            column="12"
            startOffset="1801"
            endLine="44"
            endColumn="34"
            endOffset="1823"/>
        <location id="R.color.dark_text_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="45"
            column="12"
            startOffset="1854"
            endLine="45"
            endColumn="36"
            endOffset="1878"/>
        <location id="R.color.dark_text_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="46"
            column="12"
            startOffset="1909"
            endLine="46"
            endColumn="38"
            endOffset="1935"/>
        <location id="R.color.focus_orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="525"
            endLine="13"
            endColumn="31"
            endOffset="544"/>
        <location id="R.color.gray_100"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="841"
            endLine="22"
            endColumn="27"
            endOffset="856"/>
        <location id="R.color.gray_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="887"
            endLine="23"
            endColumn="27"
            endOffset="902"/>
        <location id="R.color.gray_300"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="933"
            endLine="24"
            endColumn="27"
            endOffset="948"/>
        <location id="R.color.gray_400"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="979"
            endLine="25"
            endColumn="27"
            endOffset="994"/>
        <location id="R.color.gray_50"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="796"
            endLine="21"
            endColumn="26"
            endOffset="810"/>
        <location id="R.color.gray_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="1025"
            endLine="26"
            endColumn="27"
            endOffset="1040"/>
        <location id="R.color.gray_600"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="27"
            column="12"
            startOffset="1071"
            endLine="27"
            endColumn="27"
            endOffset="1086"/>
        <location id="R.color.gray_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="1117"
            endLine="28"
            endColumn="27"
            endOffset="1132"/>
        <location id="R.color.gray_800"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1163"
            endLine="29"
            endColumn="27"
            endOffset="1178"/>
        <location id="R.color.gray_900"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="30"
            column="12"
            startOffset="1209"
            endLine="30"
            endColumn="27"
            endOffset="1224"/>
        <location id="R.color.high_contrast_accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="50"
            column="12"
            startOffset="2062"
            endLine="50"
            endColumn="39"
            endOffset="2089"/>
        <location id="R.color.high_contrast_dark_accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="56"
            column="12"
            startOffset="2341"
            endLine="56"
            endColumn="44"
            endOffset="2373"/>
        <location id="R.color.high_contrast_dark_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="59"
            column="12"
            startOffset="2529"
            endLine="59"
            endColumn="48"
            endOffset="2565"/>
        <location id="R.color.high_contrast_dark_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="55"
            column="12"
            startOffset="2277"
            endLine="55"
            endColumn="45"
            endOffset="2310"/>
        <location id="R.color.high_contrast_dark_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="58"
            column="12"
            startOffset="2465"
            endLine="58"
            endColumn="45"
            endOffset="2498"/>
        <location id="R.color.high_contrast_dark_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="57"
            column="12"
            startOffset="2404"
            endLine="57"
            endColumn="42"
            endOffset="2434"/>
        <location id="R.color.high_contrast_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="49"
            column="12"
            startOffset="2003"
            endLine="49"
            endColumn="40"
            endOffset="2031"/>
        <location id="R.color.high_contrast_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="52"
            column="12"
            startOffset="2176"
            endLine="52"
            endColumn="40"
            endOffset="2204"/>
        <location id="R.color.high_contrast_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="51"
            column="12"
            startOffset="2120"
            endLine="51"
            endColumn="37"
            endOffset="2145"/>
        <location id="R.color.on_surface_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="38"
            column="12"
            startOffset="1552"
            endLine="38"
            endColumn="35"
            endOffset="1575"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="99"
            endLine="4"
            endColumn="29"
            endOffset="116"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="147"
            endLine="5"
            endColumn="29"
            endOffset="164"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="195"
            endLine="6"
            endColumn="29"
            endOffset="212"/>
        <location id="R.color.surface_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="37"
            column="12"
            startOffset="1501"
            endLine="37"
            endColumn="32"
            endOffset="1521"/>
        <location id="R.color.teal_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="243"
            endLine="7"
            endColumn="27"
            endOffset="258"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="289"
            endLine="8"
            endColumn="27"
            endOffset="304"/>
        <location id="R.color.text_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="35"
            column="12"
            startOffset="1399"
            endLine="35"
            endColumn="31"
            endOffset="1418"/>
        <location id="R.color.text_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="36"
            column="12"
            startOffset="1449"
            endLine="36"
            endColumn="33"
            endOffset="1470"/>
        <location id="R.dimen.button_corner_radius"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="37"
            column="12"
            startOffset="1494"
            endLine="37"
            endColumn="39"
            endOffset="1521"/>
        <location id="R.dimen.card_corner_radius"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="35"
            column="12"
            startOffset="1399"
            endLine="35"
            endColumn="37"
            endOffset="1424"/>
        <location id="R.dimen.card_elevation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="36"
            column="12"
            startOffset="1449"
            endLine="36"
            endColumn="33"
            endOffset="1470"/>
        <location id="R.dimen.focus_border_width"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="40"
            column="12"
            startOffset="1580"
            endLine="40"
            endColumn="37"
            endOffset="1605"/>
        <location id="R.dimen.focus_corner_radius"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="41"
            column="12"
            startOffset="1629"
            endLine="41"
            endColumn="38"
            endOffset="1655"/>
        <location id="R.dimen.spacing_lg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="26"
            column="12"
            startOffset="1058"
            endLine="26"
            endColumn="29"
            endOffset="1075"/>
        <location id="R.dimen.spacing_md"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="25"
            column="12"
            startOffset="1016"
            endLine="25"
            endColumn="29"
            endOffset="1033"/>
        <location id="R.dimen.spacing_sm"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="24"
            column="12"
            startOffset="975"
            endLine="24"
            endColumn="29"
            endOffset="992"/>
        <location id="R.dimen.spacing_xl"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="27"
            column="12"
            startOffset="1100"
            endLine="27"
            endColumn="29"
            endOffset="1117"/>
        <location id="R.dimen.spacing_xs"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="23"
            column="12"
            startOffset="934"
            endLine="23"
            endColumn="29"
            endOffset="951"/>
        <location id="R.dimen.text_size_body"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="6"
            column="12"
            startOffset="197"
            endLine="6"
            endColumn="33"
            endOffset="218"/>
        <location id="R.dimen.text_size_body_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="13"
            column="12"
            startOffset="501"
            endLine="13"
            endColumn="39"
            endOffset="528"/>
        <location id="R.dimen.text_size_body_xl"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="19"
            column="12"
            startOffset="781"
            endLine="19"
            endColumn="36"
            endOffset="805"/>
        <location id="R.dimen.text_size_caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="7"
            column="12"
            startOffset="243"
            endLine="7"
            endColumn="36"
            endOffset="267"/>
        <location id="R.dimen.text_size_caption_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="14"
            column="12"
            startOffset="553"
            endLine="14"
            endColumn="42"
            endOffset="583"/>
        <location id="R.dimen.text_size_caption_xl"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="20"
            column="12"
            startOffset="830"
            endLine="20"
            endColumn="39"
            endOffset="857"/>
        <location id="R.dimen.text_size_headline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="100"
            endLine="4"
            endColumn="37"
            endOffset="125"/>
        <location id="R.dimen.text_size_headline_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="11"
            column="12"
            startOffset="392"
            endLine="11"
            endColumn="43"
            endOffset="423"/>
        <location id="R.dimen.text_size_headline_xl"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="17"
            column="12"
            startOffset="678"
            endLine="17"
            endColumn="40"
            endOffset="706"/>
        <location id="R.dimen.text_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="8"
            column="12"
            startOffset="292"
            endLine="8"
            endColumn="34"
            endOffset="314"/>
        <location id="R.dimen.text_size_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="5"
            column="12"
            startOffset="150"
            endLine="5"
            endColumn="34"
            endOffset="172"/>
        <location id="R.dimen.text_size_title_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="12"
            column="12"
            startOffset="448"
            endLine="12"
            endColumn="40"
            endOffset="476"/>
        <location id="R.dimen.text_size_title_xl"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="18"
            column="12"
            startOffset="731"
            endLine="18"
            endColumn="37"
            endOffset="756"/>
        <location id="R.dimen.touch_target_comfortable"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="31"
            column="12"
            startOffset="1245"
            endLine="31"
            endColumn="43"
            endOffset="1276"/>
        <location id="R.dimen.touch_target_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="32"
            column="12"
            startOffset="1301"
            endLine="32"
            endColumn="37"
            endOffset="1326"/>
        <location id="R.dimen.touch_target_min"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="30"
            column="12"
            startOffset="1197"
            endLine="30"
            endColumn="35"
            endOffset="1220"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="19"
            endColumn="10"
            endOffset="849"/>
        <location id="R.string.default_notification_message"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="561"
            endLine="9"
            endColumn="48"
            endOffset="596"/>
        <location id="R.string.default_notification_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="487"
            endLine="8"
            endColumn="46"
            endOffset="520"/>
        <location id="R.string.get_started"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="242"
            endLine="5"
            endColumn="31"
            endOffset="260"/>
        <location id="R.string.notification_channel_description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="372"
            endLine="7"
            endColumn="52"
            endOffset="411"/>
        <location id="R.string.notification_channel_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="294"
            endLine="6"
            endColumn="45"
            endOffset="326"/>
        <location id="R.string.welcome_subtitle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="134"
            endLine="4"
            endColumn="36"
            endOffset="157"/>
        <location id="R.string.welcome_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="13"
            startOffset="71"
            endLine="3"
            endColumn="33"
            endOffset="91"/>
        <entry
            name="model"
            string="color[focus_blue(U),white(U),ic_launcher_background(U),purple_200(D),purple_500(D),purple_700(D),teal_200(D),teal_700(D),focus_blue_dark(U),focus_green(U),focus_orange(D),black(D),gray_50(D),gray_100(D),gray_200(D),gray_300(D),gray_400(D),gray_500(D),gray_600(D),gray_700(D),gray_800(D),gray_900(D),focus_blue_light(U),focus_green_light(U),text_primary(D),text_secondary(D),surface_light(D),on_surface_light(D),dark_background(U),dark_background_soft(D),dark_surface(U),dark_on_surface(D),dark_text_primary(D),dark_text_secondary(D),high_contrast_primary(D),high_contrast_accent(D),high_contrast_text(D),high_contrast_surface(D),high_contrast_dark_primary(D),high_contrast_dark_accent(D),high_contrast_dark_text(D),high_contrast_dark_surface(D),high_contrast_dark_background(D),adhd_calm_blue(D),adhd_focus_green(D),adhd_warning_amber(D),adhd_error_red(D),adhd_success_green(D)],dimen[text_size_headline(D),text_size_title(D),text_size_body(D),text_size_caption(D),text_size_small(D),text_size_headline_large(D),text_size_title_large(D),text_size_body_large(D),text_size_caption_large(D),text_size_headline_xl(D),text_size_title_xl(D),text_size_body_xl(D),text_size_caption_xl(D),spacing_xs(D),spacing_sm(D),spacing_md(D),spacing_lg(D),spacing_xl(D),touch_target_min(D),touch_target_comfortable(D),touch_target_large(D),card_corner_radius(D),card_elevation(D),button_corner_radius(D),focus_border_width(D),focus_corner_radius(D)],drawable[ic_launcher_foreground(D),ic_notification(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),welcome_title(D),welcome_subtitle(D),get_started(D),notification_channel_name(D),notification_channel_description(D),default_notification_title(D),default_notification_message(D)],style[Theme_FocusFlow(U),Theme_FocusFlow_NoActionBar(U),Theme_AppCompat_DayNight(R)],xml[data_extraction_rules(U),backup_rules(U),file_paths(U),network_security_config(U)];4a^0^1,4c^2,4d^2,56^58^0^8^9^1^16^17^1e^1c,57^56;;;"/>
    </map>

</incidents>
