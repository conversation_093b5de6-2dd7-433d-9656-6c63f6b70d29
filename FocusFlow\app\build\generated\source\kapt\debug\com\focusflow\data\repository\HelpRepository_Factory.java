package com.focusflow.data.repository;

import com.focusflow.data.dao.HelpDao;
import com.focusflow.service.CrashReportingManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HelpRepository_Factory implements Factory<HelpRepository> {
  private final Provider<HelpDao> helpDaoProvider;

  private final Provider<CrashReportingManager> crashReportingManagerProvider;

  public HelpRepository_Factory(Provider<HelpDao> helpDaoProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    this.helpDaoProvider = helpDaoProvider;
    this.crashReportingManagerProvider = crashReportingManagerProvider;
  }

  @Override
  public HelpRepository get() {
    return newInstance(helpDaoProvider.get(), crashReportingManagerProvider.get());
  }

  public static HelpRepository_Factory create(Provider<HelpDao> helpDaoProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    return new HelpRepository_Factory(helpDaoProvider, crashReportingManagerProvider);
  }

  public static HelpRepository newInstance(HelpDao helpDao,
      CrashReportingManager crashReportingManager) {
    return new HelpRepository(helpDao, crashReportingManager);
  }
}
