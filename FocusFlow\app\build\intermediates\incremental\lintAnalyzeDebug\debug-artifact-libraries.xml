<libraries>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.20\2171532b615f8c10a288e9ef0ed34f37b5304d1d\kotlin-parcelize-runtime-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.20"/>
  <library
      name="androidx.hilt:hilt-navigation-compose:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\780bf32e32b8da2855eb049e7f5282bd\transformed\jetified-hilt-navigation-compose-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation-compose:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\780bf32e32b8da2855eb049e7f5282bd\transformed\jetified-hilt-navigation-compose-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-navigation:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f1f948b1a1d0a34c09ddb2c7cd11b43c\transformed\jetified-hilt-navigation-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-navigation:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f1f948b1a1d0a34c09ddb2c7cd11b43c\transformed\jetified-hilt-navigation-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\675a96fbdef42db63b2797b3355c3b1a\transformed\navigation-common-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\675a96fbdef42db63b2797b3355c3b1a\transformed\navigation-common-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4c606334f6f174b1055377e9b721ad21\transformed\navigation-runtime-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4c606334f6f174b1055377e9b721ad21\transformed\navigation-runtime-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\48c6ac263eb7cd40ac405a11eed98f1e\transformed\navigation-common-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\48c6ac263eb7cd40ac405a11eed98f1e\transformed\navigation-common-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ccba22d02dc258c6c0f8d1611562d950\transformed\navigation-runtime-ktx-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ccba22d02dc258c6c0f8d1611562d950\transformed\navigation-runtime-ktx-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.7.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b69f53eeb29de50b9b177ef377ed74de\transformed\jetified-navigation-compose-2.7.5\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.7.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b69f53eeb29de50b9b177ef377ed74de\transformed\jetified-navigation-compose-2.7.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd99fe8d474b529eb55ef83da4ba319\transformed\biometric-1.1.0\jars\classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4fd99fe8d474b529eb55ef83da4ba319\transformed\biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.hilt:hilt-work:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d0a0ebcba49bd6d238bab815dfbd971e\transformed\jetified-hilt-work-1.1.0\jars\classes.jar"
      resolved="androidx.hilt:hilt-work:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d0a0ebcba49bd6d238bab815dfbd971e\transformed\jetified-hilt-work-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-android:2.48@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c6bec5b30be13d032446777283aafa0b\transformed\jetified-hilt-android-2.48\jars\classes.jar"
      resolved="com.google.dagger:hilt-android:2.48"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c6bec5b30be13d032446777283aafa0b\transformed\jetified-hilt-android-2.48"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\73d759f28d3aa098b1b9feba5e1a3eb1\transformed\fragment-1.5.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\73d759f28d3aa098b1b9feba5e1a3eb1\transformed\fragment-1.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2a621a68b84314f2ffab306b0a1c7b79\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2a621a68b84314f2ffab306b0a1c7b79\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d45560939f2c66bbd8bde9562d822281\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d45560939f2c66bbd8bde9562d822281\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3b4a81db9a5fdd9cb9019d4f0b7fc646\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3b4a81db9a5fdd9cb9019d4f0b7fc646\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc6d0d970e3c168245311f4f8e60786\transformed\core-1.12.0\jars\classes.jar"
      resolved="androidx.core:core:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3fc6d0d970e3c168245311f4f8e60786\transformed\core-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ba56b74c719e0e31a0dc7b4e6b0607fb\transformed\work-runtime-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ba56b74c719e0e31a0dc7b4e6b0607fb\transformed\work-runtime-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\fae81b7f7d2930fd4a9367f6b49ae72a\transformed\work-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\fae81b7f7d2930fd4a9367f6b49ae72a\transformed\work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b718f38b2f609913a6e453ac75a3a31f\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b718f38b2f609913a6e453ac75a3a31f\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\9cbbb2949e26bc96d8e23202f621f402\transformed\lifecycle-livedata-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\9cbbb2949e26bc96d8e23202f621f402\transformed\lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8985558dc73a605e71692a337d7168a9\transformed\lifecycle-livedata-core-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8985558dc73a605e71692a337d7168a9\transformed\lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.7.0\85334205d65cca70ed0109c3acbd29e22a2d9cb1\lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a20e3490cc47945359a86ab8d35805\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a20e3490cc47945359a86ab8d35805\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\59ad29456446c44060031b3eabffcdd4\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\59ad29456446c44060031b3eabffcdd4\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-java8\2.7.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cf7842f1497f9b21e4b6d3fdfe487f03\transformed\lifecycle-viewmodel-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cf7842f1497f9b21e4b6d3fdfe487f03\transformed\lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8de0e1062be1cd5a75e3caf2d0e2db2d\transformed\jetified-coil-compose-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8de0e1062be1cd5a75e3caf2d0e2db2d\transformed\jetified-coil-compose-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3432c534953ba780cae6f8310556f9df\transformed\jetified-coil-compose-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3432c534953ba780cae6f8310556f9df\transformed\jetified-coil-compose-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d84594546a519dfcff27f038d67013c\transformed\jetified-coil-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0d84594546a519dfcff27f038d67013c\transformed\jetified-coil-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\292c4259f358ccf66c799d67cdfb0ee4\transformed\jetified-coil-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-base:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\292c4259f358ccf66c799d67cdfb0ee4\transformed\jetified-coil-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\01798c09b07088d478275bfa0777bb9b\transformed\lifecycle-runtime-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\01798c09b07088d478275bfa0777bb9b\transformed\lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a357e019bf83bb26c29f62da7a1721dd\transformed\jetified-lifecycle-runtime-compose-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a357e019bf83bb26c29f62da7a1721dd\transformed\jetified-lifecycle-runtime-compose-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\140f20ce8c9810c5f23437d658405ab6\transformed\jetified-lifecycle-runtime-ktx-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\140f20ce8c9810c5f23437d658405ab6\transformed\jetified-lifecycle-runtime-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\59e0fc3b88546f79ae687858bf416bf7\transformed\jetified-lifecycle-viewmodel-compose-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\59e0fc3b88546f79ae687858bf416bf7\transformed\jetified-lifecycle-viewmodel-compose-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a478ab24747af1591b29f27f92bec421\transformed\jetified-material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a478ab24747af1591b29f27f92bec421\transformed\jetified-material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\734713fe50ebb053c62db071dc0ebcec\transformed\jetified-material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\734713fe50ebb053c62db071dc0ebcec\transformed\jetified-material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f90035d7b1d8996af465614d74ea1a\transformed\jetified-material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e3f90035d7b1d8996af465614d74ea1a\transformed\jetified-material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\70b4c3fccd591969382b340dac7decea\transformed\jetified-material-release\jars\classes.jar"
      resolved="androidx.compose.material:material-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\70b4c3fccd591969382b340dac7decea\transformed\jetified-material-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\edd5e3eddd77a51299f06e4312a2edb2\transformed\jetified-animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\edd5e3eddd77a51299f06e4312a2edb2\transformed\jetified-animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\de5cd214e2e72229407581aa08eb5fd0\transformed\jetified-foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\de5cd214e2e72229407581aa08eb5fd0\transformed\jetified-foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\76430c7192c9c339633d95c2ac8b69a0\transformed\jetified-foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\76430c7192c9c339633d95c2ac8b69a0\transformed\jetified-foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\47e638ede125070f0ea9e60895b3fd3f\transformed\jetified-animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\47e638ede125070f0ea9e60895b3fd3f\transformed\jetified-animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ffedd7ff46ecd35ed809e74595e7da40\transformed\jetified-ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ffedd7ff46ecd35ed809e74595e7da40\transformed\jetified-ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\9c26e4e06c0726149ff5b02aba7a677e\transformed\jetified-ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\9c26e4e06c0726149ff5b02aba7a677e\transformed\jetified-ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8b4e9726300c074c895b57ebf6b60171\transformed\jetified-ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8b4e9726300c074c895b57ebf6b60171\transformed\jetified-ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e0569bdcd496785db137f57b8b73b12e\transformed\jetified-ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e0569bdcd496785db137f57b8b73b12e\transformed\jetified-ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\698205b51e44629bc58f3b811664e931\transformed\jetified-ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\698205b51e44629bc58f3b811664e931\transformed\jetified-ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3052465d3c5e657b43c1c8b75b9cf1b7\transformed\jetified-ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3052465d3c5e657b43c1c8b75b9cf1b7\transformed\jetified-ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c220a8d904d175691eb4177fafcca3d5\transformed\jetified-ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c220a8d904d175691eb4177fafcca3d5\transformed\jetified-ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ae20ac95cb786f6216311627c814218a\transformed\jetified-ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ae20ac95cb786f6216311627c814218a\transformed\jetified-ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\07d809736e52631be78e4ec74270d0bd\transformed\jetified-ui-test-manifest-1.5.4\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\07d809736e52631be78e4ec74270d0bd\transformed\jetified-ui-test-manifest-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b995b2484cdeb1078aced23e6165e39c\transformed\jetified-activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b995b2484cdeb1078aced23e6165e39c\transformed\jetified-activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\593a2fd83f6bce5cbd9ed8c845c08454\transformed\jetified-activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\593a2fd83f6bce5cbd9ed8c845c08454\transformed\jetified-activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f940c3b44f4c8ee4384dac0432d861a1\transformed\jetified-activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f940c3b44f4c8ee4384dac0432d861a1\transformed\jetified-activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\514c7ddea5e471d68e5b6c3fe30b81e2\transformed\jetified-core-ktx-1.12.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\514c7ddea5e471d68e5b6c3fe30b81e2\transformed\jetified-core-ktx-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca5e963c591bc1388f745fb9c4fc9f8\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6ca5e963c591bc1388f745fb9c4fc9f8\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\79809c23b609fb6292cc3d5883d45d39\transformed\jetified-room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\79809c23b609fb6292cc3d5883d45d39\transformed\jetified-room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.20\a77ec9b1d5d4819d5194fabb98c92787142a93db\kotlin-android-extensions-runtime-1.9.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.20"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\23e1fae7a2bb6f57e2755a4dce0bb18f\transformed\jetified-annotation-experimental-1.3.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\23e1fae7a2bb6f57e2755a4dce0bb18f\transformed\jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b625fb61a69d3b2dd989a6b5d80a1932\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b625fb61a69d3b2dd989a6b5d80a1932\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0fbe7faa7a4643e30220b72cfe6daf2c\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0fbe7faa7a4643e30220b72cfe6daf2c\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-datetime-jvm\0.5.0\8882b30187d18d2dcb5e22587447485e6f42dfb3\kotlinx-datetime-jvm-0.5.0.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-datetime-jvm:0.5.0"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5326cd6544c35494861b8777db6f0df6\transformed\jetified-runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5326cd6544c35494861b8777db6f0df6\transformed\jetified-runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f4d0d9d5ce2b13307bc6aaede26e4b3d\transformed\jetified-runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f4d0d9d5ce2b13307bc6aaede26e4b3d\transformed\jetified-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.google.accompanist:accompanist-permissions:0.32.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\95eec2b79e81532093b0b0156d91132c\transformed\jetified-accompanist-permissions-0.32.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-permissions:0.32.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\95eec2b79e81532093b0b0156d91132c\transformed\jetified-accompanist-permissions-0.32.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.github.vanpra.compose-material-dialogs:datetime:0.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f89bef0a086c25fcdbf63a0be151ff0a\transformed\jetified-datetime-0.9.0\jars\classes.jar"
      resolved="io.github.vanpra.compose-material-dialogs:datetime:0.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f89bef0a086c25fcdbf63a0be151ff0a\transformed\jetified-datetime-0.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.9.0\fc93484fc67ab52b1e0ccbdaa3922d8a6678e097\converter-gson-2.9.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.9.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.9.0\d8fdfbd5da952141a665a403348b74538efc05ff\retrofit-2.9.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.9.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\506460e1fd7e8de103a8225295d8d77d\transformed\jetified-security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\506460e1fd7e8de103a8225295d8d77d\transformed\jetified-security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa0837277d01c03505066fb50215c744\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\aa0837277d01c03505066fb50215c744\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1f051f0f1c29b25edae7e2402225c54a\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1f051f0f1c29b25edae7e2402225c54a\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1339509c35234d1f6d8a88288e442424\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1339509c35234d1f6d8a88288e442424\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5d2d79799f06b4cd6081e093663f9a2d\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5d2d79799f06b4cd6081e093663f9a2d\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-jvm:1.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.3.0\33d8b28dca2450e8656cfa23316eb656fb6f1299\collection-jvm-1.3.0.jar"
      resolved="androidx.collection:collection-jvm:1.3.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.7.0\920472d40adcdef5e18708976b3e314f9a636fcd\annotation-jvm-1.7.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.7.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.21\17ee3e873d439566c7d8354403b5f3d9744c4c9c\kotlin-stdlib-1.9.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.21"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1f408c62c79972772e7949740c22acd\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1f408c62c79972772e7949740c22acd\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.dagger:hilt-core:2.48@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\hilt-core\2.48\b4568d616aefe08946cdeb1b7ad251d107a4a225\hilt-core-2.48.jar"
      resolved="com.google.dagger:hilt-core:2.48"/>
  <library
      name="com.google.dagger:dagger:2.48@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.dagger\dagger\2.48\c4a5ecf0eb4df3a726179657f1b586290dc08d1b\dagger-2.48.jar"
      resolved="com.google.dagger:dagger:2.48"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.dagger:dagger-lint-aar:2.48@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b7aa8d699d8789fab25184f3cfbe009d\transformed\jetified-dagger-lint-aar-2.48\jars\classes.jar"
      resolved="com.google.dagger:dagger-lint-aar:2.48"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b7aa8d699d8789fab25184f3cfbe009d\transformed\jetified-dagger-lint-aar-2.48"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.code.gson:gson:2.8.9@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.8.9\8a432c1d6825781e21a02db2e2c33c5fde2833b9\gson-2.8.9.jar"
      resolved="com.google.code.gson:gson:2.8.9"/>
  <library
      name="androidx.hilt:hilt-common:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.hilt\hilt-common\1.1.0\9ca9c006cfce81d1435a0735fdab4b9e1166faa1\hilt-common-1.1.0.jar"
      resolved="androidx.hilt:hilt-common:1.1.0"/>
  <library
      name="io.github.vanpra.compose-material-dialogs:core:0.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d289947dc05ba772b8b9a976ea524366\transformed\jetified-core-0.9.0\jars\classes.jar"
      resolved="io.github.vanpra.compose-material-dialogs:core:0.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d289947dc05ba772b8b9a976ea524366\transformed\jetified-core-0.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-pager:0.25.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\cac6be9f0bf98e7b0bac1e8a2d3f652b\transformed\jetified-accompanist-pager-0.25.1\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-pager:0.25.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\cac6be9f0bf98e7b0bac1e8a2d3f652b\transformed\jetified-accompanist-pager-0.25.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="dev.chrisbanes.snapper:snapper:0.2.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\da8f4f23f6d9c24245c9658b2f37e3f8\transformed\jetified-snapper-0.2.2\jars\classes.jar"
      resolved="dev.chrisbanes.snapper:snapper:0.2.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\da8f4f23f6d9c24245c9658b2f37e3f8\transformed\jetified-snapper-0.2.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.5.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\21944fbce2e9c2c2aefa4eb759a36382\transformed\jetified-ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.5.4"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\21944fbce2e9c2c2aefa4eb759a36382\transformed\jetified-ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\569bdd61d1386dd307cc0529d8f2d9d7\transformed\jetified-appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\569bdd61d1386dd307cc0529d8f2d9d7\transformed\jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e741e5374256fd7ad708d21850e91de0\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e741e5374256fd7ad708d21850e91de0\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0999956f64fcc36af7e766e022e1cfce\transformed\jetified-emoji2-views-helper-1.4.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0999956f64fcc36af7e766e022e1cfce\transformed\jetified-emoji2-views-helper-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5986a4beb4ee10d2c93ece8e78c80058\transformed\jetified-emoji2-1.4.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.12\transforms\5986a4beb4ee10d2c93ece8e78c80058\transformed\jetified-emoji2-1.4.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5986a4beb4ee10d2c93ece8e78c80058\transformed\jetified-emoji2-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1bf3d8d92deaa88d8c5c5dc3cf014ac8\transformed\jetified-lifecycle-process-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1bf3d8d92deaa88d8c5c5dc3cf014ac8\transformed\jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\04136c161a6bf2b7edcfa4d07fb848cb\transformed\jetified-lifecycle-service-2.7.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.7.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\04136c161a6bf2b7edcfa4d07fb848cb\transformed\jetified-lifecycle-service-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.accompanist:accompanist-drawablepainter:0.32.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb34808bf58b43718e1307fc87d20f4\transformed\jetified-accompanist-drawablepainter-0.32.0\jars\classes.jar"
      resolved="com.google.accompanist:accompanist-drawablepainter:0.32.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb34808bf58b43718e1307fc87d20f4\transformed\jetified-accompanist-drawablepainter-0.32.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\77084ad54196798dbf9e06278c7397cb\transformed\jetified-autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\77084ad54196798dbf9e06278c7397cb\transformed\jetified-autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ee7cd67af91bbd6f474e2e0a09048df9\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ee7cd67af91bbd6f474e2e0a09048df9\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4653c082e7731e1e04e90a3182e20caa\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4653c082e7731e1e04e90a3182e20caa\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\39a2bdeaa7b36e5bd7aece1f127567ba\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\39a2bdeaa7b36e5bd7aece1f127567ba\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ba0e3f74786ecf5f066e0119516d3b4a\transformed\jetified-customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ba0e3f74786ecf5f066e0119516d3b4a\transformed\jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.github.aakira:napier-android-debug:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ea2c6e20bd8dfb17510a85bfddcea668\transformed\jetified-napier-debug\jars\classes.jar"
      resolved="io.github.aakira:napier-android-debug:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ea2c6e20bd8dfb17510a85bfddcea668\transformed\jetified-napier-debug"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\dd740f23258a1bcb40c52c5c63e35572\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\dd740f23258a1bcb40c52c5c63e35572\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e351054fabdf48c66984a9f8ffa1b531\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e351054fabdf48c66984a9f8ffa1b531\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d4c2802bd39d3d2190d7234e26ee43c\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1d4c2802bd39d3d2190d7234e26ee43c\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1724b8218b6543eb2c0581ff29481f45\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1724b8218b6543eb2c0581ff29481f45\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.3.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.3.0.jar"
      resolved="androidx.collection:collection-ktx:1.3.0"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\50e09db5e7a2a9dfe742a106960ae2d6\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\50e09db5e7a2a9dfe742a106960ae2d6\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.crypto.tink:tink-android:1.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.crypto.tink\tink-android\1.8.0\bda82b49568d444a3b54773ca5aa487816473295\tink-android-1.8.0.jar"
      resolved="com.google.crypto.tink:tink-android:1.8.0"/>
</libraries>
