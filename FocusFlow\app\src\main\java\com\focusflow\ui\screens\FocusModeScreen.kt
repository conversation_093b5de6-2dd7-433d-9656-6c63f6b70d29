package com.focusflow.ui.screens

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.data.model.Task
import com.focusflow.ui.viewmodel.FocusModeViewModel
import kotlinx.coroutines.delay

@Composable
fun FocusModeScreen(
    viewModel: FocusModeViewModel = hiltViewModel(),
    onNavigateBack: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val currentTask by viewModel.currentTask.collectAsStateWithLifecycle()
    
    // Dark theme for focus mode
    MaterialTheme(
        colors = darkColors(
            primary = Color(0xFF6200EA),
            primaryVariant = Color(0xFF3700B3),
            secondary = Color(0xFF03DAC6),
            background = Color(0xFF121212),
            surface = Color(0xFF1E1E1E),
            onBackground = Color.White,
            onSurface = Color.White
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colors.background)
        ) {
            when {
                uiState.isSessionActive -> {
                    ActiveFocusSession(
                        task = currentTask,
                        timeRemaining = uiState.timeRemaining,
                        totalTime = uiState.sessionDuration,
                        isBreak = uiState.isBreakTime,
                        onPause = { viewModel.pauseSession() },
                        onStop = { viewModel.stopSession() },
                        onCompleteTask = { viewModel.completeCurrentTask() }
                    )
                }
                uiState.isSessionPaused -> {
                    PausedFocusSession(
                        task = currentTask,
                        timeRemaining = uiState.timeRemaining,
                        onResume = { viewModel.resumeSession() },
                        onStop = { viewModel.stopSession() }
                    )
                }
                else -> {
                    FocusModeSetup(
                        availableTasks = uiState.availableTasks,
                        selectedTask = uiState.selectedTask,
                        sessionDuration = uiState.sessionDuration,
                        breakDuration = uiState.breakDuration,
                        onTaskSelected = { viewModel.selectTask(it) },
                        onSessionDurationChanged = { viewModel.setSessionDuration(it) },
                        onBreakDurationChanged = { viewModel.setBreakDuration(it) },
                        onStartSession = { viewModel.startFocusSession() },
                        onNavigateBack = onNavigateBack
                    )
                }
            }
        }
    }
}

@Composable
fun FocusModeSetup(
    availableTasks: List<Task>,
    selectedTask: Task?,
    sessionDuration: Int,
    breakDuration: Int,
    onTaskSelected: (Task) -> Unit,
    onSessionDurationChanged: (Int) -> Unit,
    onBreakDurationChanged: (Int) -> Unit,
    onStartSession: () -> Unit,
    onNavigateBack: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colors.onBackground
                )
            }
            
            Text(
                text = "🎯 Focus Mode",
                style = MaterialTheme.typography.h5,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.onBackground
            )
            
            Spacer(modifier = Modifier.width(48.dp))
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Focus mode explanation
        Card(
            modifier = Modifier.fillMaxWidth(),
            backgroundColor = MaterialTheme.colors.surface,
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "🧠 ADHD-Friendly Focus",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colors.onSurface
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "• Distraction-free environment\n• Built-in break reminders\n• Progress tracking\n• Gentle notifications",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Task selection
        if (availableTasks.isNotEmpty()) {
            Text(
                text = "Select a task to focus on:",
                style = MaterialTheme.typography.subtitle1,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colors.onBackground
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            availableTasks.take(3).forEach { task ->
                TaskSelectionCard(
                    task = task,
                    isSelected = selectedTask?.id == task.id,
                    onClick = { onTaskSelected(task) }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Duration settings
        Text(
            text = "Session Settings",
            style = MaterialTheme.typography.subtitle1,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colors.onBackground
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        DurationSelector(
            label = "Focus Time",
            value = sessionDuration,
            onValueChange = onSessionDurationChanged,
            options = listOf(15, 25, 45, 60)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        DurationSelector(
            label = "Break Time",
            value = breakDuration,
            onValueChange = onBreakDurationChanged,
            options = listOf(5, 10, 15, 20)
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        // Start button
        Button(
            onClick = onStartSession,
            enabled = selectedTask != null,
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                backgroundColor = MaterialTheme.colors.primary
            ),
            shape = RoundedCornerShape(28.dp)
        ) {
            Icon(Icons.Default.PlayArrow, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = "Start Focus Session",
                style = MaterialTheme.typography.button,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Composable
fun TaskSelectionCard(
    task: Task,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp)),
        backgroundColor = if (isSelected) MaterialTheme.colors.primary.copy(alpha = 0.2f) else MaterialTheme.colors.surface,
        elevation = if (isSelected) 8.dp else 2.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = MaterialTheme.colors.primary
                )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = task.title,
                    style = MaterialTheme.typography.body1,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colors.onSurface
                )
                
                task.estimatedDuration?.let { duration ->
                    Text(
                        text = "~$duration minutes",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
            
            Text(
                text = when (task.priority) {
                    "high" -> "🔴"
                    "medium" -> "🟡"
                    "low" -> "🟢"
                    else -> "⚪"
                },
                style = MaterialTheme.typography.body1
            )
        }
    }
}

@Composable
fun DurationSelector(
    label: String,
    value: Int,
    onValueChange: (Int) -> Unit,
    options: List<Int>
) {
    Column {
        Text(
            text = label,
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.onBackground.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            options.forEach { option ->
                FilterChip(
                    selected = value == option,
                    onClick = { onValueChange(option) },
                    colors = ChipDefaults.filterChipColors(
                        selectedBackgroundColor = MaterialTheme.colors.primary,
                        selectedContentColor = Color.White
                    )
                ) {
                    Text("${option}m")
                }
            }
        }
    }
}

@Composable
fun ActiveFocusSession(
    task: Task?,
    timeRemaining: Int,
    totalTime: Int,
    isBreak: Boolean,
    onPause: () -> Unit,
    onStop: () -> Unit,
    onCompleteTask: () -> Unit
) {
    val progress = if (totalTime > 0) (totalTime - timeRemaining).toFloat() / totalTime else 0f
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Session type indicator
        Text(
            text = if (isBreak) "☕ Break Time" else "🎯 Focus Time",
            style = MaterialTheme.typography.h5,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colors.onBackground
        )
        
        if (!isBreak && task != null) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = task.title,
                style = MaterialTheme.typography.h6,
                color = MaterialTheme.colors.onBackground.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )
        }
        
        Spacer(modifier = Modifier.height(48.dp))
        
        // Circular progress timer
        Box(
            contentAlignment = Alignment.Center
        ) {
            CircularProgressTimer(
                progress = progress,
                timeRemaining = timeRemaining,
                isBreak = isBreak
            )
        }
        
        Spacer(modifier = Modifier.height(48.dp))
        
        // Control buttons
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            IconButton(
                onClick = onPause,
                modifier = Modifier
                    .size(64.dp)
                    .background(
                        MaterialTheme.colors.surface,
                        CircleShape
                    )
            ) {
                Icon(
                    Icons.Default.Pause,
                    contentDescription = "Pause",
                    tint = MaterialTheme.colors.onSurface,
                    modifier = Modifier.size(32.dp)
                )
            }
            
            IconButton(
                onClick = onStop,
                modifier = Modifier
                    .size(64.dp)
                    .background(
                        Color(0xFFF44336),
                        CircleShape
                    )
            ) {
                Icon(
                    Icons.Default.Stop,
                    contentDescription = "Stop",
                    tint = Color.White,
                    modifier = Modifier.size(32.dp)
                )
            }
            
            if (!isBreak && task != null) {
                IconButton(
                    onClick = onCompleteTask,
                    modifier = Modifier
                        .size(64.dp)
                        .background(
                            Color(0xFF4CAF50),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.Check,
                        contentDescription = "Complete",
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun CircularProgressTimer(
    progress: Float,
    timeRemaining: Int,
    isBreak: Boolean
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(durationMillis = 1000)
    )
    
    Box(
        modifier = Modifier.size(200.dp),
        contentAlignment = Alignment.Center
    ) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val strokeWidth = 12.dp.toPx()
            val radius = (size.minDimension - strokeWidth) / 2
            
            // Background circle
            drawCircle(
                color = Color.Gray.copy(alpha = 0.3f),
                radius = radius,
                style = Stroke(strokeWidth)
            )
            
            // Progress arc
            drawArc(
                color = if (isBreak) Color(0xFF4CAF50) else Color(0xFF6200EA),
                startAngle = -90f,
                sweepAngle = animatedProgress * 360f,
                useCenter = false,
                style = Stroke(strokeWidth)
            )
        }
        
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = formatTime(timeRemaining),
                style = MaterialTheme.typography.h3,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colors.onBackground
            )
            
            Text(
                text = "remaining",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onBackground.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun PausedFocusSession(
    task: Task?,
    timeRemaining: Int,
    onResume: () -> Unit,
    onStop: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "⏸️ Session Paused",
            style = MaterialTheme.typography.h5,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colors.onBackground
        )
        
        if (task != null) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = task.title,
                style = MaterialTheme.typography.h6,
                color = MaterialTheme.colors.onBackground.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        Text(
            text = formatTime(timeRemaining),
            style = MaterialTheme.typography.h2,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colors.onBackground
        )
        
        Text(
            text = "remaining",
            style = MaterialTheme.typography.body1,
            color = MaterialTheme.colors.onBackground.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(48.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Button(
                onClick = onResume,
                modifier = Modifier.height(48.dp),
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = MaterialTheme.colors.primary
                )
            ) {
                Icon(Icons.Default.PlayArrow, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Resume")
            }
            
            OutlinedButton(
                onClick = onStop,
                modifier = Modifier.height(48.dp)
            ) {
                Icon(Icons.Default.Stop, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Stop")
            }
        }
    }
}

private fun formatTime(seconds: Int): String {
    val minutes = seconds / 60
    val remainingSeconds = seconds % 60
    return "%02d:%02d".format(minutes, remainingSeconds)
}
