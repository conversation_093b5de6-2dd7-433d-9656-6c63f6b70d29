package com.focusflow.service

import com.focusflow.data.model.*
import com.focusflow.data.repository.HelpRepository
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service to extract marketing-ready content from help system
 * Transforms comprehensive help content into app store listings and promotional materials
 */
@Singleton
class MarketingContentExtractor @Inject constructor(
    private val helpRepository: HelpRepository,
    private val crashReportingManager: CrashReportingManager
) {
    
    suspend fun generateAppStoreContent(): AppStoreContent {
        return try {
            val categories = helpRepository.getAllCategories()
            val featuredArticles = helpRepository.getFeaturedArticles(10)
            val faqs = helpRepository.getCommonFAQs(20)
            
            AppStoreContent(
                shortDescription = generateShortDescription(),
                fullDescription = generateFullDescription(),
                featureHighlights = extractFeatureHighlights(),
                screenshots = generateScreenshotDescriptions(),
                keywordList = generateKeywords(),
                whatMakesItSpecial = generateDifferentiators(),
                userBenefits = extractUserBenefits(),
                adhdSpecificBenefits = extractADHDBenefits(),
                faqSection = generateMarketingFAQs(),
                callToAction = generateCallToAction()
            )
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            getDefaultAppStoreContent()
        }
    }
    
    private fun generateShortDescription(): String {
        return "ADHD-friendly personal finance app with visual budgeting and focus tools"
    }
    
    private fun generateFullDescription(): String {
        return """
**Finally, a personal finance app designed specifically for ADHD minds! 🧠💰**

FocusFlow transforms overwhelming financial management into an engaging, manageable experience. Built by understanding how ADHD affects financial decision-making, every feature is designed to work WITH your brain, not against it.

## 🎯 **ADHD-Optimized Features**

**Visual Budgeting That Makes Sense**
• Envelope-style budget visualization - see your money at a glance
• Color-coded spending categories with clear visual indicators
• Safe-to-Spend widget shows exactly what you can afford right now
• No complex spreadsheets or overwhelming numbers

**Impulse Control Support**
• Smart spending alerts with gentle reflection questions
• 10-second cooling-off periods for large purchases
• Wishlist feature to capture impulse buys for later consideration
• Budget impact preview before you spend

**Task Management for Financial Goals**
• AI-powered task breakdown for overwhelming financial projects
• Focus Mode with distraction-free environment
• Procrastination detection with supportive interventions
• Energy-level matching for different types of tasks

**Gamification & Motivation**
• Virtual pet that thrives when you meet financial goals
• Achievement system celebrating small wins
• Habit streaks for expense tracking and budget check-ins
• Positive reinforcement messaging (never shame-based)

## 💡 **Core Features**

**Smart Expense Tracking**
• Quick expense entry with smart categorization
• Photo receipts with automatic data extraction
• Merchant recognition and spending pattern analysis
• Weekly and monthly spending summaries

**Zero-Based Budgeting Made Simple**
• Allocate every dollar with visual envelope system
• Real-time budget tracking with instant feedback
• Automatic rollover handling for unused funds
• Budget vs. actual spending comparisons

**Debt Payoff Planner**
• Snowball and avalanche method calculators
• Visual debt-free timeline with milestone celebrations
• Minimum payment tracking with due date reminders
• Extra payment impact visualization

**AI Financial Coach**
• Personalized insights based on your spending patterns
• ADHD-specific financial advice and strategies
• Gentle reminders and encouraging check-ins
• Custom recommendations for your unique situation

## 🔒 **Privacy & Security**

• All data stored locally on your device
• Bank-level encryption for sensitive information
• No selling of personal data to third parties
• GDPR compliant with full user control over data

## 🌟 **Why FocusFlow Works for ADHD**

**Reduces Cognitive Load**
• Clean, uncluttered interface design
• Progressive disclosure of complex information
• Visual hierarchy that guides attention naturally
• Minimal context switching between screens

**Supports Executive Function**
• Automated reminders and notifications
• Task breakdown for complex financial projects
• Visual progress tracking and milestone celebrations
• Gentle accountability without overwhelm

**Accommodates ADHD Challenges**
• Time blindness support with clear due dates and timers
• Hyperfocus protection with mandatory break reminders
• Procrastination intervention with task simplification
• Emotional regulation support through positive messaging

## 📱 **Perfect For**

• Adults with ADHD managing their finances
• Anyone who finds traditional budgeting apps overwhelming
• People who struggle with impulse spending
• Those who want visual, engaging financial tools
• Users seeking supportive, non-judgmental financial guidance

## 🚀 **Get Started Today**

Transform your relationship with money. Download FocusFlow and discover how personal finance can be engaging, manageable, and even enjoyable when designed for your ADHD brain.

**Your financial goals are achievable. Let FocusFlow help you get there, one small step at a time.**

*FocusFlow is developed by individuals who understand ADHD firsthand. We're committed to creating tools that truly work for neurodivergent minds.*
        """.trimIndent()
    }
    
    private fun extractFeatureHighlights(): List<FeatureHighlight> {
        return listOf(
            FeatureHighlight(
                title = "Safe-to-Spend Calculator",
                description = "Know exactly what you can spend without anxiety",
                benefits = listOf(
                    "Instant spending guidance",
                    "Reduces financial stress",
                    "Prevents overspending",
                    "Eliminates mental math"
                ),
                adhdBenefits = listOf(
                    "Eliminates decision paralysis",
                    "Provides immediate clarity",
                    "Reduces cognitive load",
                    "Works with ADHD impulsivity"
                ),
                category = "budgeting",
                priority = 1
            ),
            FeatureHighlight(
                title = "Visual Envelope Budgeting",
                description = "See your money at a glance with intuitive visual budgets",
                benefits = listOf(
                    "Easy budget visualization",
                    "Real-time spending tracking",
                    "Color-coded categories",
                    "Zero-based budgeting made simple"
                ),
                adhdBenefits = listOf(
                    "Visual processing over text",
                    "Immediate feedback",
                    "Reduces overwhelm",
                    "Makes abstract concepts concrete"
                ),
                category = "budgeting",
                priority = 2
            ),
            FeatureHighlight(
                title = "Impulse Control Tools",
                description = "Built-in support for mindful spending decisions",
                benefits = listOf(
                    "Spending confirmation dialogs",
                    "Cooling-off periods",
                    "Budget impact preview",
                    "Wishlist for delayed gratification"
                ),
                adhdBenefits = listOf(
                    "Interrupts impulse-to-action pathway",
                    "Gentle, not restrictive",
                    "Works with ADHD brain patterns",
                    "Positive reinforcement approach"
                ),
                category = "spending",
                priority = 3
            ),
            FeatureHighlight(
                title = "AI Task Breakdown",
                description = "Complex financial tasks made manageable",
                benefits = listOf(
                    "Overwhelming projects simplified",
                    "Step-by-step guidance",
                    "Progress tracking",
                    "Personalized recommendations"
                ),
                adhdBenefits = listOf(
                    "Reduces executive function burden",
                    "Prevents procrastination",
                    "Builds momentum through small wins",
                    "Accommodates ADHD working memory"
                ),
                category = "productivity",
                priority = 4
            ),
            FeatureHighlight(
                title = "Virtual Pet Gamification",
                description = "Your financial companion that grows with your success",
                benefits = listOf(
                    "Increased motivation",
                    "Habit reinforcement",
                    "Progress visualization",
                    "Achievement system"
                ),
                adhdBenefits = listOf(
                    "Dopamine-driven engagement",
                    "Immediate positive feedback",
                    "Makes finance fun",
                    "Sustains long-term motivation"
                ),
                category = "gamification",
                priority = 5
            )
        )
    }
    
    private fun generateScreenshotDescriptions(): List<MarketingScreenshot> {
        return listOf(
            MarketingScreenshot(
                path = "screenshots/dashboard_hero.png",
                title = "ADHD-Friendly Dashboard",
                description = "Clear visual hierarchy with Safe-to-Spend prominently displayed",
                feature = "dashboard",
                overlayText = "See exactly what you can spend",
                isHeroImage = true
            ),
            MarketingScreenshot(
                path = "screenshots/visual_budgeting.png",
                title = "Visual Envelope Budgeting",
                description = "Intuitive budget visualization with color-coded categories",
                feature = "budgeting",
                overlayText = "Budgeting that makes sense"
            ),
            MarketingScreenshot(
                path = "screenshots/impulse_control.png",
                title = "Impulse Control Support",
                description = "Gentle spending confirmations with reflection questions",
                feature = "impulse_control",
                overlayText = "Pause, reflect, decide mindfully"
            ),
            MarketingScreenshot(
                path = "screenshots/task_breakdown.png",
                title = "AI Task Breakdown",
                description = "Complex financial tasks simplified into manageable steps",
                feature = "task_management",
                overlayText = "Break down overwhelming tasks"
            ),
            MarketingScreenshot(
                path = "screenshots/virtual_pet.png",
                title = "Gamification & Motivation",
                description = "Virtual pet and achievements keep you motivated",
                feature = "gamification",
                overlayText = "Your pet thrives when you do!"
            )
        )
    }
    
    private fun generateKeywords(): List<String> {
        return listOf(
            "ADHD", "personal finance", "budgeting", "expense tracking",
            "debt payoff", "financial planning", "neurodivergent", "visual budgeting",
            "impulse control", "financial wellness", "money management", "ADHD tools",
            "executive function", "financial coaching", "habit tracking", "gamification",
            "focus", "attention", "mindful spending", "anxiety-free", "supportive",
            "envelope budgeting", "safe to spend", "financial goals", "motivation"
        )
    }
    
    private fun generateDifferentiators(): List<String> {
        return listOf(
            "First personal finance app designed specifically for ADHD minds",
            "Visual hierarchy and clean design reduce cognitive load",
            "Impulse control tools work WITH ADHD brain patterns",
            "Positive reinforcement instead of shame-based messaging",
            "AI-powered task breakdown for executive function support",
            "Gamification elements maintain long-term engagement",
            "Local-first data storage for privacy and security",
            "Comprehensive help system with ADHD-specific guidance"
        )
    }
    
    private fun extractUserBenefits(): List<String> {
        return listOf(
            "Reduce financial anxiety with clear spending guidance",
            "Build healthy money habits through engaging gamification",
            "Get personalized ADHD-friendly financial coaching",
            "Track expenses effortlessly with quick-entry tools",
            "Visualize your budget with intuitive envelope system",
            "Control impulse spending with gentle intervention tools",
            "Break down overwhelming financial tasks into manageable steps",
            "Stay motivated with achievement system and virtual pet"
        )
    }
    
    private fun extractADHDBenefits(): List<String> {
        return listOf(
            "Designed specifically for ADHD cognitive patterns",
            "Reduces overwhelm with visual, intuitive interface",
            "Supports executive function with task breakdown and reminders",
            "Accommodates time blindness with clear due dates and timers",
            "Prevents hyperfocus burnout with mandatory break reminders",
            "Uses positive reinforcement to build sustainable habits",
            "Minimizes context switching with streamlined navigation",
            "Provides immediate feedback to maintain engagement"
        )
    }
    
    private fun generateMarketingFAQs(): List<MarketingFAQ> {
        return listOf(
            MarketingFAQ(
                question = "How is FocusFlow different from other budgeting apps?",
                answer = "FocusFlow is the first personal finance app designed specifically for ADHD minds, with features like impulse control tools, visual budgeting, and AI task breakdown."
            ),
            MarketingFAQ(
                question = "Is my financial data safe?",
                answer = "Yes! Your data is protected with bank-level encryption and stored locally on your device. We never sell your personal information."
            ),
            MarketingFAQ(
                question = "Do I need to have ADHD to use FocusFlow?",
                answer = "While designed for ADHD users, anyone who finds traditional budgeting overwhelming will benefit from FocusFlow's visual, supportive approach."
            )
        )
    }
    
    private fun generateCallToAction(): String {
        return "Transform your relationship with money. Download FocusFlow today and discover how personal finance can be engaging, manageable, and even enjoyable when designed for your ADHD brain. Your financial goals are achievable - let FocusFlow help you get there, one small step at a time! 🌟"
    }
    
    private fun getDefaultAppStoreContent(): AppStoreContent {
        return AppStoreContent(
            shortDescription = "ADHD-friendly personal finance with visual budgeting",
            fullDescription = "Personal finance app designed for ADHD minds with visual budgeting, impulse control, and gamification.",
            featureHighlights = emptyList(),
            screenshots = emptyList(),
            keywordList = listOf("ADHD", "budgeting", "finance"),
            whatMakesItSpecial = emptyList(),
            userBenefits = emptyList(),
            adhdSpecificBenefits = emptyList(),
            faqSection = emptyList(),
            callToAction = "Download FocusFlow today!"
        )
    }
}

/**
 * Data classes for marketing content
 */
data class AppStoreContent(
    val shortDescription: String,
    val fullDescription: String,
    val featureHighlights: List<FeatureHighlight>,
    val screenshots: List<MarketingScreenshot>,
    val keywordList: List<String>,
    val whatMakesItSpecial: List<String>,
    val userBenefits: List<String>,
    val adhdSpecificBenefits: List<String>,
    val faqSection: List<MarketingFAQ>,
    val callToAction: String
)

data class MarketingFAQ(
    val question: String,
    val answer: String
)
