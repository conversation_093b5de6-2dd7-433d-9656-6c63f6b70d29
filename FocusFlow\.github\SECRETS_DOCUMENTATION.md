# GitHub Repository Secrets Documentation

## Overview

This document outlines all required GitHub repository secrets for the FocusFlow Android CI/CD pipeline. These secrets are essential for production builds, app signing, and automated deployment to Google Play Store.

## Required Secrets

### 🔐 **Android App Signing Secrets**

#### `KEYSTORE_BASE64`
- **Purpose**: Base64 encoded Android keystore file for production app signing
- **Type**: String (Base64 encoded binary)
- **Required for**: Release builds, Google Play Store deployment
- **Security Level**: Critical - Never expose or commit to repository

#### `SIGNING_KEY_ALIAS`
- **Purpose**: Alias name for the signing key within the keystore
- **Type**: String
- **Example**: `focusflow-release-key`
- **Required for**: Release builds
- **Security Level**: Moderate - Can be descriptive but should not reveal sensitive info

#### `SIGNING_KEY_PASSWORD`
- **Purpose**: Password for the specific signing key within the keystore
- **Type**: String
- **Required for**: Release builds
- **Security Level**: Critical - Use strong, unique password

#### `SIGNING_STORE_PASSWORD`
- **Purpose**: Password for the keystore file itself
- **Type**: String
- **Required for**: Release builds
- **Security Level**: Critical - Use strong, unique password

### 🚀 **Google Play Console Deployment Secrets**

#### `GOOGLE_PLAY_SERVICE_ACCOUNT_JSON`
- **Purpose**: Service account JSON for Google Play Console API access
- **Type**: String (JSON content)
- **Required for**: Automated deployment to Google Play Store
- **Security Level**: Critical - Provides API access to Play Console
- **Permissions Required**:
  - Edit and delete draft apps
  - Manage app releases
  - View app information and download bulk reports

## Secret Categories by Environment

### Production Secrets (Required for Release)
```
KEYSTORE_BASE64
SIGNING_KEY_ALIAS
SIGNING_KEY_PASSWORD
SIGNING_STORE_PASSWORD
GOOGLE_PLAY_SERVICE_ACCOUNT_JSON
```

### Development Secrets (Optional)
```
None - Development builds use debug signing
```

### CI/CD Secrets (Infrastructure)
```
All production secrets are required for full CI/CD functionality
```

## Security Requirements

### Password Complexity
- **Minimum Length**: 16 characters
- **Character Types**: Uppercase, lowercase, numbers, special characters
- **Uniqueness**: Each password must be unique across all secrets
- **Rotation**: Passwords should be rotated annually

### Access Control
- **Repository Access**: Only repository administrators should manage secrets
- **Audit Trail**: All secret access is logged by GitHub
- **Principle of Least Privilege**: Secrets are only accessible to necessary workflows

### Storage Security
- **GitHub Encryption**: All secrets are encrypted at rest by GitHub
- **Transit Encryption**: Secrets are encrypted in transit during workflow execution
- **Memory Protection**: Secrets are masked in workflow logs
- **Temporary Access**: Secrets are only available during workflow execution

## Validation and Fallback Behavior

### Secret Availability Checks
The CI/CD pipeline includes validation steps that:
- Check for required secrets before attempting to use them
- Provide clear error messages if secrets are missing
- Skip deployment steps gracefully when secrets are unavailable
- Continue with basic build and test operations when possible

### Development Environment Support
- **Fork Compatibility**: Pipeline works in forked repositories without production secrets
- **Debug Builds**: Always available without any secrets
- **Testing**: Unit and UI tests run without production secrets
- **Linting**: Code quality checks work without secrets

### Error Handling
- **Missing Secrets**: Clear error messages with setup instructions
- **Invalid Secrets**: Validation steps catch malformed or incorrect secrets
- **Permission Issues**: Helpful messages for Google Play Console permission problems
- **Network Issues**: Retry logic for deployment operations

## Compliance and Auditing

### Security Standards
- **SOC 2 Type II**: GitHub's security compliance
- **ISO 27001**: Information security management
- **PCI DSS**: Payment card industry standards (where applicable)

### Audit Requirements
- **Access Logging**: All secret access is automatically logged
- **Change Tracking**: Secret updates are tracked with timestamps
- **Review Process**: Regular review of secret usage and access patterns

### Data Protection
- **GDPR Compliance**: No personal data stored in secrets
- **Data Minimization**: Only necessary secrets are stored
- **Right to Erasure**: Secrets can be deleted when no longer needed

## Troubleshooting

### Common Issues

#### "Secret not found" Error
```
Error: Secret KEYSTORE_BASE64 not found
Solution: Verify secret is added to repository settings with correct name
```

#### "Invalid keystore" Error
```
Error: Invalid keystore format
Solution: Ensure keystore is properly base64 encoded
```

#### "Google Play API permission denied"
```
Error: The caller does not have permission
Solution: Verify service account has correct permissions in Play Console
```

#### "Keystore password incorrect"
```
Error: Keystore was tampered with, or password was incorrect
Solution: Verify SIGNING_STORE_PASSWORD matches keystore password
```

### Debug Steps
1. **Verify Secret Names**: Ensure exact case-sensitive match
2. **Check Secret Content**: Verify no extra whitespace or characters
3. **Test Locally**: Validate keystore and passwords work locally first
4. **Review Permissions**: Confirm Google Play Console service account permissions
5. **Check Logs**: Review workflow logs for specific error messages

## Maintenance Schedule

### Regular Tasks
- **Monthly**: Review secret access logs
- **Quarterly**: Validate all secrets are still functional
- **Annually**: Rotate all passwords and regenerate keystore if needed
- **As Needed**: Update service account permissions

### Emergency Procedures
- **Compromised Secret**: Immediately rotate affected credentials
- **Lost Access**: Use backup service account or regenerate keystore
- **Permission Changes**: Update Google Play Console permissions promptly

## Contact Information

### Support Channels
- **Primary**: Repository administrators
- **Secondary**: DevOps team
- **Emergency**: Security team

### Documentation Updates
- **Owner**: DevOps team
- **Review Cycle**: Quarterly
- **Last Updated**: [Current Date]
- **Next Review**: [Next Quarter]

---

**⚠️ Security Notice**: Never share, commit, or expose these secrets. Always use GitHub's secure secret management system. Report any suspected security issues immediately.
