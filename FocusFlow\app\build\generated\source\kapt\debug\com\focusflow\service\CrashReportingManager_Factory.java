package com.focusflow.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CrashReportingManager_Factory implements Factory<CrashReportingManager> {
  private final Provider<Context> contextProvider;

  public CrashReportingManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CrashReportingManager get() {
    return newInstance(contextProvider.get());
  }

  public static CrashReportingManager_Factory create(Provider<Context> contextProvider) {
    return new CrashReportingManager_Factory(contextProvider);
  }

  public static CrashReportingManager newInstance(Context context) {
    return new CrashReportingManager(context);
  }
}
