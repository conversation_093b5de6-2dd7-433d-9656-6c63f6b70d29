package com.focusflow.service;

import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.BudgetRecommendationRepository;
import com.focusflow.data.repository.ExpenseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BudgetRecommendationService_Factory implements Factory<BudgetRecommendationService> {
  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  private final Provider<BudgetRecommendationRepository> budgetRecommendationRepositoryProvider;

  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  public BudgetRecommendationService_Factory(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<BudgetRecommendationRepository> budgetRecommendationRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider) {
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
    this.budgetRecommendationRepositoryProvider = budgetRecommendationRepositoryProvider;
    this.expenseRepositoryProvider = expenseRepositoryProvider;
  }

  @Override
  public BudgetRecommendationService get() {
    return newInstance(budgetCategoryRepositoryProvider.get(), budgetRecommendationRepositoryProvider.get(), expenseRepositoryProvider.get());
  }

  public static BudgetRecommendationService_Factory create(
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<BudgetRecommendationRepository> budgetRecommendationRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider) {
    return new BudgetRecommendationService_Factory(budgetCategoryRepositoryProvider, budgetRecommendationRepositoryProvider, expenseRepositoryProvider);
  }

  public static BudgetRecommendationService newInstance(
      BudgetCategoryRepository budgetCategoryRepository,
      BudgetRecommendationRepository budgetRecommendationRepository,
      ExpenseRepository expenseRepository) {
    return new BudgetRecommendationService(budgetCategoryRepository, budgetRecommendationRepository, expenseRepository);
  }
}
