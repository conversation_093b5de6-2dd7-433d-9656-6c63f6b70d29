package com.focusflow.viewmodel;

import com.focusflow.data.repository.HelpRepository;
import com.focusflow.service.CrashReportingManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HelpViewModel_Factory implements Factory<HelpViewModel> {
  private final Provider<HelpRepository> helpRepositoryProvider;

  private final Provider<CrashReportingManager> crashReportingManagerProvider;

  public HelpViewModel_Factory(Provider<HelpRepository> helpRepositoryProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    this.helpRepositoryProvider = helpRepositoryProvider;
    this.crashReportingManagerProvider = crashReportingManagerProvider;
  }

  @Override
  public HelpViewModel get() {
    return newInstance(helpRepositoryProvider.get(), crashReportingManagerProvider.get());
  }

  public static HelpViewModel_Factory create(Provider<HelpRepository> helpRepositoryProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    return new HelpViewModel_Factory(helpRepositoryProvider, crashReportingManagerProvider);
  }

  public static HelpViewModel newInstance(HelpRepository helpRepository,
      CrashReportingManager crashReportingManager) {
    return new HelpViewModel(helpRepository, crashReportingManager);
  }
}
