package com.focusflow.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0010\u0002\n\u0002\u0010\u0003\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0012\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0004\u00a8\u0006\u0005"}, d2 = {"reportToCrashlytics", "", "", "crashReportingManager", "Lcom/focusflow/service/CrashReportingManager;", "app_debug"})
public final class CrashReportingManagerKt {
    
    /**
     * Extension functions for easier crash reporting integration
     */
    public static final void reportToCrashlytics(@org.jetbrains.annotations.NotNull
    java.lang.Throwable $this$reportToCrashlytics, @org.jetbrains.annotations.NotNull
    com.focusflow.service.CrashReportingManager crashReportingManager) {
    }
}