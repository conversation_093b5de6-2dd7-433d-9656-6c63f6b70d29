package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.Task
import com.focusflow.data.repository.TaskRepository
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.service.GamificationService
import com.focusflow.service.NotificationManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class FocusModeViewModel @Inject constructor(
    private val taskRepository: TaskRepository,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val gamificationService: GamificationService,
    private val notificationManager: NotificationManager
) : ViewModel() {

    private val _uiState = MutableStateFlow(FocusModeUiState())
    val uiState: StateFlow<FocusModeUiState> = _uiState.asStateFlow()

    private val _currentTask = MutableStateFlow<Task?>(null)
    val currentTask: StateFlow<Task?> = _currentTask.asStateFlow()

    private var timerJob: Job? = null
    private var sessionStartTime: Long = 0

    init {
        loadAvailableTasks()
        loadUserPreferences()
    }

    private fun loadAvailableTasks() {
        viewModelScope.launch {
            taskRepository.getIncompleteTasks().collect { tasks ->
                _uiState.value = _uiState.value.copy(
                    availableTasks = tasks.filter { !it.isCompleted }
                )
            }
        }
    }

    private fun loadUserPreferences() {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                _uiState.value = _uiState.value.copy(
                    sessionDuration = preferences?.focusSessionDuration ?: 25,
                    breakDuration = preferences?.breakDuration ?: 5
                )
            } catch (e: Exception) {
                // Use defaults if preferences can't be loaded
                _uiState.value = _uiState.value.copy(
                    sessionDuration = 25,
                    breakDuration = 5
                )
            }
        }
    }

    fun selectTask(task: Task) {
        _currentTask.value = task
        _uiState.value = _uiState.value.copy(selectedTask = task)
    }

    fun setSessionDuration(minutes: Int) {
        _uiState.value = _uiState.value.copy(sessionDuration = minutes)
        saveUserPreferences()
    }

    fun setBreakDuration(minutes: Int) {
        _uiState.value = _uiState.value.copy(breakDuration = minutes)
        saveUserPreferences()
    }

    private fun saveUserPreferences() {
        viewModelScope.launch {
            try {
                val currentPrefs = userPreferencesRepository.getUserPreferencesSync()
                val updatedPrefs = currentPrefs?.copy(
                    focusSessionDuration = _uiState.value.sessionDuration,
                    breakDuration = _uiState.value.breakDuration
                )
                if (updatedPrefs != null) {
                    userPreferencesRepository.updateUserPreferences(updatedPrefs)
                }
            } catch (e: Exception) {
                // Silently fail - preferences are not critical
            }
        }
    }

    fun startFocusSession() {
        val selectedTask = _uiState.value.selectedTask
        if (selectedTask == null) return

        sessionStartTime = System.currentTimeMillis()
        _currentTask.value = selectedTask
        _uiState.value = _uiState.value.copy(
            isSessionActive = true,
            isSessionPaused = false,
            isBreakTime = false,
            timeRemaining = _uiState.value.sessionDuration * 60,
            totalSessionTime = _uiState.value.sessionDuration * 60
        )

        startTimer()
        
        // Send focus session start notification
        notificationManager.sendFocusSessionNotification(
            "Focus session started",
            "Working on: ${selectedTask.title}",
            isStart = true
        )
    }

    fun pauseSession() {
        timerJob?.cancel()
        _uiState.value = _uiState.value.copy(
            isSessionActive = false,
            isSessionPaused = true
        )
        
        notificationManager.sendFocusSessionNotification(
            "Focus session paused",
            "Take a moment to breathe",
            isStart = false
        )
    }

    fun resumeSession() {
        _uiState.value = _uiState.value.copy(
            isSessionActive = true,
            isSessionPaused = false
        )
        startTimer()
        
        notificationManager.sendFocusSessionNotification(
            "Focus session resumed",
            "Back to work!",
            isStart = true
        )
    }

    fun stopSession() {
        timerJob?.cancel()
        
        // Calculate session statistics
        val sessionDuration = System.currentTimeMillis() - sessionStartTime
        val completedMinutes = (sessionDuration / 60000).toInt()
        
        // Award points for partial completion
        if (completedMinutes >= 5) {
            viewModelScope.launch {
                try {
                    gamificationService.onFocusSessionCompleted(completedMinutes)
                } catch (e: Exception) {
                    // Don't fail session stop if gamification fails
                }
            }
        }
        
        resetSession()
        
        notificationManager.sendFocusSessionNotification(
            "Focus session ended",
            "Great effort! You focused for $completedMinutes minutes",
            isStart = false
        )
    }

    fun completeCurrentTask() {
        val task = _currentTask.value
        if (task != null) {
            viewModelScope.launch {
                try {
                    taskRepository.completeTask(task)
                    gamificationService.onTaskCompleted()
                    gamificationService.onFocusSessionCompleted(_uiState.value.sessionDuration)
                    
                    notificationManager.sendFocusSessionNotification(
                        "Task completed! 🎉",
                        "Great job finishing: ${task.title}",
                        isStart = false
                    )
                    
                    stopSession()
                } catch (e: Exception) {
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to complete task: ${e.message}"
                    )
                }
            }
        }
    }

    private fun startTimer() {
        timerJob?.cancel()
        timerJob = viewModelScope.launch {
            while (_uiState.value.timeRemaining > 0 && _uiState.value.isSessionActive) {
                delay(1000)
                val currentState = _uiState.value
                _uiState.value = currentState.copy(
                    timeRemaining = currentState.timeRemaining - 1
                )
            }
            
            // Timer finished
            if (_uiState.value.timeRemaining <= 0) {
                onTimerFinished()
            }
        }
    }

    private fun onTimerFinished() {
        val currentState = _uiState.value
        
        if (currentState.isBreakTime) {
            // Break finished, back to focus
            _uiState.value = currentState.copy(
                isBreakTime = false,
                timeRemaining = currentState.sessionDuration * 60,
                totalSessionTime = currentState.sessionDuration * 60
            )
            
            notificationManager.sendFocusSessionNotification(
                "Break time over! 🎯",
                "Ready to focus again?",
                isStart = true
            )
            
            startTimer()
        } else {
            // Focus session finished, start break
            viewModelScope.launch {
                try {
                    gamificationService.onFocusSessionCompleted(currentState.sessionDuration)
                } catch (e: Exception) {
                    // Don't fail session if gamification fails
                }
            }
            
            _uiState.value = currentState.copy(
                isBreakTime = true,
                timeRemaining = currentState.breakDuration * 60,
                totalSessionTime = currentState.breakDuration * 60
            )
            
            notificationManager.sendFocusSessionNotification(
                "Focus session complete! ☕",
                "Time for a ${currentState.breakDuration}-minute break",
                isStart = false
            )
            
            startTimer()
        }
    }

    private fun resetSession() {
        timerJob?.cancel()
        _currentTask.value = null
        _uiState.value = _uiState.value.copy(
            isSessionActive = false,
            isSessionPaused = false,
            isBreakTime = false,
            timeRemaining = 0,
            totalSessionTime = 0,
            selectedTask = null
        )
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    override fun onCleared() {
        super.onCleared()
        timerJob?.cancel()
    }
}

data class FocusModeUiState(
    val availableTasks: List<Task> = emptyList(),
    val selectedTask: Task? = null,
    val sessionDuration: Int = 25, // minutes
    val breakDuration: Int = 5, // minutes
    val isSessionActive: Boolean = false,
    val isSessionPaused: Boolean = false,
    val isBreakTime: Boolean = false,
    val timeRemaining: Int = 0, // seconds
    val totalSessionTime: Int = 0, // seconds
    val error: String? = null
)
