package com.focusflow.data.repository;

import android.content.Context;
import com.focusflow.service.FocusFlowNotificationManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NotificationRepository_Factory implements Factory<NotificationRepository> {
  private final Provider<Context> contextProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<FocusFlowNotificationManager> notificationManagerProvider;

  public NotificationRepository_Factory(Provider<Context> contextProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<FocusFlowNotificationManager> notificationManagerProvider) {
    this.contextProvider = contextProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.notificationManagerProvider = notificationManagerProvider;
  }

  @Override
  public NotificationRepository get() {
    return newInstance(contextProvider.get(), userPreferencesRepositoryProvider.get(), notificationManagerProvider.get());
  }

  public static NotificationRepository_Factory create(Provider<Context> contextProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<FocusFlowNotificationManager> notificationManagerProvider) {
    return new NotificationRepository_Factory(contextProvider, userPreferencesRepositoryProvider, notificationManagerProvider);
  }

  public static NotificationRepository newInstance(Context context,
      UserPreferencesRepository userPreferencesRepository,
      FocusFlowNotificationManager notificationManager) {
    return new NotificationRepository(context, userPreferencesRepository, notificationManager);
  }
}
