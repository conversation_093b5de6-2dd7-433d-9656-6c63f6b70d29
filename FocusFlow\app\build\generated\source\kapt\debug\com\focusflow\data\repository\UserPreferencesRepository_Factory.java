package com.focusflow.data.repository;

import com.focusflow.data.dao.UserPreferencesDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserPreferencesRepository_Factory implements Factory<UserPreferencesRepository> {
  private final Provider<UserPreferencesDao> userPreferencesDaoProvider;

  public UserPreferencesRepository_Factory(
      Provider<UserPreferencesDao> userPreferencesDaoProvider) {
    this.userPreferencesDaoProvider = userPreferencesDaoProvider;
  }

  @Override
  public UserPreferencesRepository get() {
    return newInstance(userPreferencesDaoProvider.get());
  }

  public static UserPreferencesRepository_Factory create(
      Provider<UserPreferencesDao> userPreferencesDaoProvider) {
    return new UserPreferencesRepository_Factory(userPreferencesDaoProvider);
  }

  public static UserPreferencesRepository newInstance(UserPreferencesDao userPreferencesDao) {
    return new UserPreferencesRepository(userPreferencesDao);
  }
}
