package com.focusflow.service;

import com.focusflow.data.dao.AchievementDao;
import com.focusflow.data.dao.UserStatsDao;
import com.focusflow.data.dao.VirtualPetDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GamificationService_Factory implements Factory<GamificationService> {
  private final Provider<AchievementDao> achievementDaoProvider;

  private final Provider<UserStatsDao> userStatsDaoProvider;

  private final Provider<VirtualPetDao> virtualPetDaoProvider;

  public GamificationService_Factory(Provider<AchievementDao> achievementDaoProvider,
      Provider<UserStatsDao> userStatsDaoProvider, Provider<VirtualPetDao> virtualPetDaoProvider) {
    this.achievementDaoProvider = achievementDaoProvider;
    this.userStatsDaoProvider = userStatsDaoProvider;
    this.virtualPetDaoProvider = virtualPetDaoProvider;
  }

  @Override
  public GamificationService get() {
    return newInstance(achievementDaoProvider.get(), userStatsDaoProvider.get(), virtualPetDaoProvider.get());
  }

  public static GamificationService_Factory create(Provider<AchievementDao> achievementDaoProvider,
      Provider<UserStatsDao> userStatsDaoProvider, Provider<VirtualPetDao> virtualPetDaoProvider) {
    return new GamificationService_Factory(achievementDaoProvider, userStatsDaoProvider, virtualPetDaoProvider);
  }

  public static GamificationService newInstance(AchievementDao achievementDao,
      UserStatsDao userStatsDao, VirtualPetDao virtualPetDao) {
    return new GamificationService(achievementDao, userStatsDao, virtualPetDao);
  }
}
