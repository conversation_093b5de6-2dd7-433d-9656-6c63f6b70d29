package com.focusflow.service;

/**
 * UI interaction events that are important for ADHD users
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/focusflow/service/ADHDUIEvents;", "", "()V", "ACCIDENTAL_BACK_NAVIGATION", "", "FOCUS_LOSS_DURING_TASK", "FORM_ABANDONMENT", "INSUFFICIENT_VISUAL_FEEDBACK", "NAVIGATION_CONFUSION", "OVERWHELMING_INFORMATION_DISPLAY", "REPEATED_BUTTON_TAPS", "app_debug"})
public final class ADHDUIEvents {
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String NAVIGATION_CONFUSION = "navigation_confusion";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String FORM_ABANDONMENT = "form_abandonment";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String REPEATED_BUTTON_TAPS = "repeated_button_taps";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String ACCIDENTAL_BACK_NAVIGATION = "accidental_back_navigation";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String FOCUS_LOSS_DURING_TASK = "focus_loss_during_task";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String OVERWHELMING_INFORMATION_DISPLAY = "overwhelming_information_display";
    @org.jetbrains.annotations.NotNull
    public static final java.lang.String INSUFFICIENT_VISUAL_FEEDBACK = "insufficient_visual_feedback";
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.ADHDUIEvents INSTANCE = null;
    
    private ADHDUIEvents() {
        super();
    }
}