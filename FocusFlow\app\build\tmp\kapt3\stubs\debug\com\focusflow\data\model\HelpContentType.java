package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0087\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/focusflow/data/model/HelpContentType;", "", "(Ljava/lang/String;I)V", "QUICK_START", "HOW_TO_GUIDE", "TROUBLESHOOTING", "BEST_PRACTICES", "ADHD_TIPS", "FAQ", "VIDEO_TUTORIAL", "INTERACTIVE_DEMO", "FEATURE_OVERVIEW", "app_debug"})
public enum HelpContentType {
    /*public static final*/ QUICK_START /* = new QUICK_START() */,
    /*public static final*/ HOW_TO_GUIDE /* = new HOW_TO_GUIDE() */,
    /*public static final*/ TROUBLESHOOTING /* = new TROUBLESHOOTING() */,
    /*public static final*/ BEST_PRACTICES /* = new BEST_PRACTICES() */,
    /*public static final*/ ADHD_TIPS /* = new ADHD_TIPS() */,
    /*public static final*/ FAQ /* = new FAQ() */,
    /*public static final*/ VIDEO_TUTORIAL /* = new VIDEO_TUTORIAL() */,
    /*public static final*/ INTERACTIVE_DEMO /* = new INTERACTIVE_DEMO() */,
    /*public static final*/ FEATURE_OVERVIEW /* = new FEATURE_OVERVIEW() */;
    
    HelpContentType() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.focusflow.data.model.HelpContentType> getEntries() {
        return null;
    }
}