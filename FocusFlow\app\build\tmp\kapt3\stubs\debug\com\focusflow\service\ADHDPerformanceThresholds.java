package com.focusflow.service;

/**
 * Performance monitoring constants for ADHD-optimized thresholds
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/focusflow/service/ADHDPerformanceThresholds;", "", "()V", "ACCEPTABLE_INPUT_RESPONSE_MS", "", "ACCEPTABLE_SCREEN_LOAD_MS", "CRITICAL_MEMORY_USAGE_PERCENT", "", "HIGH_MEMORY_USAGE_PERCENT", "MAX_AI_RESPONSE_MS", "OPTIMAL_INPUT_RESPONSE_MS", "OPTIMAL_SCREEN_LOAD_MS", "app_debug"})
public final class ADHDPerformanceThresholds {
    public static final long OPTIMAL_INPUT_RESPONSE_MS = 100L;
    public static final long ACCEPTABLE_INPUT_RESPONSE_MS = 300L;
    public static final long OPTIMAL_SCREEN_LOAD_MS = 1000L;
    public static final long ACCEPTABLE_SCREEN_LOAD_MS = 2000L;
    public static final long MAX_AI_RESPONSE_MS = 10000L;
    public static final int HIGH_MEMORY_USAGE_PERCENT = 80;
    public static final int CRITICAL_MEMORY_USAGE_PERCENT = 90;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.ADHDPerformanceThresholds INSTANCE = null;
    
    private ADHDPerformanceThresholds() {
        super();
    }
}