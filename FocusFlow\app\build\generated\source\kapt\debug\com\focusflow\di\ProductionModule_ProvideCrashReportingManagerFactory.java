package com.focusflow.di;

import android.content.Context;
import com.focusflow.service.CrashReportingManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductionModule_ProvideCrashReportingManagerFactory implements Factory<CrashReportingManager> {
  private final Provider<Context> contextProvider;

  public ProductionModule_ProvideCrashReportingManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public CrashReportingManager get() {
    return provideCrashReportingManager(contextProvider.get());
  }

  public static ProductionModule_ProvideCrashReportingManagerFactory create(
      Provider<Context> contextProvider) {
    return new ProductionModule_ProvideCrashReportingManagerFactory(contextProvider);
  }

  public static CrashReportingManager provideCrashReportingManager(Context context) {
    return Preconditions.checkNotNullFromProvides(ProductionModule.INSTANCE.provideCrashReportingManager(context));
  }
}
