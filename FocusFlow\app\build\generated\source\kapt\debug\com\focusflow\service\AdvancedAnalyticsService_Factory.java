package com.focusflow.service;

import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AdvancedAnalyticsService_Factory implements Factory<AdvancedAnalyticsService> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  private final Provider<PerformanceOptimizationService> performanceServiceProvider;

  public AdvancedAnalyticsService_Factory(Provider<FocusFlowDatabase> databaseProvider,
      Provider<PerformanceOptimizationService> performanceServiceProvider) {
    this.databaseProvider = databaseProvider;
    this.performanceServiceProvider = performanceServiceProvider;
  }

  @Override
  public AdvancedAnalyticsService get() {
    return newInstance(databaseProvider.get(), performanceServiceProvider.get());
  }

  public static AdvancedAnalyticsService_Factory create(
      Provider<FocusFlowDatabase> databaseProvider,
      Provider<PerformanceOptimizationService> performanceServiceProvider) {
    return new AdvancedAnalyticsService_Factory(databaseProvider, performanceServiceProvider);
  }

  public static AdvancedAnalyticsService newInstance(FocusFlowDatabase database,
      PerformanceOptimizationService performanceService) {
    return new AdvancedAnalyticsService(database, performanceService);
  }
}
