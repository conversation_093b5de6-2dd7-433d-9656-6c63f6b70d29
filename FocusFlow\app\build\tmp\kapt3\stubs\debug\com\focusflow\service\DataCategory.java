package com.focusflow.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/focusflow/service/DataCategory;", "", "(<PERSON>ja<PERSON>/lang/String;I)V", "EXPENSES", "BUDGET", "CREDIT_CARDS", "TASKS", "HABITS", "WISHLIST", "PREFERENCES", "SECURITY_DATA", "app_debug"})
public enum DataCategory {
    /*public static final*/ EXPENSES /* = new EXPENSES() */,
    /*public static final*/ BUDGET /* = new BUDGET() */,
    /*public static final*/ CREDIT_CARDS /* = new CREDIT_CARDS() */,
    /*public static final*/ TASKS /* = new TASKS() */,
    /*public static final*/ HABITS /* = new HABITS() */,
    /*public static final*/ WISHLIST /* = new WISHLIST() */,
    /*public static final*/ PREFERENCES /* = new PREFERENCES() */,
    /*public static final*/ SECURITY_DATA /* = new SECURITY_DATA() */;
    
    DataCategory() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.focusflow.service.DataCategory> getEntries() {
        return null;
    }
}