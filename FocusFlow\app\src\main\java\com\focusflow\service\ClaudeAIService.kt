package com.focusflow.service

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Real Claude AI service implementation for production use
 * Integrates with Anthropic's Claude API for personalized financial coaching
 */
@Singleton
class ClaudeAIService @Inject constructor(
    private val httpClient: OkHttpClient
) : AIService {

    companion object {
        private const val CLAUDE_API_URL = "https://api.anthropic.com/v1/messages"
        private const val MODEL = "claude-3-sonnet-20240229"
        private const val MAX_TOKENS = 1000
        
        // API key should be stored securely (e.g., in BuildConfig or secure storage)
        // For production, use proper secret management
        private const val API_KEY = "YOUR_CLAUDE_API_KEY_HERE" // Replace with actual key
    }

    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    override suspend fun generateResponse(
        prompt: String,
        context: String?,
        maxTokens: Int
    ): AIResponse = withContext(Dispatchers.IO) {
        try {
            val systemPrompt = """
                You are an ADHD-friendly financial coach integrated into the FocusFlow app. 
                Your responses should be:
                - Encouraging and positive (never shame-based)
                - Broken into digestible chunks with clear headings
                - Action-oriented with specific next steps
                - Empathetic to ADHD challenges like executive dysfunction and impulse control
                - Use emojis and visual elements to make content engaging
                - Keep responses concise but comprehensive
                
                Context: ${context ?: "General financial guidance"}
            """.trimIndent()

            val request = ClaudeRequest(
                model = MODEL,
                maxTokens = maxTokens,
                messages = listOf(
                    ClaudeMessage(
                        role = "user",
                        content = prompt
                    )
                ),
                system = systemPrompt
            )

            val response = makeClaudeRequest(request)
            
            AIResponse(
                content = response.content.firstOrNull()?.text ?: "I'm having trouble generating a response right now. Please try again.",
                confidence = 0.9,
                tokens = response.usage?.outputTokens ?: 0,
                model = MODEL
            )
        } catch (e: Exception) {
            // Fallback to mock response if API fails
            generateFallbackResponse(prompt)
        }
    }

    override suspend fun generateInsight(
        userContext: UserContext,
        insightType: InsightType
    ): AIResponse = withContext(Dispatchers.IO) {
        val prompt = when (insightType) {
            InsightType.SPENDING_PATTERN -> """
                Analyze this user's spending patterns and provide ADHD-friendly insights:
                Recent activity: ${userContext.recentActivity.joinToString(", ")}
                Goals: ${userContext.goals.joinToString(", ")}
                
                Focus on actionable patterns and gentle suggestions for improvement.
            """.trimIndent()
            
            InsightType.BUDGET_OPTIMIZATION -> """
                Provide budget optimization advice for someone with ADHD:
                User goals: ${userContext.goals.joinToString(", ")}
                
                Suggest simple, visual budgeting methods and automation strategies.
            """.trimIndent()
            
            InsightType.DEBT_STRATEGY -> """
                Create a debt payoff strategy that works for ADHD:
                User preferences: ${userContext.preferences}
                
                Focus on motivation-building approaches like the debt snowball method.
            """.trimIndent()
            
            InsightType.HABIT_IMPROVEMENT -> """
                Suggest habit improvements for financial tracking with ADHD in mind:
                Current habits: ${userContext.recentActivity.joinToString(", ")}
                
                Recommend habit stacking and environmental design strategies.
            """.trimIndent()
            
            InsightType.GOAL_PROGRESS -> """
                Provide encouraging progress feedback:
                User goals: ${userContext.goals.joinToString(", ")}
                Recent activity: ${userContext.recentActivity.joinToString(", ")}
                
                Celebrate wins and suggest next steps.
            """.trimIndent()
        }

        generateResponse(prompt, "Personalized insight generation")
    }

    override suspend fun analyzeSpending(
        expenses: List<ExpenseData>,
        budget: BudgetData?
    ): AIResponse {
        val totalSpent = expenses.sumOf { it.amount }
        val categoryBreakdown = expenses.groupBy { it.category }
            .mapValues { it.value.sumOf { expense -> expense.amount } }

        val prompt = """
            Analyze this spending data for someone with ADHD:
            
            Total spent: $${String.format("%.2f", totalSpent)}
            Number of transactions: ${expenses.size}
            Category breakdown: ${categoryBreakdown.entries.joinToString(", ") { "${it.key}: $${String.format("%.2f", it.value)}" }}
            ${budget?.let { "Weekly budget: $${String.format("%.2f", it.weeklyAmount)}" } ?: "No budget set"}
            
            Provide encouraging analysis with:
            1. What they're doing well
            2. Gentle suggestions for improvement
            3. ADHD-specific tips for better spending control
            4. Actionable next steps
        """.trimIndent()

        return generateResponse(prompt, "Spending analysis")
    }

    override suspend fun generateBudgetAdvice(
        currentBudget: BudgetData?,
        spendingHistory: List<ExpenseData>
    ): AIResponse {
        val prompt = if (currentBudget == null) {
            """
            Help create a first budget for someone with ADHD:
            Recent spending: ${spendingHistory.take(10).joinToString(", ") { "${it.category}: $${it.amount}" }}
            
            Provide:
            1. Simple 3-category budget recommendation
            2. ADHD-friendly budgeting methods
            3. Automation suggestions
            4. Visual tracking tips
            """
        } else {
            val avgSpending = spendingHistory.sumOf { it.amount } / maxOf(1, spendingHistory.size)
            """
            Optimize existing budget for ADHD user:
            Current weekly budget: $${currentBudget.weeklyAmount}
            Average spending: $${String.format("%.2f", avgSpending)}
            Categories: ${currentBudget.categories.entries.joinToString(", ") { "${it.key}: $${it.value}" }}
            
            Suggest improvements focusing on simplicity and automation.
            """
        }

        return generateResponse(prompt, "Budget advice")
    }

    override suspend fun analyzeDebtSituation(
        creditCards: List<CreditCardData>,
        paymentHistory: List<PaymentData>
    ): AIResponse {
        if (creditCards.isEmpty()) {
            return AIResponse(
                content = """
                    🎉 **Debt-Free Achievement!**
                    
                    Amazing work! You're currently debt-free. Here's how to stay that way with ADHD-friendly strategies:
                    
                    **Prevention Tips:**
                    • Set up automatic payments for any future cards
                    • Use the envelope budgeting method
                    • Create a "fun money" category for impulse purchases
                    • Practice the 24-hour rule for purchases over $50
                    
                    You're building excellent financial habits! 🌟
                """.trimIndent(),
                confidence = 0.95,
                tokens = 100,
                model = MODEL
            )
        }

        val totalDebt = creditCards.sumOf { it.balance }
        val totalMinPayments = creditCards.sumOf { it.minimumPayment }

        val prompt = """
            Create a debt payoff strategy for ADHD user:
            
            Credit cards: ${creditCards.joinToString(", ") { "${it.name}: $${it.balance} at ${it.interestRate}%" }}
            Total debt: $${String.format("%.2f", totalDebt)}
            Total minimum payments: $${String.format("%.2f", totalMinPayments)}
            
            Provide:
            1. Snowball vs Avalanche method comparison
            2. ADHD-specific motivation strategies
            3. Automation recommendations
            4. Celebration milestones
            5. Specific next steps
        """.trimIndent()

        return generateResponse(prompt, "Debt analysis")
    }

    override suspend fun generateTaskBreakdown(
        task: TaskData,
        userPreferences: UserPreferences
    ): List<String> {
        val prompt = """
            Break down this task for someone with ADHD:
            Task: "${task.title}"
            Description: ${task.description ?: "No description"}
            Estimated duration: ${task.estimatedDuration ?: "Unknown"} minutes
            Priority: ${task.priority}
            
            Create ${userPreferences.maxSubtasks ?: 5} specific, actionable subtasks that:
            - Are small enough to complete in 15-30 minutes each
            - Have clear completion criteria
            - Build momentum from easy to harder
            - Include any necessary preparation steps
            
            Return only the subtask list, one per line, without numbering.
        """.trimIndent()

        return try {
            val response = generateResponse(prompt, "Task breakdown")
            response.content.split("\n")
                .map { it.trim() }
                .filter { it.isNotBlank() && !it.startsWith("•") && !it.matches(Regex("^\\d+\\..*")) }
                .map { it.removePrefix("- ").removePrefix("• ") }
                .take(userPreferences.maxSubtasks ?: 5)
        } catch (e: Exception) {
            // Fallback to simple breakdown
            listOf(
                "Gather necessary materials and information",
                "Break the task into smaller 15-minute chunks",
                "Start with the easiest part to build momentum",
                "Set a timer and work in focused bursts",
                "Review and celebrate completion"
            ).take(userPreferences.maxSubtasks ?: 5)
        }
    }

    override suspend fun generateMotivationalMessage(
        userProgress: UserProgress,
        recentAchievements: List<Achievement>
    ): AIResponse {
        val prompt = """
            Create an encouraging message for ADHD user:
            Level: ${userProgress.level}
            Experience: ${userProgress.experience}
            Current streaks: ${userProgress.streaks.entries.joinToString(", ") { "${it.key}: ${it.value} days" }}
            Recent achievements: ${recentAchievements.joinToString(", ") { it.title }}
            
            Make it:
            - Genuinely encouraging (not patronizing)
            - Specific to their progress
            - Forward-looking with gentle next steps
            - Include relevant emoji
            - Keep it under 100 words
        """.trimIndent()

        return generateResponse(prompt, "Motivational message")
    }

    private suspend fun makeClaudeRequest(request: ClaudeRequest): ClaudeResponse {
        val requestBody = json.encodeToString(ClaudeRequest.serializer(), request)
            .toRequestBody("application/json".toMediaType())

        val httpRequest = Request.Builder()
            .url(CLAUDE_API_URL)
            .addHeader("Content-Type", "application/json")
            .addHeader("x-api-key", API_KEY)
            .addHeader("anthropic-version", "2023-06-01")
            .post(requestBody)
            .build()

        val response = httpClient.newCall(httpRequest).execute()
        
        if (!response.isSuccessful) {
            throw IOException("Claude API request failed: ${response.code}")
        }

        val responseBody = response.body?.string() ?: throw IOException("Empty response body")
        return json.decodeFromString(ClaudeResponse.serializer(), responseBody)
    }

    private fun generateFallbackResponse(prompt: String): AIResponse {
        val fallbackMessage = """
            I'm currently having trouble connecting to my AI service, but I'm here to help! 
            
            For immediate assistance with your financial question, here are some general ADHD-friendly tips:
            
            💡 **Quick Tips:**
            • Break large financial tasks into 15-minute chunks
            • Use visual tools like our envelope budgeting system
            • Set up automatic payments to reduce mental load
            • Celebrate small wins along your financial journey
            
            Please try your question again in a moment, or check out our built-in tools for immediate help! 🌟
        """.trimIndent()

        return AIResponse(
            content = fallbackMessage,
            confidence = 0.7,
            tokens = fallbackMessage.length / 4,
            model = "fallback-v1.0"
        )
    }
}

@Serializable
data class ClaudeRequest(
    val model: String,
    val maxTokens: Int,
    val messages: List<ClaudeMessage>,
    val system: String? = null
)

@Serializable
data class ClaudeMessage(
    val role: String,
    val content: String
)

@Serializable
data class ClaudeResponse(
    val content: List<ClaudeContent>,
    val usage: ClaudeUsage? = null
)

@Serializable
data class ClaudeContent(
    val text: String,
    val type: String = "text"
)

@Serializable
data class ClaudeUsage(
    val inputTokens: Int,
    val outputTokens: Int
)
