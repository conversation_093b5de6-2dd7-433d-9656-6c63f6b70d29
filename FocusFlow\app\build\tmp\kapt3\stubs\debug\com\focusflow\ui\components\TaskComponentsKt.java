package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000`\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u001a^\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u000328\u0010\u0004\u001a4\u0012\u0004\u0012\u00020\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0004\u0012\u00020\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u0006\u0012\u0006\u0012\u0004\u0018\u00010\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00060\nH\u0007\u001a\u001e\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\r2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aP\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00060\n2\u0006\u0010\u0013\u001a\u00020\u00142\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a$\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0018\u001a\u00020\r2\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u00010\u001aH\u0007\u001a\\\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010 \u001a\u00020\u00142\b\b\u0002\u0010!\u001a\u00020\"H\u0007\u001a*\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\u00062\u0006\u0010\'\u001a\u00020(H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b)\u0010*\u001a\u0010\u0010+\u001a\u00020\u00062\u0006\u0010,\u001a\u00020\u0007H\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006-"}, d2 = {"AddTaskDialog", "", "onDismiss", "Lkotlin/Function0;", "onAddTask", "Lkotlin/Function6;", "", "Lkotlinx/datetime/LocalDateTime;", "", "availableCategories", "", "EmptyTasksState", "filter", "Lcom/focusflow/ui/viewmodel/TaskFilter;", "onAddTaskClick", "TaskBreakdownDialog", "task", "Lcom/focusflow/data/model/Task;", "suggestedSubtasks", "isLoading", "", "onAccept", "onReject", "TaskFilterChips", "selectedFilter", "onFilterSelected", "Lkotlin/Function1;", "TaskItem", "onCompleteClick", "onEditClick", "onDeleteClick", "onBreakdownClick", "isProcrastinated", "modifier", "Landroidx/compose/ui/Modifier;", "TaskMetadataChip", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "text", "color", "Landroidx/compose/ui/graphics/Color;", "TaskMetadataChip-mxwnekA", "(Landroidx/compose/ui/graphics/vector/ImageVector;Ljava/lang/String;J)V", "formatDueDate", "dueDate", "app_debug"})
public final class TaskComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void TaskItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onCompleteClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onEditClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDeleteClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onBreakdownClick, boolean isProcrastinated, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TaskFilterChips(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.TaskFilter selectedFilter, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.TaskFilter, kotlin.Unit> onFilterSelected) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyTasksState(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.TaskFilter filter, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddTaskClick) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void AddTaskDialog(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function6<? super java.lang.String, ? super java.lang.String, ? super kotlinx.datetime.LocalDateTime, ? super java.lang.String, ? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onAddTask, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> availableCategories) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TaskBreakdownDialog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> suggestedSubtasks, boolean isLoading, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onAccept, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onReject, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    private static final java.lang.String formatDueDate(kotlinx.datetime.LocalDateTime dueDate) {
        return null;
    }
}