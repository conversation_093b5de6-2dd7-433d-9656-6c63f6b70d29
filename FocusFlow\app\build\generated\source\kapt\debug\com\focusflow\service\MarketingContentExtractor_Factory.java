package com.focusflow.service;

import com.focusflow.data.repository.HelpRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MarketingContentExtractor_Factory implements Factory<MarketingContentExtractor> {
  private final Provider<HelpRepository> helpRepositoryProvider;

  private final Provider<CrashReportingManager> crashReportingManagerProvider;

  public MarketingContentExtractor_Factory(Provider<HelpRepository> helpRepositoryProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    this.helpRepositoryProvider = helpRepositoryProvider;
    this.crashReportingManagerProvider = crashReportingManagerProvider;
  }

  @Override
  public MarketingContentExtractor get() {
    return newInstance(helpRepositoryProvider.get(), crashReportingManagerProvider.get());
  }

  public static MarketingContentExtractor_Factory create(
      Provider<HelpRepository> helpRepositoryProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    return new MarketingContentExtractor_Factory(helpRepositoryProvider, crashReportingManagerProvider);
  }

  public static MarketingContentExtractor newInstance(HelpRepository helpRepository,
      CrashReportingManager crashReportingManager) {
    return new MarketingContentExtractor(helpRepository, crashReportingManager);
  }
}
