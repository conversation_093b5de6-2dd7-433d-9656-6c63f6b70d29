<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.1">

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="        notificationManager.notify(notificationId, notification)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\receiver\AlarmReceiver.kt"
            line="78"
            column="9"/>
    </issue>

    <issue
        id="MissingPermission"
        severity="Error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`"
        category="Correctness"
        priority="9"
        summary="Missing Permissions"
        explanation="This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.&#xA;&#xA;Furthermore, for permissions that are revocable (with `targetSdkVersion` 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime."
        errorLine1="        notificationManager.notify(notificationId, notification)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\NotificationManager.kt"
            line="281"
            column="9"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                issues.add(&quot;Insufficient contrast ratio: ${String.format(&quot;%.2f&quot;, contrastRatio)} &lt; ${Standards.MIN_CONTRAST_RATIO}&quot;)"
        errorLine2="                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ADHDDesignValidator.kt"
            line="134"
            column="60"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Weekly budget: $${String.format(&quot;%.2f&quot;, weeklyBudget)}"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\viewmodel\AICoachViewModel.kt"
            line="155"
            column="33"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Total Spent: $${String.format(&quot;%.2f&quot;, totalSpent)}"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="119"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Top Category: ${topCategory?.key ?: &quot;None&quot;} ($${String.format(&quot;%.2f&quot;, topCategory?.value ?: 0.0)})"
        errorLine2="                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="121"
            column="63"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                &quot;✅ Great job staying within budget! You have $${String.format(&quot;%.2f&quot;, (budget?.weeklyAmount ?: 300.0) - totalSpent)} left this week.&quot;"
        errorLine2="                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="127"
            column="65"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Weekly Budget: $${String.format(&quot;%.2f&quot;, currentBudget.weeklyAmount)}"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="175"
            column="33"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Average Spending: $${String.format(&quot;%.2f&quot;, avgSpending)}"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="176"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Total Debt: $${String.format(&quot;%.2f&quot;, totalDebt)}"
        errorLine2="                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="238"
            column="30"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Monthly Minimums: $${String.format(&quot;%.2f&quot;, totalMinPayments)}"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="239"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            • Highest Interest Rate: ${String.format(&quot;%.1f&quot;, highestRate)}%"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="240"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            Start with: ${smallestDebt?.name} ($${String.format(&quot;%.2f&quot;, smallestDebt?.balance ?: 0.0)})"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="245"
            column="51"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            Start with: ${highestRateCard?.name} (${String.format(&quot;%.1f&quot;, highestRateCard?.interestRate ?: 0.0)}% APR)"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AIService.kt"
            line="251"
            column="53"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        description = &quot;You spend significantly more on weekends (${String.format(&quot;%.2f&quot;, weekendAvg)}) compared to weekdays (${String.format(&quot;%.2f&quot;, weekdayAvg)})&quot;,"
        errorLine2="                                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AdvancedAnalyticsService.kt"
            line="390"
            column="84"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        description = &quot;You spend significantly more on weekends (${String.format(&quot;%.2f&quot;, weekendAvg)}) compared to weekdays (${String.format(&quot;%.2f&quot;, weekdayAvg)})&quot;,"
        errorLine2="                                                                                                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\AdvancedAnalyticsService.kt"
            line="390"
            column="144"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                &quot;spending_increase&quot; to &quot;Your spending in this category has increased significantly. Consider increasing your budget by ${String.format(&quot;%.0f&quot;, abs(difference))}.&quot;"
        errorLine2="                                                                                                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\BudgetRecommendationService.kt"
            line="191"
            column="138"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                &quot;spending_decrease&quot; to &quot;You&apos;ve been spending less in this category. You could reduce your budget by ${String.format(&quot;%.0f&quot;, abs(difference))}.&quot;"
        errorLine2="                                                                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\BudgetRecommendationService.kt"
            line="194"
            column="119"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;$${String.format(&quot;%.2f&quot;, safeToSpend)}&quot;,"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DashboardScreen.kt"
            line="163"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = if (totalDebt > 0) &quot;$${String.format(&quot;%.2f&quot;, totalDebt)}&quot; else &quot;No debt&quot;,"
        errorLine2="                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DashboardScreen.kt"
            line="213"
            column="55"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = if (nextPayment > 0) &quot;$${String.format(&quot;%.2f&quot;, nextPayment)}&quot; else &quot;None due&quot;,"
        errorLine2="                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DashboardScreen.kt"
            line="225"
            column="57"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                text = &quot;$${String.format(&quot;%.2f&quot;, amount)}&quot;,"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="287"
            column="28"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.2f&quot;, creditCard.currentBalance)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="362"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.2f&quot;, creditCard.creditLimit)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="375"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;${String.format(&quot;%.1f&quot;, utilization * 100)}%&quot;,"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="397"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;Min Payment: $${String.format(&quot;%.2f&quot;, creditCard.minimumPayment)}&quot;,"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="431"
            column="49"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Current Balance: $${String.format(&quot;%.2f&quot;, creditCard.currentBalance)}&quot;,"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="787"
            column="49"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        onClick = { paymentAmount = String.format(&quot;%.2f&quot;, creditCard.minimumPayment) }"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="809"
            column="53"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        onClick = { paymentAmount = String.format(&quot;%.2f&quot;, creditCard.currentBalance) }"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\DebtScreen.kt"
            line="814"
            column="53"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.0f&quot;, recommendation.currentAmount)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt"
            line="142"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.0f&quot;, recommendation.recommendedAmount)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt"
            line="162"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;${if (isOverBudget) &quot;+&quot; else &quot;&quot;}${String.format(&quot;%.0f&quot;, variance)}&quot;,"
        errorLine2="                                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt"
            line="298"
            column="67"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;${String.format(&quot;%.1f&quot;, abs(variancePercentage))}%&quot;,"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnhancedBudgetComponents.kt"
            line="304"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;You have $${String.format(&quot;%.0f&quot;, unallocatedAmount)} unallocated&quot;,"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt"
            line="244"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Allocate your $${String.format(&quot;%.0f&quot;, unallocatedAmount)} across these categories:&quot;,"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt"
            line="512"
            column="46"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Remaining: $${String.format(&quot;%.2f&quot;, remaining)}&quot;,"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt"
            line="547"
            column="43"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;$${String.format(&quot;%.0f&quot;, remaining)}&quot;,"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="123"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.0f&quot;, category.spentAmount)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="142"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.0f&quot;, category.allocatedAmount)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="147"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Over by $${String.format(&quot;%.0f&quot;, -remaining)}&quot;,"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="169"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    unallocatedAmount > 0 -> &quot;💰 You have $${String.format(&quot;%.0f&quot;, unallocatedAmount)} to allocate&quot;"
        errorLine2="                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="257"
            column="62"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    else -> &quot;⚠️ You&apos;ve over-allocated by $${String.format(&quot;%.0f&quot;, abs(unallocatedAmount))}&quot;"
        errorLine2="                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="258"
            column="61"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            text = &quot;$${String.format(&quot;%.0f&quot;, amount)}&quot;,"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="296"
            column="24"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    &quot;$${String.format(&quot;%.0f&quot;, currentIncome)} ${period.replaceFirstChar { it.lowercase() }}&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="335"
            column="25"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Available: $${String.format(&quot;%.2f&quot;, fromCategory.allocatedAmount - fromCategory.spentAmount)}&quot;,"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="474"
            column="43"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.2f&quot;, remaining)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="667"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.2f&quot;, category.allocatedAmount)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="681"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.2f&quot;, category.spentAmount)} spent&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="698"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;${String.format(&quot;%.1f&quot;, spentPercentage * 100)}%&quot;,"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="703"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;Over budget by $${String.format(&quot;%.2f&quot;, -remaining)}&quot;,"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="737"
            column="51"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        val formattedAmount = String.format(&quot;%.2f&quot;, amount)"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt"
            line="348"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return &quot;$category spending: $${String.format(&quot;%.2f&quot;, amount)} of $${String.format(&quot;%.2f&quot;, budget)} budget, $percentage percent used&quot;"
        errorLine2="                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt"
            line="359"
            column="40"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return &quot;$category spending: $${String.format(&quot;%.2f&quot;, amount)} of $${String.format(&quot;%.2f&quot;, budget)} budget, $percentage percent used&quot;"
        errorLine2="                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt"
            line="359"
            column="77"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return &quot;$cardName: $${String.format(&quot;%.2f&quot;, balance)} balance, $utilization percent utilization&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\utils\ErrorHandling.kt"
            line="364"
            column="31"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = String.format(&quot;%02d:%02d&quot;, minutes, seconds),"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\FocusTimerComponents.kt"
            line="92"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `capitalize(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            title = achievementId.replace(&quot;_&quot;, &quot; &quot;).capitalize(),"
        errorLine2="                                                    ~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\GamificationService.kt"
            line="334"
            column="53"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;You&apos;re about to spend $${String.format(&quot;%.2f&quot;, amount)} on $category&quot;,"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt"
            line="71"
            column="54"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        &quot;This purchase will put you $${String.format(&quot;%.2f&quot;, expenseAmount - remainingBudget)} over budget.&quot;"
        errorLine2="                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt"
            line="284"
            column="56"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        &quot;You only have $${String.format(&quot;%.2f&quot;, remainingBudget)} left in your budget.&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt"
            line="286"
            column="43"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    subtitle = &quot;Trigger impulse control for purchases over $${String.format(&quot;%.0f&quot;, uiState.spendingThreshold)}&quot;,"
        errorLine2="                                                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlSettingsCard.kt"
            line="93"
            column="79"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                &quot;You&apos;ve successfully tracked $${String.format(&quot;%.2f&quot;, amount)} in expenses this $period!&quot;"
        errorLine2="                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\data\repository\NotificationRepository.kt"
            line="115"
            column="49"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        String.format(&quot;$%.2f&quot;, expense.amount)"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt"
            line="63"
            column="9"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = String.format(&quot;$%.0f left&quot;, remaining),"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt"
            line="179"
            column="28"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                text = String.format(&quot;$%.0f of $%.0f spent&quot;, category.spentAmount, category.allocatedAmount),"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt"
            line="197"
            column="24"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = String.format(&quot;$%.0f&quot;, creditCard.currentBalance),"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt"
            line="278"
            column="28"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = String.format(&quot;%.1f%% used&quot;, utilization * 100f),"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt"
            line="301"
            column="28"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = String.format(&quot;$%.0f available&quot;, availableCredit),"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\OptimizedComponents.kt"
            line="306"
            column="28"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    value = &quot;$${String.format(&quot;%.2f&quot;, payoffPlan.totalInterestPaid)}&quot;,"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt"
            line="861"
            column="33"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    value = &quot;$${String.format(&quot;%.2f&quot;, payoffPlan.totalPayments)}&quot;,"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt"
            line="866"
            column="33"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;Interest: $${String.format(&quot;%.2f&quot;, step.interestPaid)}&quot;,"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt"
            line="1000"
            column="46"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;$${String.format(&quot;%.2f&quot;, step.payment)}&quot;,"
        errorLine2="                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt"
            line="1011"
            column="32"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Balance: $${String.format(&quot;%.2f&quot;, step.remainingBalance)}&quot;,"
        errorLine2="                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PayoffPlannerScreen.kt"
            line="1017"
            column="41"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                            description = &quot;You spend an average of $${String.format(&quot;%.2f&quot;, avgAmount)} on $category&quot;,"
        errorLine2="                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\PerformanceOptimizationService.kt"
            line="99"
            column="71"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;${String.format(&quot;%.1f&quot;, spentPercentage * 100)}%&quot;,"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt"
            line="180"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="            text = &quot;$${String.format(&quot;%.0f&quot;, amount)}&quot;,"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt"
            line="254"
            column="24"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.2f&quot;, budgetCategory.spentAmount)} of $${String.format(&quot;%.2f&quot;, budgetCategory.allocatedAmount)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt"
            line="326"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;$${String.format(&quot;%.2f&quot;, budgetCategory.spentAmount)} of $${String.format(&quot;%.2f&quot;, budgetCategory.allocatedAmount)}&quot;,"
        errorLine2="                                                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt"
            line="326"
            column="93"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        text = &quot;${String.format(&quot;%.1f&quot;, spentPercentage * 100)}%&quot;,"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt"
            line="330"
            column="35"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                    text = &quot;Over budget by $${String.format(&quot;%.2f&quot;, -remaining)}&quot;,"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt"
            line="358"
            column="47"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                        &quot;Current scale: ${String.format(&quot;%.1f&quot;, currentScale)}&quot;"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ThemeSettingsComponents.kt"
            line="396"
            column="43"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                text = &quot;${if (isOverBudget) &quot;+&quot; else &quot;&quot;}${String.format(&quot;%.1f&quot;, variancePercentage)}%&quot;,"
        errorLine2="                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt"
            line="293"
            column="59"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                text = &quot;$${String.format(&quot;%.0f&quot;, actualAmount)}&quot;,"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt"
            line="331"
            column="28"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                text = &quot;Budget: $${String.format(&quot;%.0f&quot;, budgetAmount)}&quot;,"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt"
            line="337"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="                text = &quot;$${String.format(&quot;%.0f&quot;, category.amount)} ($percentage%)&quot;,"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt"
            line="369"
            column="28"/>
    </issue>

    <issue
        id="Instantiatable"
        severity="Fatal"
        message="`NotificationService` must extend android.app.Service"
        category="Correctness"
        priority="6"
        summary="Registered class is not instantiatable"
        explanation="Activities, services, broadcast receivers etc. registered in the manifest file (or for custom views, in a layout file) must be &quot;instantiatable&quot; by the system, which means that the class must be public, it must have an empty public constructor, and if it&apos;s an inner class, it must be a static inner class.&#xA;&#xA;If you use a custom `AppComponentFactory` to instantiate app components yourself, consider disabling this Lint issue in order to avoid false positives."
        errorLine1="            android:name=&quot;.service.NotificationService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml"
            line="69"
            column="27"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="Call requires API level 34 (current min is 24): `java.util.regex.Matcher#replaceAll`"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        masked = CREDIT_CARD_PATTERN.matcher(masked).replaceAll { matchResult ->"
        errorLine2="                                                     ~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\DataProtectionService.kt"
            line="119"
            column="54"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="Call requires API level 34 (current min is 24): `java.util.regex.Matcher#replaceAll`"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        masked = BANK_ACCOUNT_PATTERN.matcher(masked).replaceAll { matchResult ->"
        errorLine2="                                                      ~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\DataProtectionService.kt"
            line="132"
            column="55"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application or sdk runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="16"
            column="9"/>
    </issue>

    <issue
        id="ScheduleExactAlarm"
        severity="Error"
        message="When scheduling exact alarms, apps should explicitly call `AlarmManager#canScheduleExactAlarms` or handle `SecurityException`s"
        category="Correctness"
        priority="6"
        summary="Scheduling Exact Alarms Without Required Permission"
        explanation="Applications looking to schedule exact alarms should ensure that the `SCHEDULE_EXACT_ALARM` permission is granted by calling the `AlarmManager#canScheduleExactAlarms` API before attempting to set an exact alarm. If the permission is not granted to your application, please consider requesting it from the user by starting the `ACTION_REQUEST_SCHEDULE_EXACT_ALARM` intent or gracefully falling back to another option."
        url="https://developer.android.com/training/scheduling/alarms#exact"
        urls="https://developer.android.com/training/scheduling/alarms#exact"
        errorLine1="                alarmManager.setExactAndAllowWhileIdle("
        errorLine2="                ^">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\service\NotificationManager.kt"
            line="155"
            column="17"/>
    </issue>

    <issue
        id="RedundantLabel"
        severity="Warning"
        message="Redundant label can be removed"
        category="Correctness"
        priority="5"
        summary="Redundant label on activity"
        explanation="When an activity does not have a label attribute, it will use the one from the application tag. Since the application has already specified the same label, the label on this activity can be omitted."
        errorLine1="            android:label=&quot;@string/app_name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="SelectedPhotoAccess"
        severity="Warning"
        message="Your app is currently not handling Selected Photos Access introduced in Android 14+"
        category="Correctness"
        priority="5"
        summary="Behavior change when requesting photo library access"
        explanation="Selected Photo Access is a new ability for users to share partial access to their photo library when apps request access to their device storage on Android 14+.&#xA;&#xA;Instead of letting the system manage the selection lifecycle, we recommend you adapt your app to handle partial access to the photo library."
        url="https://developer.android.com/about/versions/14/changes/partial-photo-video-access"
        urls="https://developer.android.com/about/versions/14/changes/partial-photo-video-access"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_MEDIA_IMAGES&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml"
            line="18"
            column="36"/>
    </issue>

    <issue
        id="SelectedPhotoAccess"
        severity="Warning"
        message="Your app is currently not handling Selected Photos Access introduced in Android 14+"
        category="Correctness"
        priority="5"
        summary="Behavior change when requesting photo library access"
        explanation="Selected Photo Access is a new ability for users to share partial access to their photo library when apps request access to their device storage on Android 14+.&#xA;&#xA;Instead of letting the system manage the selection lifecycle, we recommend you adapt your app to handle partial access to the photo library."
        url="https://developer.android.com/about/versions/14/changes/partial-photo-video-access"
        urls="https://developer.android.com/about/versions/14/changes/partial-photo-video-access"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_MEDIA_VIDEO&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml"
            line="19"
            column="36"/>
    </issue>

    <issue
        id="AndroidGradlePluginVersion"
        severity="Warning"
        message="A newer version of Gradle than 8.12 is available: 8.14.2"
        category="Correctness"
        priority="4"
        summary="Obsolete Android Gradle Plugin Version"
        explanation="This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="distributionUrl=https\://services.gradle.org/distributions/gradle-8.12-bin.zip"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\gradle\wrapper\gradle-wrapper.properties"
            line="3"
            column="17"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of `compileSdkVersion` than 34 is available: 35"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    compileSdk 34"
        errorLine2="    ~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="11"
            column="5"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="116"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-runtime-ktx:2.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="117"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.activity:activity-compose:1.8.2&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="118"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation platform(&apos;androidx.compose:compose-bom:2023.10.01&apos;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="121"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.5 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.navigation:navigation-compose:2.7.5&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="129"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="132"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-runtime-compose:2.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="133"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.hilt:hilt-navigation-compose:1.1.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="137"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.room:room-runtime:2.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="141"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.room:room-ktx:2.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="142"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    kapt &apos;androidx.room:room-compiler:2.6.1&apos;"
        errorLine2="         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="143"
            column="10"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-beta01"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.security:security-crypto:1.1.0-alpha06&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="163"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.work:work-runtime-ktx:2.9.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="166"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.hilt:hilt-work:1.1.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="167"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="173"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    coreLibraryDesugaring &apos;com.android.tools:desugar_jdk_libs:2.0.4&apos;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="184"
            column="27"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test:core than 1.5.0 is available: 1.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    testImplementation &apos;androidx.test:core:1.5.0&apos;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="188"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    testImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="189"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.room:room-testing than 2.6.1 is available: 2.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    testImplementation &apos;androidx.room:room-testing:2.6.1&apos;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="190"
            column="24"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="196"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="197"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation platform(&apos;androidx.compose:compose-bom:2023.10.01&apos;)"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="198"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.navigation:navigation-testing than 2.7.5 is available: 2.9.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.navigation:navigation-testing:2.7.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="200"
            column="31"/>
    </issue>

    <issue
        id="ComposableModifierFactory"
        severity="Warning"
        message="Modifier factory functions should not be marked as @Composable, and should use composed instead"
        category="Correctness"
        priority="3"
        summary="Modifier factory functions should not be @Composable"
        explanation="Modifier factory functions that need to be aware of the composition should use androidx.compose.ui.composed {} in their implementation instead of being marked as @Composable. This allows Modifiers to be referenced in top level variables and constructed outside of the composition."
        errorLine1="    fun getCardModifier(): Modifier {"
        errorLine2="        ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\responsive\ResponsiveLayoutManager.kt"
            line="217"
            column="9"/>
    </issue>

    <issue
        id="ModifierFactoryExtensionFunction"
        severity="Warning"
        message="Modifier factory functions should be extensions on Modifier"
        category="Correctness"
        priority="3"
        summary="Modifier factory functions should be extensions on Modifier"
        explanation="Modifier factory functions should be defined as extension functions on Modifier to allow modifiers to be fluently chained."
        errorLine1="    fun getCardModifier(): Modifier {"
        errorLine2="        ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\responsive\ResponsiveLayoutManager.kt"
            line="217"
            column="9"/>
    </issue>

    <issue
        id="ModifierParameter"
        severity="Warning"
        message="Modifier parameter should be the first optional parameter"
        category="Correctness"
        priority="3"
        summary="Guidelines for Modifier parameters in a Composable function"
        explanation="The first (or only) Modifier parameter in a Composable function should follow the following rules:&#xA;- Be named `modifier`&#xA;- Have a type of `Modifier`&#xA;- Either have no default value, or have a default value of `Modifier`&#xA;- If optional, be the first optional parameter in the parameter list"
        errorLine1="    modifier: Modifier = Modifier,"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\DecisionSupportComponents.kt"
            line="29"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        severity="Warning"
        message="Modifier parameter should be the first optional parameter"
        category="Correctness"
        priority="3"
        summary="Guidelines for Modifier parameters in a Composable function"
        explanation="The first (or only) Modifier parameter in a Composable function should follow the following rules:&#xA;- Be named `modifier`&#xA;- Have a type of `Modifier`&#xA;- Either have no default value, or have a default value of `Modifier`&#xA;- If optional, be the first optional parameter in the parameter list"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\FocusTimerComponents.kt"
            line="41"
            column="5"/>
    </issue>

    <issue
        id="ModifierParameter"
        severity="Warning"
        message="Modifier parameter should be the first optional parameter"
        category="Correctness"
        priority="3"
        summary="Guidelines for Modifier parameters in a Composable function"
        explanation="The first (or only) Modifier parameter in a Composable function should follow the following rules:&#xA;- Be named `modifier`&#xA;- Have a type of `Modifier`&#xA;- Either have no default value, or have a default value of `Modifier`&#xA;- If optional, be the first optional parameter in the parameter list"
        errorLine1="    modifier: Modifier = Modifier"
        errorLine2="    ~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlSettingsCard.kt"
            line="23"
            column="5"/>
    </issue>

    <issue
        id="MutableCollectionMutableState"
        severity="Warning"
        message="Creating a MutableState object with a mutable collection type"
        category="Correctness"
        priority="3"
        summary="Creating a MutableState object with a mutable collection type"
        explanation="Writes to mutable collections inside a MutableState will not cause a recomposition - only writes to the MutableState itself will. In most cases you should either use a read-only collection (such as List or Map) and assign a new instance to the MutableState when your data changes, or you can use an snapshot-backed collection such as SnapshotStateList or SnapshotStateMap which will correctly cause a recomposition when their contents are modified."
        errorLine1="        mutableStateOf("
        errorLine2="        ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt"
            line="493"
            column="9"/>
    </issue>

    <issue
        id="MutableCollectionMutableState"
        severity="Warning"
        message="Creating a MutableState object with a mutable collection type"
        category="Correctness"
        priority="3"
        summary="Creating a MutableState object with a mutable collection type"
        explanation="Writes to mutable collections inside a MutableState will not cause a recomposition - only writes to the MutableState itself will. In most cases you should either use a read-only collection (such as List or Map) and assign a new instance to the MutableState when your data changes, or you can use an snapshot-backed collection such as SnapshotStateList or SnapshotStateMap which will correctly cause a recomposition when their contents are modified."
        errorLine1="    var selectedCategories by remember { mutableStateOf(DefaultBudgetCategories.categories.toMap().toMutableMap()) }"
        errorLine2="                                         ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\PlaceholderScreens.kt"
            line="407"
            column="42"/>
    </issue>

    <issue
        id="DiscouragedApi"
        severity="Warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        category="Correctness"
        priority="2"
        summary="Using discouraged APIs"
        explanation="Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior)."
        errorLine1="            android:screenOrientation=&quot;unspecified&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        severity="Warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        category="Correctness"
        priority="2"
        summary="Using discouraged APIs"
        explanation="Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior)."
        errorLine1="            android:screenOrientation=&quot;unspecified&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml"
            line="65"
            column="13"/>
    </issue>

    <issue
        id="KaptUsageInsteadOfKsp"
        severity="Warning"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp"
        category="Performance"
        priority="4"
        summary="Kapt usage should be replaced with KSP"
        explanation="KSP is a more efficient replacement for kapt. For libraries that support both, KSP should be used to improve build times."
        url="https://developer.android.com/studio/build/migrate-to-ksp"
        urls="https://developer.android.com/studio/build/migrate-to-ksp"
        errorLine1="    kapt &apos;androidx.room:room-compiler:2.6.1&apos;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\build.gradle"
            line="143"
            column="5"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        severity="Hint"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        category="Performance"
        priority="3"
        summary="`State&lt;T>` will autobox values assigned to this state. Use a specialized state type instead."
        explanation="Calling `mutableStateOf&lt;T>()` when `T` is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for `Int`, `Long`, `Float`, and `Double` when the state does not need to track null values and does not override the default `SnapshotMutationPolicy`."
        errorLine1="    var timeRemaining by remember { mutableStateOf(10) }"
        errorLine2="                                    ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt"
            line="27"
            column="37"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        severity="Hint"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        category="Performance"
        priority="3"
        summary="`State&lt;T>` will autobox values assigned to this state. Use a specialized state type instead."
        explanation="Calling `mutableStateOf&lt;T>()` when `T` is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for `Int`, `Long`, `Float`, and `Double` when the state does not need to track null values and does not override the default `SnapshotMutationPolicy`."
        errorLine1="    var secondsRemaining by remember { mutableStateOf(4) }"
        errorLine2="                                       ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt"
            line="469"
            column="40"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        severity="Hint"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        category="Performance"
        priority="3"
        summary="`State&lt;T>` will autobox values assigned to this state. Use a specialized state type instead."
        explanation="Calling `mutableStateOf&lt;T>()` when `T` is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for `Int`, `Long`, `Float`, and `Double` when the state does not need to track null values and does not override the default `SnapshotMutationPolicy`."
        errorLine1="    var cycleCount by remember { mutableStateOf(0) }"
        errorLine2="                                 ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlComponents.kt"
            line="470"
            column="34"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        severity="Hint"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`"
        category="Performance"
        priority="3"
        summary="`State&lt;T>` will autobox values assigned to this state. Use a specialized state type instead."
        explanation="Calling `mutableStateOf&lt;T>()` when `T` is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for `Int`, `Long`, `Float`, and `Double` when the state does not need to track null values and does not override the default `SnapshotMutationPolicy`."
        errorLine1="    var selectedPeriod by remember { mutableStateOf(currentPeriod) }"
        errorLine2="                                     ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\ImpulseControlSettingsCard.kt"
            line="335"
            column="38"/>
    </issue>

    <issue
        id="AutoboxingStateCreation"
        severity="Hint"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`"
        category="Performance"
        priority="3"
        summary="`State&lt;T>` will autobox values assigned to this state. Use a specialized state type instead."
        explanation="Calling `mutableStateOf&lt;T>()` when `T` is either backed by a primitive type on the JVM or is a value class results in a state implementation that requires all state values to be boxed. This usually causes an additional allocation for each state write, and adds some additional work to auto-unbox values when reading the value of the state. Instead, prefer to use a specialized primitive state implementation for `Int`, `Long`, `Float`, and `Double` when the state does not need to track null values and does not override the default `SnapshotMutationPolicy`."
        errorLine1="    var animationProgress by remember { mutableStateOf(if (animateEntry) 0f else 1f) }"
        errorLine2="                                        ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\VisualAnalyticsComponents.kt"
            line="32"
            column="41"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_500` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_200&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.teal_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.focus_orange` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;focus_orange&quot;>#FFFF9800&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.black` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;black&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_50` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_50&quot;>#FFFAFAFA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_100` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_100&quot;>#FFF5F5F5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_200&quot;>#FFEEEEEE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_300` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_300&quot;>#FFE0E0E0&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_400` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_400&quot;>#FFBDBDBD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="25"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_500` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_500&quot;>#FF9E9E9E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_600` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_600&quot;>#FF757575&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="27"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_700&quot;>#FF616161&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_800` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_800&quot;>#FF424242&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.gray_900` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;gray_900&quot;>#FF212121&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.text_primary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;text_primary&quot;>#FF212121&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.text_secondary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;text_secondary&quot;>#FF757575&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="36"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.surface_light` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;surface_light&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="37"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.on_surface_light` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;on_surface_light&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="38"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.dark_background_soft` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;dark_background_soft&quot;>#FF1E1E1E&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="42"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.dark_on_surface` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;dark_on_surface&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="44"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.dark_text_primary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;dark_text_primary&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="45"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.dark_text_secondary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;dark_text_secondary&quot;>#FFBDBDBD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="46"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_primary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_primary&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="49"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_accent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_accent&quot;>#FFFF0000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="50"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_text` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_text&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="51"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_surface` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_surface&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="52"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_dark_primary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_dark_primary&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="55"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_dark_accent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_dark_accent&quot;>#FFFF4444&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="56"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_dark_text` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_dark_text&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="57"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_dark_surface` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_dark_surface&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="58"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.high_contrast_dark_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;high_contrast_dark_background&quot;>#FF000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="59"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.adhd_calm_blue` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;adhd_calm_blue&quot;>#FF81C784&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="62"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.adhd_focus_green` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;adhd_focus_green&quot;>#FF66BB6A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="63"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.adhd_warning_amber` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;adhd_warning_amber&quot;>#FFFFB74D&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="64"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.adhd_error_red` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;adhd_error_red&quot;>#FFE57373&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="65"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.adhd_success_green` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;adhd_success_green&quot;>#FF81C784&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\colors.xml"
            line="66"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_headline` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_headline&quot;>24sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_title&quot;>20sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_body` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_body&quot;>16sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_caption` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_caption&quot;>14sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="7"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_small` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_small&quot;>12sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_headline_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_headline_large&quot;>28sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_title_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_title_large&quot;>24sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="12"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_body_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_body_large&quot;>18sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_caption_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_caption_large&quot;>16sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_headline_xl` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_headline_xl&quot;>32sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="17"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_title_xl` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_title_xl&quot;>28sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_body_xl` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_body_xl&quot;>22sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.text_size_caption_xl` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;text_size_caption_xl&quot;>20sp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="20"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.spacing_xs` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;spacing_xs&quot;>4dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.spacing_sm` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;spacing_sm&quot;>8dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.spacing_md` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;spacing_md&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="25"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.spacing_lg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;spacing_lg&quot;>24dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.spacing_xl` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;spacing_xl&quot;>32dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="27"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.touch_target_min` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;touch_target_min&quot;>48dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.touch_target_comfortable` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;touch_target_comfortable&quot;>56dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.touch_target_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;touch_target_large&quot;>64dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="32"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.card_corner_radius` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;card_corner_radius&quot;>12dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="35"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.card_elevation` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;card_elevation&quot;>4dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="36"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.button_corner_radius` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;button_corner_radius&quot;>8dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="37"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.focus_border_width` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;focus_border_width&quot;>3dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="40"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.focus_corner_radius` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;focus_corner_radius&quot;>8dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\dimens.xml"
            line="41"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\drawable\ic_launcher_foreground.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.welcome_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;welcome_title&quot;>Welcome to FocusFlow&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml"
            line="3"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.welcome_subtitle` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;welcome_subtitle&quot;>Your ADHD-friendly companion for better focus and productivity&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.get_started` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;get_started&quot;>Get Started&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml"
            line="5"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.notification_channel_name` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;notification_channel_name&quot;>FocusFlow Notifications&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml"
            line="6"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.notification_channel_description` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;notification_channel_description&quot;>Notifications for tasks, reminders and focus sessions&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml"
            line="7"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.default_notification_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;default_notification_title&quot;>FocusFlow Reminder&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.default_notification_message` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;default_notification_message&quot;>Time to check your tasks!&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\res\values\strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                                    Color(android.graphics.Color.parseColor(color)),"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt"
            line="397"
            column="43"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                                    Color(android.graphics.Color.parseColor(color)),"
        errorLine2="                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\screens\EnhancedBudgetScreen.kt"
            line="431"
            column="43"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        Color(android.graphics.Color.parseColor(category.categoryColor))"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="71"
            column="15"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `String.toColorInt` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="        Color(android.graphics.Color.parseColor(category.categoryColor))"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\ui\components\EnvelopeBudgetComponents.kt"
            line="581"
            column="15"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                encryptedPrefs.edit().putString(key, value).apply()"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\SecurityManager.kt"
            line="153"
            column="17"/>
    </issue>

    <issue
        id="UseKtx"
        severity="Warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?"
        category="Productivity"
        priority="6"
        summary="Use KTX extension function"
        explanation="The Android KTX libraries decorates the Android platform SDK as well as various libraries with more convenient extension functions available from Kotlin, allowing you to use default parameters, named parameters, and more."
        errorLine1="                encryptedPrefs.edit().clear().apply()"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\java\com\focusflow\security\SecurityManager.kt"
            line="276"
            column="17"/>
    </issue>

</issues>
