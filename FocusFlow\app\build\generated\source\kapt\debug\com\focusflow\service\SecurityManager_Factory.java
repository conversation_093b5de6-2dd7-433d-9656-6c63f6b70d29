package com.focusflow.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SecurityManager_Factory implements Factory<SecurityManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CrashReportingManager> crashReportingManagerProvider;

  public SecurityManager_Factory(Provider<Context> contextProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    this.contextProvider = contextProvider;
    this.crashReportingManagerProvider = crashReportingManagerProvider;
  }

  @Override
  public SecurityManager get() {
    return newInstance(contextProvider.get(), crashReportingManagerProvider.get());
  }

  public static SecurityManager_Factory create(Provider<Context> contextProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    return new SecurityManager_Factory(contextProvider, crashReportingManagerProvider);
  }

  public static SecurityManager newInstance(Context context,
      CrashReportingManager crashReportingManager) {
    return new SecurityManager(context, crashReportingManager);
  }
}
