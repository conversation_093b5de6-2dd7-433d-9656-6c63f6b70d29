package com.focusflow.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.ui.viewmodel.ImpulseControlViewModel
import kotlinx.coroutines.launch

@Composable
fun EnhancedAddExpenseDialog(
    onDismiss: () -> Unit,
    onAddExpense: (Double, String, String, String?) -> Unit,
    impulseControlViewModel: ImpulseControlViewModel = hiltViewModel()
) {
    val impulseControlState by impulseControlViewModel.uiState.collectAsStateWithLifecycle()
    val scope = rememberCoroutineScope()
    
    var amount by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var merchant by remember { mutableStateOf("") }
    
    var showImpulseControl by remember { mutableStateOf(false) }
    var budgetImpact by remember { mutableStateOf<com.focusflow.ui.viewmodel.BudgetImpactPreview?>(null) }
    var impulseControlProcessed by remember { mutableStateOf(false) }

    val categories = listOf(
        "Groceries", "Entertainment", "Transportation", "Dining Out", 
        "Shopping", "Utilities", "Healthcare", "Education", "Other"
    )

    // Check for impulse control when amount or category changes
    LaunchedEffect(amount, selectedCategory) {
        val amountDouble = amount.toDoubleOrNull()
        if (amountDouble != null && amountDouble > 0 && !impulseControlProcessed) {
            val shouldShow = impulseControlViewModel.shouldShowImpulseControl(amountDouble, selectedCategory)
            if (shouldShow && impulseControlState.impulseControlEnabled) {
                budgetImpact = impulseControlViewModel.getBudgetImpactPreview(amountDouble, selectedCategory)
                showImpulseControl = true
            }
        }
    }

    if (showImpulseControl && !impulseControlProcessed) {
        ImpulseControlDialog(
            amount = amount.toDoubleOrNull() ?: 0.0,
            category = selectedCategory.ifBlank { "Unknown" },
            budgetImpact = budgetImpact,
            onConfirm = {
                impulseControlProcessed = true
                showImpulseControl = false
                // Proceed with expense entry
                val amountDouble = amount.toDoubleOrNull()
                if (amountDouble != null && amountDouble > 0 && selectedCategory.isNotBlank() && description.isNotBlank()) {
                    onAddExpense(
                        amountDouble,
                        selectedCategory,
                        description,
                        merchant.takeIf { it.isNotBlank() }
                    )
                }
            },
            onCancel = {
                impulseControlProcessed = true
                showImpulseControl = false
                onDismiss()
            },
            onAddToWishlist = {
                // Add to wishlist when user chooses to delay
                scope.launch {
                    impulseControlViewModel.addItemToWishlist(
                        itemName = description.ifBlank { "Purchase from $merchant" },
                        estimatedPrice = amount.toDoubleOrNull() ?: 0.0,
                        category = selectedCategory,
                        description = description.takeIf { it.isNotBlank() },
                        merchant = merchant.takeIf { it.isNotBlank() }
                    )
                }
                impulseControlProcessed = true
                showImpulseControl = false
                onDismiss()
            },
            onDelay = {
                // 10-second cooling off period
                impulseControlProcessed = true
                showImpulseControl = false
                onDismiss()
            }
        )
    } else {
        // Regular expense dialog
        AlertDialog(
            onDismissRequest = onDismiss,
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.ShoppingCart,
                        contentDescription = null,
                        tint = MaterialTheme.colors.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Add Expense",
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            text = {
                Column {
                    // Amount input
                    OutlinedTextField(
                        value = amount,
                        onValueChange = { 
                            amount = it
                            impulseControlProcessed = false // Reset when amount changes
                        },
                        label = { Text("Amount") },
                        placeholder = { Text("0.00") },
                        leadingIcon = { Text("$") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Category dropdown
                    var expanded by remember { mutableStateOf(false) }
                    
                    Box {
                        OutlinedTextField(
                            value = selectedCategory,
                            onValueChange = { 
                                selectedCategory = it
                                impulseControlProcessed = false // Reset when category changes
                            },
                            label = { Text("Category") },
                            trailingIcon = {
                                IconButton(onClick = { expanded = !expanded }) {
                                    Icon(
                                        if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                        contentDescription = "Dropdown"
                                    )
                                }
                            },
                            modifier = Modifier.fillMaxWidth(),
                            readOnly = true
                        )
                        
                        DropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            categories.forEach { category ->
                                DropdownMenuItem(
                                    onClick = {
                                        selectedCategory = category
                                        expanded = false
                                        impulseControlProcessed = false
                                    }
                                ) {
                                    Text(category)
                                }
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Description input
                    OutlinedTextField(
                        value = description,
                        onValueChange = { description = it },
                        label = { Text("Description") },
                        placeholder = { Text("What did you buy?") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Merchant input (optional)
                    OutlinedTextField(
                        value = merchant,
                        onValueChange = { merchant = it },
                        label = { Text("Merchant (Optional)") },
                        placeholder = { Text("Where did you shop?") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )

                    // Show impulse control status if enabled
                    if (impulseControlState.impulseControlEnabled) {
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        val amountDouble = amount.toDoubleOrNull()
                        if (amountDouble != null && amountDouble >= impulseControlState.spendingThreshold) {
                            Card(
                                backgroundColor = Color(0xFFFFF3E0),
                                elevation = 2.dp,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                Row(
                                    modifier = Modifier.padding(8.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        Icons.Default.Info,
                                        contentDescription = null,
                                        tint = Color(0xFFFF9800),
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "This purchase will trigger impulse control",
                                        style = MaterialTheme.typography.caption,
                                        color = Color(0xFFFF9800)
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        val amountDouble = amount.toDoubleOrNull()
                        if (amountDouble != null && amountDouble > 0 && selectedCategory.isNotBlank() && description.isNotBlank()) {
                            // Check if impulse control should be triggered
                            if (impulseControlState.impulseControlEnabled && 
                                impulseControlViewModel.shouldShowImpulseControl(amountDouble, selectedCategory) &&
                                !impulseControlProcessed) {
                                // Trigger impulse control
                                scope.launch {
                                    budgetImpact = impulseControlViewModel.getBudgetImpactPreview(amountDouble, selectedCategory)
                                    showImpulseControl = true
                                }
                            } else {
                                // Add expense directly
                                onAddExpense(
                                    amountDouble,
                                    selectedCategory,
                                    description,
                                    merchant.takeIf { it.isNotBlank() }
                                )
                            }
                        }
                    },
                    enabled = amount.toDoubleOrNull() != null && 
                             (amount.toDoubleOrNull() ?: 0.0) > 0 && 
                             selectedCategory.isNotBlank() && 
                             description.isNotBlank()
                ) {
                    Text("Add Expense")
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text("Cancel")
                }
            }
        )
    }

    // Handle success/error messages
    impulseControlState.error?.let { error ->
        LaunchedEffect(error) {
            // TODO: Show snackbar with error
            impulseControlViewModel.clearError()
        }
    }
}
