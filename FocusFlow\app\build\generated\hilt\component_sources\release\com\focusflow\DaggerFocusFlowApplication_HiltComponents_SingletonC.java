package com.focusflow;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.hilt.work.HiltWrapper_WorkerFactoryModule;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.focusflow.data.dao.AchievementDao;
import com.focusflow.data.dao.BudgetCategoryDao;
import com.focusflow.data.dao.BudgetRecommendationDao;
import com.focusflow.data.dao.CreditCardDao;
import com.focusflow.data.dao.ExpenseDao;
import com.focusflow.data.dao.HabitLogDao;
import com.focusflow.data.dao.PaymentScheduleDao;
import com.focusflow.data.dao.PayoffMilestoneDao;
import com.focusflow.data.dao.PayoffPlanDao;
import com.focusflow.data.dao.TaskDao;
import com.focusflow.data.dao.UserPreferencesDao;
import com.focusflow.data.dao.UserStatsDao;
import com.focusflow.data.dao.VirtualPetDao;
import com.focusflow.data.dao.WishlistItemDao;
import com.focusflow.data.database.FocusFlowDatabase;
import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.BudgetRecommendationRepository;
import com.focusflow.data.repository.CreditCardRepository;
import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.HabitRepository;
import com.focusflow.data.repository.NotificationRepository;
import com.focusflow.data.repository.OptimizedExpenseRepository;
import com.focusflow.data.repository.PayoffPlanRepository;
import com.focusflow.data.repository.TaskRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.data.repository.VirtualPetRepository;
import com.focusflow.data.repository.WishlistRepository;
import com.focusflow.di.DatabaseModule;
import com.focusflow.di.DatabaseModule_ProvideAchievementDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideBudgetCategoryDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideBudgetRecommendationDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideCreditCardDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideDatabaseFactory;
import com.focusflow.di.DatabaseModule_ProvideExpenseDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideHabitLogDaoFactory;
import com.focusflow.di.DatabaseModule_ProvidePaymentScheduleDaoFactory;
import com.focusflow.di.DatabaseModule_ProvidePayoffMilestoneDaoFactory;
import com.focusflow.di.DatabaseModule_ProvidePayoffPlanDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideTaskDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideUserPreferencesDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideUserStatsDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideVirtualPetDaoFactory;
import com.focusflow.di.DatabaseModule_ProvideWishlistItemDaoFactory;
import com.focusflow.service.BudgetRecommendationService;
import com.focusflow.service.FocusFlowNotificationManager;
import com.focusflow.service.GamificationService;
import com.focusflow.service.MockAIService;
import com.focusflow.service.NotificationService;
import com.focusflow.service.PurchaseDelayService;
import com.focusflow.service.SampleDataService;
import com.focusflow.ui.viewmodel.AICoachViewModel;
import com.focusflow.ui.viewmodel.AICoachViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.BudgetViewModel;
import com.focusflow.ui.viewmodel.BudgetViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.DashboardViewModel;
import com.focusflow.ui.viewmodel.DashboardViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.DebtViewModel;
import com.focusflow.ui.viewmodel.DebtViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.EnhancedBudgetViewModel;
import com.focusflow.ui.viewmodel.EnhancedBudgetViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.ExpenseViewModel;
import com.focusflow.ui.viewmodel.ExpenseViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.HabitViewModel;
import com.focusflow.ui.viewmodel.HabitViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.ImpulseControlViewModel;
import com.focusflow.ui.viewmodel.ImpulseControlViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.MainViewModel;
import com.focusflow.ui.viewmodel.MainViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.OnboardingViewModel;
import com.focusflow.ui.viewmodel.OnboardingViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.PayoffPlannerViewModel;
import com.focusflow.ui.viewmodel.PayoffPlannerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.SettingsViewModel;
import com.focusflow.ui.viewmodel.SettingsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.TaskViewModel;
import com.focusflow.ui.viewmodel.TaskViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.VirtualPetViewModel;
import com.focusflow.ui.viewmodel.VirtualPetViewModel_HiltModules_KeyModule_ProvideFactory;
import com.focusflow.ui.viewmodel.ZeroBudgetViewModel;
import com.focusflow.ui.viewmodel.ZeroBudgetViewModel_HiltModules_KeyModule_ProvideFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.SetBuilder;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerFocusFlowApplication_HiltComponents_SingletonC {
  private DaggerFocusFlowApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder databaseModule(DatabaseModule databaseModule) {
      Preconditions.checkNotNull(databaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_WorkerFactoryModule(
        HiltWrapper_WorkerFactoryModule hiltWrapper_WorkerFactoryModule) {
      Preconditions.checkNotNull(hiltWrapper_WorkerFactoryModule);
      return this;
    }

    public FocusFlowApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements FocusFlowApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public FocusFlowApplication_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements FocusFlowApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public FocusFlowApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements FocusFlowApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public FocusFlowApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements FocusFlowApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public FocusFlowApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements FocusFlowApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public FocusFlowApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements FocusFlowApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public FocusFlowApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements FocusFlowApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public FocusFlowApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends FocusFlowApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends FocusFlowApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends FocusFlowApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends FocusFlowApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return SetBuilder.<String>newSetBuilder(15).add(AICoachViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(BudgetViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(DashboardViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(DebtViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(EnhancedBudgetViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ExpenseViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(HabitViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ImpulseControlViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(MainViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(OnboardingViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(PayoffPlannerViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SettingsViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(TaskViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(VirtualPetViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(ZeroBudgetViewModel_HiltModules_KeyModule_ProvideFactory.provide()).build();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends FocusFlowApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<AICoachViewModel> aICoachViewModelProvider;

    private Provider<BudgetViewModel> budgetViewModelProvider;

    private Provider<DashboardViewModel> dashboardViewModelProvider;

    private Provider<DebtViewModel> debtViewModelProvider;

    private Provider<EnhancedBudgetViewModel> enhancedBudgetViewModelProvider;

    private Provider<ExpenseViewModel> expenseViewModelProvider;

    private Provider<HabitViewModel> habitViewModelProvider;

    private Provider<ImpulseControlViewModel> impulseControlViewModelProvider;

    private Provider<MainViewModel> mainViewModelProvider;

    private Provider<OnboardingViewModel> onboardingViewModelProvider;

    private Provider<PayoffPlannerViewModel> payoffPlannerViewModelProvider;

    private Provider<SettingsViewModel> settingsViewModelProvider;

    private Provider<TaskViewModel> taskViewModelProvider;

    private Provider<VirtualPetViewModel> virtualPetViewModelProvider;

    private Provider<ZeroBudgetViewModel> zeroBudgetViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.aICoachViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.budgetViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.dashboardViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.debtViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.enhancedBudgetViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.expenseViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.habitViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.impulseControlViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.mainViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.onboardingViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
      this.payoffPlannerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 10);
      this.settingsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 11);
      this.taskViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 12);
      this.virtualPetViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 13);
      this.zeroBudgetViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 14);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return MapBuilder.<String, Provider<ViewModel>>newMapBuilder(15).put("com.focusflow.ui.viewmodel.AICoachViewModel", ((Provider) aICoachViewModelProvider)).put("com.focusflow.ui.viewmodel.BudgetViewModel", ((Provider) budgetViewModelProvider)).put("com.focusflow.ui.viewmodel.DashboardViewModel", ((Provider) dashboardViewModelProvider)).put("com.focusflow.ui.viewmodel.DebtViewModel", ((Provider) debtViewModelProvider)).put("com.focusflow.ui.viewmodel.EnhancedBudgetViewModel", ((Provider) enhancedBudgetViewModelProvider)).put("com.focusflow.ui.viewmodel.ExpenseViewModel", ((Provider) expenseViewModelProvider)).put("com.focusflow.ui.viewmodel.HabitViewModel", ((Provider) habitViewModelProvider)).put("com.focusflow.ui.viewmodel.ImpulseControlViewModel", ((Provider) impulseControlViewModelProvider)).put("com.focusflow.ui.viewmodel.MainViewModel", ((Provider) mainViewModelProvider)).put("com.focusflow.ui.viewmodel.OnboardingViewModel", ((Provider) onboardingViewModelProvider)).put("com.focusflow.ui.viewmodel.PayoffPlannerViewModel", ((Provider) payoffPlannerViewModelProvider)).put("com.focusflow.ui.viewmodel.SettingsViewModel", ((Provider) settingsViewModelProvider)).put("com.focusflow.ui.viewmodel.TaskViewModel", ((Provider) taskViewModelProvider)).put("com.focusflow.ui.viewmodel.VirtualPetViewModel", ((Provider) virtualPetViewModelProvider)).put("com.focusflow.ui.viewmodel.ZeroBudgetViewModel", ((Provider) zeroBudgetViewModelProvider)).build();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.focusflow.ui.viewmodel.AICoachViewModel 
          return (T) new AICoachViewModel(singletonCImpl.userPreferencesRepositoryProvider.get(), singletonCImpl.expenseRepositoryProvider.get(), singletonCImpl.creditCardRepositoryProvider.get(), singletonCImpl.mockAIServiceProvider.get());

          case 1: // com.focusflow.ui.viewmodel.BudgetViewModel 
          return (T) new BudgetViewModel(singletonCImpl.budgetCategoryRepositoryProvider.get(), singletonCImpl.expenseRepositoryProvider.get(), singletonCImpl.userPreferencesRepositoryProvider.get());

          case 2: // com.focusflow.ui.viewmodel.DashboardViewModel 
          return (T) new DashboardViewModel(singletonCImpl.expenseRepositoryProvider.get(), singletonCImpl.creditCardRepositoryProvider.get(), singletonCImpl.userPreferencesRepositoryProvider.get());

          case 3: // com.focusflow.ui.viewmodel.DebtViewModel 
          return (T) new DebtViewModel(singletonCImpl.creditCardRepositoryProvider.get());

          case 4: // com.focusflow.ui.viewmodel.EnhancedBudgetViewModel 
          return (T) new EnhancedBudgetViewModel(singletonCImpl.budgetCategoryRepositoryProvider.get(), singletonCImpl.budgetRecommendationRepositoryProvider.get(), singletonCImpl.expenseRepositoryProvider.get(), singletonCImpl.userPreferencesRepositoryProvider.get(), singletonCImpl.budgetRecommendationServiceProvider.get());

          case 5: // com.focusflow.ui.viewmodel.ExpenseViewModel 
          return (T) new ExpenseViewModel(singletonCImpl.expenseRepositoryProvider.get(), singletonCImpl.optimizedExpenseRepositoryProvider.get(), singletonCImpl.userPreferencesRepositoryProvider.get(), singletonCImpl.gamificationServiceProvider.get());

          case 6: // com.focusflow.ui.viewmodel.HabitViewModel 
          return (T) new HabitViewModel(singletonCImpl.habitRepositoryProvider.get(), singletonCImpl.gamificationServiceProvider.get());

          case 7: // com.focusflow.ui.viewmodel.ImpulseControlViewModel 
          return (T) new ImpulseControlViewModel(singletonCImpl.wishlistRepositoryProvider.get(), singletonCImpl.purchaseDelayServiceProvider.get());

          case 8: // com.focusflow.ui.viewmodel.MainViewModel 
          return (T) new MainViewModel(singletonCImpl.userPreferencesRepositoryProvider.get(), singletonCImpl.gamificationServiceProvider.get(), singletonCImpl.notificationRepositoryProvider.get(), singletonCImpl.sampleDataServiceProvider.get());

          case 9: // com.focusflow.ui.viewmodel.OnboardingViewModel 
          return (T) new OnboardingViewModel(singletonCImpl.userPreferencesRepositoryProvider.get());

          case 10: // com.focusflow.ui.viewmodel.PayoffPlannerViewModel 
          return (T) new PayoffPlannerViewModel(singletonCImpl.creditCardRepositoryProvider.get(), singletonCImpl.payoffPlanRepositoryProvider.get(), singletonCImpl.budgetCategoryRepositoryProvider.get(), singletonCImpl.userPreferencesRepositoryProvider.get());

          case 11: // com.focusflow.ui.viewmodel.SettingsViewModel 
          return (T) new SettingsViewModel(singletonCImpl.userPreferencesRepositoryProvider.get(), singletonCImpl.notificationRepositoryProvider.get());

          case 12: // com.focusflow.ui.viewmodel.TaskViewModel 
          return (T) new TaskViewModel(singletonCImpl.taskRepositoryProvider.get(), singletonCImpl.gamificationServiceProvider.get());

          case 13: // com.focusflow.ui.viewmodel.VirtualPetViewModel 
          return (T) new VirtualPetViewModel(singletonCImpl.virtualPetRepositoryProvider.get());

          case 14: // com.focusflow.ui.viewmodel.ZeroBudgetViewModel 
          return (T) new ZeroBudgetViewModel(singletonCImpl.budgetCategoryRepositoryProvider.get(), singletonCImpl.expenseRepositoryProvider.get(), singletonCImpl.userPreferencesRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends FocusFlowApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends FocusFlowApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends FocusFlowApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<FocusFlowDatabase> provideDatabaseProvider;

    private Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

    private Provider<ExpenseRepository> expenseRepositoryProvider;

    private Provider<CreditCardRepository> creditCardRepositoryProvider;

    private Provider<MockAIService> mockAIServiceProvider;

    private Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

    private Provider<BudgetRecommendationRepository> budgetRecommendationRepositoryProvider;

    private Provider<BudgetRecommendationService> budgetRecommendationServiceProvider;

    private Provider<OptimizedExpenseRepository> optimizedExpenseRepositoryProvider;

    private Provider<GamificationService> gamificationServiceProvider;

    private Provider<HabitRepository> habitRepositoryProvider;

    private Provider<WishlistRepository> wishlistRepositoryProvider;

    private Provider<NotificationService> notificationServiceProvider;

    private Provider<PurchaseDelayService> purchaseDelayServiceProvider;

    private Provider<FocusFlowNotificationManager> focusFlowNotificationManagerProvider;

    private Provider<NotificationRepository> notificationRepositoryProvider;

    private Provider<SampleDataService> sampleDataServiceProvider;

    private Provider<PayoffPlanRepository> payoffPlanRepositoryProvider;

    private Provider<TaskRepository> taskRepositoryProvider;

    private Provider<VirtualPetRepository> virtualPetRepositoryProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private UserPreferencesDao userPreferencesDao() {
      return DatabaseModule_ProvideUserPreferencesDaoFactory.provideUserPreferencesDao(provideDatabaseProvider.get());
    }

    private ExpenseDao expenseDao() {
      return DatabaseModule_ProvideExpenseDaoFactory.provideExpenseDao(provideDatabaseProvider.get());
    }

    private CreditCardDao creditCardDao() {
      return DatabaseModule_ProvideCreditCardDaoFactory.provideCreditCardDao(provideDatabaseProvider.get());
    }

    private BudgetCategoryDao budgetCategoryDao() {
      return DatabaseModule_ProvideBudgetCategoryDaoFactory.provideBudgetCategoryDao(provideDatabaseProvider.get());
    }

    private BudgetRecommendationDao budgetRecommendationDao() {
      return DatabaseModule_ProvideBudgetRecommendationDaoFactory.provideBudgetRecommendationDao(provideDatabaseProvider.get());
    }

    private AchievementDao achievementDao() {
      return DatabaseModule_ProvideAchievementDaoFactory.provideAchievementDao(provideDatabaseProvider.get());
    }

    private UserStatsDao userStatsDao() {
      return DatabaseModule_ProvideUserStatsDaoFactory.provideUserStatsDao(provideDatabaseProvider.get());
    }

    private VirtualPetDao virtualPetDao() {
      return DatabaseModule_ProvideVirtualPetDaoFactory.provideVirtualPetDao(provideDatabaseProvider.get());
    }

    private HabitLogDao habitLogDao() {
      return DatabaseModule_ProvideHabitLogDaoFactory.provideHabitLogDao(provideDatabaseProvider.get());
    }

    private WishlistItemDao wishlistItemDao() {
      return DatabaseModule_ProvideWishlistItemDaoFactory.provideWishlistItemDao(provideDatabaseProvider.get());
    }

    private PayoffPlanDao payoffPlanDao() {
      return DatabaseModule_ProvidePayoffPlanDaoFactory.providePayoffPlanDao(provideDatabaseProvider.get());
    }

    private PaymentScheduleDao paymentScheduleDao() {
      return DatabaseModule_ProvidePaymentScheduleDaoFactory.providePaymentScheduleDao(provideDatabaseProvider.get());
    }

    private PayoffMilestoneDao payoffMilestoneDao() {
      return DatabaseModule_ProvidePayoffMilestoneDaoFactory.providePayoffMilestoneDao(provideDatabaseProvider.get());
    }

    private TaskDao taskDao() {
      return DatabaseModule_ProvideTaskDaoFactory.provideTaskDao(provideDatabaseProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<FocusFlowDatabase>(singletonCImpl, 1));
      this.userPreferencesRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<UserPreferencesRepository>(singletonCImpl, 0));
      this.expenseRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<ExpenseRepository>(singletonCImpl, 2));
      this.creditCardRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<CreditCardRepository>(singletonCImpl, 3));
      this.mockAIServiceProvider = DoubleCheck.provider(new SwitchingProvider<MockAIService>(singletonCImpl, 4));
      this.budgetCategoryRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<BudgetCategoryRepository>(singletonCImpl, 5));
      this.budgetRecommendationRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<BudgetRecommendationRepository>(singletonCImpl, 6));
      this.budgetRecommendationServiceProvider = DoubleCheck.provider(new SwitchingProvider<BudgetRecommendationService>(singletonCImpl, 7));
      this.optimizedExpenseRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<OptimizedExpenseRepository>(singletonCImpl, 8));
      this.gamificationServiceProvider = DoubleCheck.provider(new SwitchingProvider<GamificationService>(singletonCImpl, 9));
      this.habitRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<HabitRepository>(singletonCImpl, 10));
      this.wishlistRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<WishlistRepository>(singletonCImpl, 11));
      this.notificationServiceProvider = DoubleCheck.provider(new SwitchingProvider<NotificationService>(singletonCImpl, 13));
      this.purchaseDelayServiceProvider = DoubleCheck.provider(new SwitchingProvider<PurchaseDelayService>(singletonCImpl, 12));
      this.focusFlowNotificationManagerProvider = DoubleCheck.provider(new SwitchingProvider<FocusFlowNotificationManager>(singletonCImpl, 15));
      this.notificationRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<NotificationRepository>(singletonCImpl, 14));
      this.sampleDataServiceProvider = DoubleCheck.provider(new SwitchingProvider<SampleDataService>(singletonCImpl, 16));
      this.payoffPlanRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<PayoffPlanRepository>(singletonCImpl, 17));
      this.taskRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<TaskRepository>(singletonCImpl, 18));
      this.virtualPetRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<VirtualPetRepository>(singletonCImpl, 19));
    }

    @Override
    public void injectFocusFlowApplication(FocusFlowApplication focusFlowApplication) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.focusflow.data.repository.UserPreferencesRepository 
          return (T) new UserPreferencesRepository(singletonCImpl.userPreferencesDao());

          case 1: // com.focusflow.data.database.FocusFlowDatabase 
          return (T) DatabaseModule_ProvideDatabaseFactory.provideDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 2: // com.focusflow.data.repository.ExpenseRepository 
          return (T) new ExpenseRepository(singletonCImpl.expenseDao());

          case 3: // com.focusflow.data.repository.CreditCardRepository 
          return (T) new CreditCardRepository(singletonCImpl.creditCardDao());

          case 4: // com.focusflow.service.MockAIService 
          return (T) new MockAIService();

          case 5: // com.focusflow.data.repository.BudgetCategoryRepository 
          return (T) new BudgetCategoryRepository(singletonCImpl.budgetCategoryDao());

          case 6: // com.focusflow.data.repository.BudgetRecommendationRepository 
          return (T) new BudgetRecommendationRepository(singletonCImpl.budgetRecommendationDao());

          case 7: // com.focusflow.service.BudgetRecommendationService 
          return (T) new BudgetRecommendationService(singletonCImpl.budgetCategoryRepositoryProvider.get(), singletonCImpl.budgetRecommendationRepositoryProvider.get(), singletonCImpl.expenseRepositoryProvider.get());

          case 8: // com.focusflow.data.repository.OptimizedExpenseRepository 
          return (T) new OptimizedExpenseRepository(singletonCImpl.expenseDao());

          case 9: // com.focusflow.service.GamificationService 
          return (T) new GamificationService(singletonCImpl.achievementDao(), singletonCImpl.userStatsDao(), singletonCImpl.virtualPetDao());

          case 10: // com.focusflow.data.repository.HabitRepository 
          return (T) new HabitRepository(singletonCImpl.habitLogDao());

          case 11: // com.focusflow.data.repository.WishlistRepository 
          return (T) new WishlistRepository(singletonCImpl.wishlistItemDao());

          case 12: // com.focusflow.service.PurchaseDelayService 
          return (T) new PurchaseDelayService(singletonCImpl.wishlistRepositoryProvider.get(), singletonCImpl.notificationServiceProvider.get());

          case 13: // com.focusflow.service.NotificationService 
          return (T) new NotificationService(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 14: // com.focusflow.data.repository.NotificationRepository 
          return (T) new NotificationRepository(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.userPreferencesRepositoryProvider.get(), singletonCImpl.focusFlowNotificationManagerProvider.get());

          case 15: // com.focusflow.service.FocusFlowNotificationManager 
          return (T) new FocusFlowNotificationManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 16: // com.focusflow.service.SampleDataService 
          return (T) new SampleDataService(singletonCImpl.creditCardRepositoryProvider.get(), singletonCImpl.budgetCategoryRepositoryProvider.get(), singletonCImpl.userPreferencesRepositoryProvider.get());

          case 17: // com.focusflow.data.repository.PayoffPlanRepository 
          return (T) new PayoffPlanRepository(singletonCImpl.payoffPlanDao(), singletonCImpl.paymentScheduleDao(), singletonCImpl.payoffMilestoneDao());

          case 18: // com.focusflow.data.repository.TaskRepository 
          return (T) new TaskRepository(singletonCImpl.taskDao());

          case 19: // com.focusflow.data.repository.VirtualPetRepository 
          return (T) new VirtualPetRepository(singletonCImpl.virtualPetDao());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
