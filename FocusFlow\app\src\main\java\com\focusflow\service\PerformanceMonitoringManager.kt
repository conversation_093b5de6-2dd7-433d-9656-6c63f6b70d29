package com.focusflow.service

import android.content.Context
import android.os.Build
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import com.google.firebase.perf.FirebasePerformance
import com.google.firebase.perf.ktx.performance
import com.google.firebase.perf.metrics.Trace
import com.google.firebase.ktx.Firebase
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Performance monitoring service optimized for ADHD user experience
 * Tracks metrics that are critical for maintaining user engagement and reducing cognitive load
 */
@Singleton
class PerformanceMonitoringManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val crashReportingManager: CrashReportingManager
) {
    
    private val firebasePerformance: FirebasePerformance by lazy {
        Firebase.performance
    }
    
    private val activeTraces = mutableMapOf<String, Trace>()
    private val performanceScope = CoroutineScope(Dispatchers.IO)
    
    init {
        initializePerformanceMonitoring()
    }
    
    private fun initializePerformanceMonitoring() {
        // Enable performance monitoring
        firebasePerformance.isPerformanceCollectionEnabled = true
        
        // Set device information for better analysis
        setDeviceMetrics()
    }
    
    private fun setDeviceMetrics() {
        try {
            // These help understand performance across different devices
            crashReportingManager.setCustomKey("device_model", Build.MODEL)
            crashReportingManager.setCustomKey("android_version", Build.VERSION.RELEASE)
            crashReportingManager.setCustomKey("sdk_int", Build.VERSION.SDK_INT)
            crashReportingManager.setCustomKey("manufacturer", Build.MANUFACTURER)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    /**
     * Start tracking a custom performance trace
     */
    fun startTrace(traceName: String): Trace? {
        return try {
            val trace = firebasePerformance.newTrace(traceName)
            trace.start()
            activeTraces[traceName] = trace
            trace
        } catch (e: Exception) {
            crashReportingManager.logException(e)
            null
        }
    }
    
    /**
     * Stop and record a performance trace
     */
    fun stopTrace(traceName: String) {
        try {
            activeTraces[traceName]?.let { trace ->
                trace.stop()
                activeTraces.remove(traceName)
            }
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    /**
     * Add custom metrics to an active trace
     */
    fun addTraceMetric(traceName: String, metricName: String, value: Long) {
        try {
            activeTraces[traceName]?.putMetric(metricName, value)
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    /**
     * Track screen load performance - critical for ADHD users who need quick feedback
     */
    fun trackScreenLoad(screenName: String, onComplete: () -> Unit = {}) {
        val traceName = "screen_load_$screenName"
        val startTime = System.currentTimeMillis()
        
        startTrace(traceName)?.let { trace ->
            trace.putAttribute("screen_name", screenName)
            
            performanceScope.launch {
                try {
                    onComplete()
                    val loadTime = System.currentTimeMillis() - startTime
                    
                    // ADHD users need screens to load within 2 seconds for optimal experience
                    if (loadTime > 2000) {
                        crashReportingManager.logPerformanceIssue(
                            "Slow screen load: $screenName",
                            loadTime
                        )
                    }
                    
                    trace.putMetric("load_time_ms", loadTime)
                    trace.stop()
                    activeTraces.remove(traceName)
                } catch (e: Exception) {
                    crashReportingManager.logException(e)
                    trace.stop()
                    activeTraces.remove(traceName)
                }
            }
        }
    }
    
    /**
     * Track user input response time - important for maintaining ADHD user engagement
     */
    fun trackUserInputResponse(inputType: String, responseTimeMs: Long) {
        try {
            val traceName = "user_input_$inputType"
            val trace = firebasePerformance.newTrace(traceName)
            trace.start()
            trace.putAttribute("input_type", inputType)
            trace.putMetric("response_time_ms", responseTimeMs)
            
            // ADHD users need immediate feedback (< 100ms for optimal, < 300ms acceptable)
            when {
                responseTimeMs > 300 -> {
                    trace.putAttribute("performance_rating", "poor")
                    crashReportingManager.logPerformanceIssue(
                        "Slow input response: $inputType",
                        responseTimeMs
                    )
                }
                responseTimeMs > 100 -> {
                    trace.putAttribute("performance_rating", "acceptable")
                }
                else -> {
                    trace.putAttribute("performance_rating", "excellent")
                }
            }
            
            trace.stop()
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    /**
     * Track database operation performance
     */
    fun trackDatabaseOperation(operation: String, tableCount: Int = 1, recordCount: Int = 1) {
        val traceName = "db_operation_$operation"
        val startTime = System.currentTimeMillis()
        
        startTrace(traceName)?.let { trace ->
            trace.putAttribute("operation_type", operation)
            trace.putMetric("table_count", tableCount.toLong())
            trace.putMetric("record_count", recordCount.toLong())
            
            performanceScope.launch {
                val duration = System.currentTimeMillis() - startTime
                trace.putMetric("duration_ms", duration)
                
                // Database operations should be fast to maintain app responsiveness
                if (duration > 500) {
                    crashReportingManager.logPerformanceIssue(
                        "Slow database operation: $operation",
                        duration
                    )
                }
                
                trace.stop()
                activeTraces.remove(traceName)
            }
        }
    }
    
    /**
     * Track AI service response time
     */
    fun trackAIServiceCall(serviceType: String, requestSize: Int = 0) {
        val traceName = "ai_service_$serviceType"
        val startTime = System.currentTimeMillis()
        
        startTrace(traceName)?.let { trace ->
            trace.putAttribute("service_type", serviceType)
            trace.putMetric("request_size", requestSize.toLong())
            
            performanceScope.launch {
                val duration = System.currentTimeMillis() - startTime
                trace.putMetric("response_time_ms", duration)
                
                // AI responses should be reasonably fast to maintain user engagement
                when {
                    duration > 10000 -> {
                        trace.putAttribute("response_rating", "timeout")
                        crashReportingManager.logPerformanceIssue(
                            "AI service timeout: $serviceType",
                            duration
                        )
                    }
                    duration > 5000 -> {
                        trace.putAttribute("response_rating", "slow")
                    }
                    else -> {
                        trace.putAttribute("response_rating", "good")
                    }
                }
                
                trace.stop()
                activeTraces.remove(traceName)
            }
        }
    }
    
    /**
     * Track memory usage patterns
     */
    fun trackMemoryUsage(context: String) {
        try {
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val maxMemory = runtime.maxMemory()
            val memoryUsagePercent = (usedMemory * 100 / maxMemory).toInt()
            
            crashReportingManager.setCustomKey("memory_usage_percent", memoryUsagePercent)
            crashReportingManager.setCustomKey("memory_context", context)
            
            // Alert if memory usage is high
            if (memoryUsagePercent > 80) {
                crashReportingManager.logPerformanceIssue(
                    "High memory usage in $context: ${memoryUsagePercent}%"
                )
            }
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    /**
     * Track focus session performance metrics
     */
    fun trackFocusSessionPerformance(sessionDuration: Int, interruptions: Int) {
        try {
            val trace = firebasePerformance.newTrace("focus_session_performance")
            trace.start()
            trace.putMetric("session_duration_minutes", sessionDuration.toLong())
            trace.putMetric("interruption_count", interruptions.toLong())
            trace.putAttribute("session_quality", when {
                interruptions == 0 -> "excellent"
                interruptions <= 2 -> "good"
                interruptions <= 5 -> "fair"
                else -> "poor"
            })
            trace.stop()
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    /**
     * Track task completion performance
     */
    fun trackTaskCompletion(taskType: String, completionTimeMs: Long, wasBreakdownUsed: Boolean) {
        try {
            val trace = firebasePerformance.newTrace("task_completion")
            trace.start()
            trace.putAttribute("task_type", taskType)
            trace.putMetric("completion_time_ms", completionTimeMs)
            trace.putAttribute("breakdown_used", if (wasBreakdownUsed) "yes" else "no")
            trace.stop()
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
    
    /**
     * Clean up any remaining active traces
     */
    fun cleanup() {
        try {
            activeTraces.values.forEach { trace ->
                trace.stop()
            }
            activeTraces.clear()
        } catch (e: Exception) {
            crashReportingManager.logException(e)
        }
    }
}

/**
 * Composable function to automatically track screen performance
 */
@Composable
fun TrackScreenPerformance(
    screenName: String,
    performanceManager: PerformanceMonitoringManager
) {
    val startTime = remember { System.currentTimeMillis() }
    
    DisposableEffect(screenName) {
        val trace = performanceManager.startTrace("screen_$screenName")
        trace?.putAttribute("screen_name", screenName)
        
        onDispose {
            val duration = System.currentTimeMillis() - startTime
            trace?.putMetric("screen_time_ms", duration)
            performanceManager.stopTrace("screen_$screenName")
        }
    }
}

/**
 * Performance monitoring constants for ADHD-optimized thresholds
 */
object ADHDPerformanceThresholds {
    const val OPTIMAL_INPUT_RESPONSE_MS = 100L
    const val ACCEPTABLE_INPUT_RESPONSE_MS = 300L
    const val OPTIMAL_SCREEN_LOAD_MS = 1000L
    const val ACCEPTABLE_SCREEN_LOAD_MS = 2000L
    const val MAX_AI_RESPONSE_MS = 10000L
    const val HIGH_MEMORY_USAGE_PERCENT = 80
    const val CRITICAL_MEMORY_USAGE_PERCENT = 90
}
