package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.StringListConverter;
import com.focusflow.data.model.VirtualPet;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class VirtualPetDao_Impl implements VirtualPetDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<VirtualPet> __insertionAdapterOfVirtualPet;

  private final Converters __converters = new Converters();

  private final StringListConverter __stringListConverter = new StringListConverter();

  private final EntityDeletionOrUpdateAdapter<VirtualPet> __updateAdapterOfVirtualPet;

  private final SharedSQLiteStatement __preparedStmtOfUpdateHappiness;

  private final SharedSQLiteStatement __preparedStmtOfUpdateHealth;

  private final SharedSQLiteStatement __preparedStmtOfAddExperience;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLevel;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLastFed;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLastPlayed;

  public VirtualPetDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfVirtualPet = new EntityInsertionAdapter<VirtualPet>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `virtual_pet` (`id`,`name`,`type`,`level`,`happiness`,`health`,`experience`,`lastFed`,`lastPlayed`,`accessories`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VirtualPet entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getType());
        }
        statement.bindLong(4, entity.getLevel());
        statement.bindLong(5, entity.getHappiness());
        statement.bindLong(6, entity.getHealth());
        statement.bindLong(7, entity.getExperience());
        final String _tmp = __converters.fromLocalDateTime(entity.getLastFed());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getLastPlayed());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        final String _tmp_2 = __stringListConverter.fromStringList(entity.getAccessories());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
      }
    };
    this.__updateAdapterOfVirtualPet = new EntityDeletionOrUpdateAdapter<VirtualPet>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `virtual_pet` SET `id` = ?,`name` = ?,`type` = ?,`level` = ?,`happiness` = ?,`health` = ?,`experience` = ?,`lastFed` = ?,`lastPlayed` = ?,`accessories` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VirtualPet entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getName());
        }
        if (entity.getType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getType());
        }
        statement.bindLong(4, entity.getLevel());
        statement.bindLong(5, entity.getHappiness());
        statement.bindLong(6, entity.getHealth());
        statement.bindLong(7, entity.getExperience());
        final String _tmp = __converters.fromLocalDateTime(entity.getLastFed());
        if (_tmp == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getLastPlayed());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        final String _tmp_2 = __stringListConverter.fromStringList(entity.getAccessories());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        statement.bindLong(11, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateHappiness = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE virtual_pet SET happiness = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateHealth = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE virtual_pet SET health = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfAddExperience = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE virtual_pet SET experience = experience + ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLevel = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE virtual_pet SET level = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLastFed = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE virtual_pet SET lastFed = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLastPlayed = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE virtual_pet SET lastPlayed = ? WHERE id = 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertVirtualPet(final VirtualPet virtualPet,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfVirtualPet.insert(virtualPet);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateVirtualPet(final VirtualPet virtualPet,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfVirtualPet.handle(virtualPet);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateHappiness(final int happiness, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateHappiness.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, happiness);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateHappiness.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateHealth(final int health, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateHealth.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, health);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateHealth.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object addExperience(final int exp, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfAddExperience.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, exp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfAddExperience.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLevel(final int level, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLevel.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, level);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLevel.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLastFed(final LocalDateTime timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLastFed.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(timestamp);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLastFed.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLastPlayed(final LocalDateTime timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLastPlayed.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(timestamp);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLastPlayed.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<VirtualPet> getVirtualPet() {
    final String _sql = "SELECT `virtual_pet`.`id` AS `id`, `virtual_pet`.`name` AS `name`, `virtual_pet`.`type` AS `type`, `virtual_pet`.`level` AS `level`, `virtual_pet`.`happiness` AS `happiness`, `virtual_pet`.`health` AS `health`, `virtual_pet`.`experience` AS `experience`, `virtual_pet`.`lastFed` AS `lastFed`, `virtual_pet`.`lastPlayed` AS `lastPlayed`, `virtual_pet`.`accessories` AS `accessories` FROM virtual_pet WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"virtual_pet"}, new Callable<VirtualPet>() {
      @Override
      @Nullable
      public VirtualPet call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfType = 2;
          final int _cursorIndexOfLevel = 3;
          final int _cursorIndexOfHappiness = 4;
          final int _cursorIndexOfHealth = 5;
          final int _cursorIndexOfExperience = 6;
          final int _cursorIndexOfLastFed = 7;
          final int _cursorIndexOfLastPlayed = 8;
          final int _cursorIndexOfAccessories = 9;
          final VirtualPet _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpHappiness;
            _tmpHappiness = _cursor.getInt(_cursorIndexOfHappiness);
            final int _tmpHealth;
            _tmpHealth = _cursor.getInt(_cursorIndexOfHealth);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final LocalDateTime _tmpLastFed;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfLastFed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfLastFed);
            }
            _tmpLastFed = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastPlayed;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastPlayed)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastPlayed);
            }
            _tmpLastPlayed = __converters.toLocalDateTime(_tmp_1);
            final List<String> _tmpAccessories;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAccessories)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAccessories);
            }
            _tmpAccessories = __stringListConverter.toStringList(_tmp_2);
            _result = new VirtualPet(_tmpId,_tmpName,_tmpType,_tmpLevel,_tmpHappiness,_tmpHealth,_tmpExperience,_tmpLastFed,_tmpLastPlayed,_tmpAccessories);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getVirtualPetSync(final Continuation<? super VirtualPet> $completion) {
    final String _sql = "SELECT `virtual_pet`.`id` AS `id`, `virtual_pet`.`name` AS `name`, `virtual_pet`.`type` AS `type`, `virtual_pet`.`level` AS `level`, `virtual_pet`.`happiness` AS `happiness`, `virtual_pet`.`health` AS `health`, `virtual_pet`.`experience` AS `experience`, `virtual_pet`.`lastFed` AS `lastFed`, `virtual_pet`.`lastPlayed` AS `lastPlayed`, `virtual_pet`.`accessories` AS `accessories` FROM virtual_pet WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<VirtualPet>() {
      @Override
      @Nullable
      public VirtualPet call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfName = 1;
          final int _cursorIndexOfType = 2;
          final int _cursorIndexOfLevel = 3;
          final int _cursorIndexOfHappiness = 4;
          final int _cursorIndexOfHealth = 5;
          final int _cursorIndexOfExperience = 6;
          final int _cursorIndexOfLastFed = 7;
          final int _cursorIndexOfLastPlayed = 8;
          final int _cursorIndexOfAccessories = 9;
          final VirtualPet _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            if (_cursor.isNull(_cursorIndexOfName)) {
              _tmpName = null;
            } else {
              _tmpName = _cursor.getString(_cursorIndexOfName);
            }
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final int _tmpLevel;
            _tmpLevel = _cursor.getInt(_cursorIndexOfLevel);
            final int _tmpHappiness;
            _tmpHappiness = _cursor.getInt(_cursorIndexOfHappiness);
            final int _tmpHealth;
            _tmpHealth = _cursor.getInt(_cursorIndexOfHealth);
            final int _tmpExperience;
            _tmpExperience = _cursor.getInt(_cursorIndexOfExperience);
            final LocalDateTime _tmpLastFed;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfLastFed)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfLastFed);
            }
            _tmpLastFed = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastPlayed;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastPlayed)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastPlayed);
            }
            _tmpLastPlayed = __converters.toLocalDateTime(_tmp_1);
            final List<String> _tmpAccessories;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfAccessories)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfAccessories);
            }
            _tmpAccessories = __stringListConverter.toStringList(_tmp_2);
            _result = new VirtualPet(_tmpId,_tmpName,_tmpType,_tmpLevel,_tmpHappiness,_tmpHealth,_tmpExperience,_tmpLastFed,_tmpLastPlayed,_tmpAccessories);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
