package com.focusflow.data.model;

/**
 * Search and filtering models
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0016\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001BM\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0005\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005H\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00070\u0005H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u000bH\u00c6\u0003JS\u0010\u001e\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u00052\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001f\u001a\u00020\u000b2\b\u0010 \u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010!\u001a\u00020\"H\u00d6\u0001J\t\u0010#\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0013\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017\u00a8\u0006$"}, d2 = {"Lcom/focusflow/data/model/HelpSearchQuery;", "", "query", "", "categories", "", "contentTypes", "Lcom/focusflow/data/model/HelpContentType;", "difficulty", "Lcom/focusflow/data/model/DifficultyLevel;", "adhdSpecificOnly", "", "includeCompleted", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Lcom/focusflow/data/model/DifficultyLevel;ZZ)V", "getAdhdSpecificOnly", "()Z", "getCategories", "()Ljava/util/List;", "getContentTypes", "getDifficulty", "()Lcom/focusflow/data/model/DifficultyLevel;", "getIncludeCompleted", "getQuery", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class HelpSearchQuery {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String query = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> categories = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpContentType> contentTypes = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.DifficultyLevel difficulty = null;
    private final boolean adhdSpecificOnly = false;
    private final boolean includeCompleted = false;
    
    public HelpSearchQuery(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> categories, @org.jetbrains.annotations.NotNull
    java.util.List<? extends com.focusflow.data.model.HelpContentType> contentTypes, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.DifficultyLevel difficulty, boolean adhdSpecificOnly, boolean includeCompleted) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getQuery() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpContentType> getContentTypes() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.DifficultyLevel getDifficulty() {
        return null;
    }
    
    public final boolean getAdhdSpecificOnly() {
        return false;
    }
    
    public final boolean getIncludeCompleted() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpContentType> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.DifficultyLevel component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpSearchQuery copy(@org.jetbrains.annotations.NotNull
    java.lang.String query, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> categories, @org.jetbrains.annotations.NotNull
    java.util.List<? extends com.focusflow.data.model.HelpContentType> contentTypes, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.DifficultyLevel difficulty, boolean adhdSpecificOnly, boolean includeCompleted) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}