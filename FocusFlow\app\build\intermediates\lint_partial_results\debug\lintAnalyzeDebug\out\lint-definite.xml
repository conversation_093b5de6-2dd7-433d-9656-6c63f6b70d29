<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/utils/ADHDDesignValidator.kt"
            line="134"
            column="60"
            startOffset="4968"
            endLine="134"
            endColumn="96"
            endOffset="5004"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/viewmodel/AICoachViewModel.kt"
            line="155"
            column="33"
            startOffset="5899"
            endLine="155"
            endColumn="68"
            endOffset="5934"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="119"
            column="31"
            startOffset="4001"
            endLine="119"
            endColumn="64"
            endOffset="4034"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="121"
            column="63"
            startOffset="4143"
            endLine="121"
            endColumn="111"
            endOffset="4191"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="127"
            column="65"
            startOffset="4503"
            endLine="127"
            endColumn="132"
            endOffset="4570"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="175"
            column="33"
            startOffset="6337"
            endLine="175"
            endColumn="82"
            endOffset="6386"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="176"
            column="36"
            startOffset="6423"
            endLine="176"
            endColumn="70"
            endOffset="6457"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="238"
            column="30"
            startOffset="8879"
            endLine="238"
            endColumn="62"
            endOffset="8911"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="239"
            column="36"
            startOffset="8948"
            endLine="239"
            endColumn="75"
            endOffset="8987"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="240"
            column="40"
            startOffset="9028"
            endLine="240"
            endColumn="74"
            endOffset="9062"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="245"
            column="51"
            startOffset="9254"
            endLine="245"
            endColumn="102"
            endOffset="9305"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AIService.kt"
            line="251"
            column="53"
            startOffset="9545"
            endLine="251"
            endColumn="112"
            endOffset="9604"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AdvancedAnalyticsService.kt"
            line="390"
            column="84"
            startOffset="15880"
            endLine="390"
            endColumn="117"
            endOffset="15913"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/AdvancedAnalyticsService.kt"
            line="390"
            column="144"
            startOffset="15940"
            endLine="390"
            endColumn="177"
            endOffset="15973"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/BudgetRecommendationService.kt"
            line="191"
            column="138"
            startOffset="8907"
            endLine="191"
            endColumn="176"
            endOffset="8945"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/BudgetRecommendationService.kt"
            line="194"
            column="119"
            startOffset="9126"
            endLine="194"
            endColumn="157"
            endOffset="9164"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DashboardScreen.kt"
            line="163"
            column="32"
            startOffset="4857"
            endLine="163"
            endColumn="66"
            endOffset="4891"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DashboardScreen.kt"
            line="213"
            column="55"
            startOffset="6575"
            endLine="213"
            endColumn="87"
            endOffset="6607"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DashboardScreen.kt"
            line="225"
            column="57"
            startOffset="7165"
            endLine="225"
            endColumn="91"
            endOffset="7199"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="287"
            column="28"
            startOffset="9854"
            endLine="287"
            endColumn="57"
            endOffset="9883"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="362"
            column="36"
            startOffset="12346"
            endLine="362"
            endColumn="84"
            endOffset="12394"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="375"
            column="36"
            startOffset="12933"
            endLine="375"
            endColumn="81"
            endOffset="12978"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="397"
            column="35"
            startOffset="13832"
            endLine="397"
            endColumn="75"
            endOffset="13872"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="431"
            column="49"
            startOffset="15229"
            endLine="431"
            endColumn="97"
            endOffset="15277"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="787"
            column="49"
            startOffset="29809"
            endLine="787"
            endColumn="97"
            endOffset="29857"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="809"
            column="53"
            startOffset="30809"
            endLine="809"
            endColumn="101"
            endOffset="30857"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/DebtScreen.kt"
            line="814"
            column="53"
            startOffset="31034"
            endLine="814"
            endColumn="101"
            endOffset="31082"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnhancedBudgetComponents.kt"
            line="142"
            column="36"
            startOffset="4804"
            endLine="142"
            endColumn="87"
            endOffset="4855"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnhancedBudgetComponents.kt"
            line="162"
            column="36"
            startOffset="5660"
            endLine="162"
            endColumn="91"
            endOffset="5715"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnhancedBudgetComponents.kt"
            line="298"
            column="67"
            startOffset="10275"
            endLine="298"
            endColumn="98"
            endOffset="10306"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnhancedBudgetComponents.kt"
            line="304"
            column="35"
            startOffset="10550"
            endLine="304"
            endColumn="81"
            endOffset="10596"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
            line="244"
            column="41"
            startOffset="8967"
            endLine="244"
            endColumn="81"
            endOffset="9007"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
            line="512"
            column="46"
            startOffset="18421"
            endLine="512"
            endColumn="86"
            endOffset="18461"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
            line="547"
            column="43"
            startOffset="20029"
            endLine="547"
            endColumn="75"
            endOffset="20061"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="123"
            column="32"
            startOffset="4363"
            endLine="123"
            endColumn="64"
            endOffset="4395"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="142"
            column="36"
            startOffset="5136"
            endLine="142"
            endColumn="79"
            endOffset="5179"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="147"
            column="36"
            startOffset="5414"
            endLine="147"
            endColumn="83"
            endOffset="5461"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="169"
            column="40"
            startOffset="6326"
            endLine="169"
            endColumn="73"
            endOffset="6359"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="257"
            column="62"
            startOffset="9345"
            endLine="257"
            endColumn="102"
            endOffset="9385"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="258"
            column="61"
            startOffset="9460"
            endLine="258"
            endColumn="106"
            endOffset="9505"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="296"
            column="24"
            startOffset="10670"
            endLine="296"
            endColumn="53"
            endOffset="10699"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="335"
            column="25"
            startOffset="11883"
            endLine="335"
            endColumn="61"
            endOffset="11919"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="474"
            column="43"
            startOffset="16498"
            endLine="474"
            endColumn="121"
            endOffset="16576"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="667"
            column="36"
            startOffset="24034"
            endLine="667"
            endColumn="68"
            endOffset="24066"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="681"
            column="36"
            startOffset="24676"
            endLine="681"
            endColumn="83"
            endOffset="24723"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="698"
            column="36"
            startOffset="25286"
            endLine="698"
            endColumn="79"
            endOffset="25329"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="703"
            column="35"
            startOffset="25567"
            endLine="703"
            endColumn="79"
            endOffset="25611"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="737"
            column="51"
            startOffset="26956"
            endLine="737"
            endColumn="84"
            endOffset="26989"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/utils/ErrorHandling.kt"
            line="348"
            column="31"
            startOffset="10951"
            endLine="348"
            endColumn="60"
            endOffset="10980"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/utils/ErrorHandling.kt"
            line="359"
            column="40"
            startOffset="11473"
            endLine="359"
            endColumn="69"
            endOffset="11502"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/utils/ErrorHandling.kt"
            line="359"
            column="77"
            startOffset="11510"
            endLine="359"
            endColumn="106"
            endOffset="11539"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/utils/ErrorHandling.kt"
            line="364"
            column="31"
            startOffset="11779"
            endLine="364"
            endColumn="61"
            endOffset="11809"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/FocusTimerComponents.kt"
            line="92"
            column="32"
            startOffset="3275"
            endLine="92"
            endColumn="76"
            endOffset="3319"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `capitalize(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <fix-alternatives>
            <fix-replace
                description="Replace with `capitalize(Locale.ROOT)`"
                family="Use explicit locale"
                replacement="capitalize(java.util.Locale.ROOT)"
                shortenNames="true"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/GamificationService.kt"
                    startOffset="11828"
                    endOffset="11840"/>
            </fix-replace>
            <fix-replace
                description="Replace with `capitalize(Locale.getDefault())`"
                family="Use explicit locale"
                replacement="capitalize(java.util.Locale.getDefault())"
                shortenNames="true"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/GamificationService.kt"
                    startOffset="11828"
                    endOffset="11840"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/GamificationService.kt"
            line="334"
            column="53"
            startOffset="11828"
            endLine="334"
            endColumn="63"
            endOffset="11838"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="71"
            column="54"
            startOffset="2278"
            endLine="71"
            endColumn="83"
            endOffset="2307"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="284"
            column="56"
            startOffset="10259"
            endLine="284"
            endColumn="110"
            endOffset="10313"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="286"
            column="43"
            startOffset="10400"
            endLine="286"
            endColumn="81"
            endOffset="10438"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlSettingsCard.kt"
            line="93"
            column="79"
            startOffset="3661"
            endLine="93"
            endColumn="127"
            endOffset="3709"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/data/repository/NotificationRepository.kt"
            line="115"
            column="49"
            startOffset="4621"
            endLine="115"
            endColumn="78"
            endOffset="4650"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/OptimizedComponents.kt"
            line="63"
            column="9"
            startOffset="1954"
            endLine="63"
            endColumn="47"
            endOffset="1992"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/OptimizedComponents.kt"
            line="179"
            column="28"
            startOffset="5483"
            endLine="179"
            endColumn="66"
            endOffset="5521"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/OptimizedComponents.kt"
            line="197"
            column="24"
            startOffset="6149"
            endLine="197"
            endColumn="109"
            endOffset="6234"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/OptimizedComponents.kt"
            line="278"
            column="28"
            startOffset="8572"
            endLine="278"
            endColumn="77"
            endOffset="8621"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/OptimizedComponents.kt"
            line="301"
            column="28"
            startOffset="9420"
            endLine="301"
            endColumn="76"
            endOffset="9468"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/OptimizedComponents.kt"
            line="306"
            column="28"
            startOffset="9675"
            endLine="306"
            endColumn="77"
            endOffset="9724"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PayoffPlannerScreen.kt"
            line="861"
            column="33"
            startOffset="32715"
            endLine="861"
            endColumn="84"
            endOffset="32766"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PayoffPlannerScreen.kt"
            line="866"
            column="33"
            startOffset="32941"
            endLine="866"
            endColumn="80"
            endOffset="32988"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PayoffPlannerScreen.kt"
            line="1000"
            column="46"
            startOffset="37270"
            endLine="1000"
            endColumn="86"
            endOffset="37310"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PayoffPlannerScreen.kt"
            line="1011"
            column="32"
            startOffset="37626"
            endLine="1011"
            endColumn="67"
            endOffset="37661"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PayoffPlannerScreen.kt"
            line="1017"
            column="41"
            startOffset="37973"
            endLine="1017"
            endColumn="85"
            endOffset="38017"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/PerformanceOptimizationService.kt"
            line="99"
            column="71"
            startOffset="3565"
            endLine="99"
            endColumn="103"
            endOffset="3597"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="180"
            column="35"
            startOffset="5969"
            endLine="180"
            endColumn="79"
            endOffset="6013"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="254"
            column="24"
            startOffset="8598"
            endLine="254"
            endColumn="53"
            endOffset="8627"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="326"
            column="36"
            startOffset="10967"
            endLine="326"
            endColumn="85"
            endOffset="11016"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="326"
            column="93"
            startOffset="11024"
            endLine="326"
            endColumn="146"
            endOffset="11077"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="330"
            column="35"
            startOffset="11226"
            endLine="330"
            endColumn="79"
            endOffset="11270"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="358"
            column="47"
            startOffset="12484"
            endLine="358"
            endColumn="80"
            endOffset="12517"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ThemeSettingsComponents.kt"
            line="396"
            column="43"
            startOffset="13114"
            endLine="396"
            endColumn="78"
            endOffset="13149"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/VisualAnalyticsComponents.kt"
            line="293"
            column="59"
            startOffset="9485"
            endLine="293"
            endColumn="100"
            endOffset="9526"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/VisualAnalyticsComponents.kt"
            line="331"
            column="28"
            startOffset="10824"
            endLine="331"
            endColumn="63"
            endOffset="10859"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/VisualAnalyticsComponents.kt"
            line="337"
            column="36"
            startOffset="11075"
            endLine="337"
            endColumn="71"
            endOffset="11110"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/VisualAnalyticsComponents.kt"
            line="369"
            column="28"
            startOffset="11917"
            endLine="369"
            endColumn="66"
            endOffset="11955"/>
    </incident>

    <incident
        id="Instantiatable"
        severity="fatal"
        message="`NotificationService` must extend android.app.Service">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="69"
            column="27"
            startOffset="2989"
            endLine="69"
            endColumn="55"
            endOffset="3017"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="16"
            column="9"
            startOffset="310"
            endLine="16"
            endColumn="21"
            endOffset="322"/>
    </incident>

    <incident
        id="RedundantLabel"
        severity="warning"
        message="Redundant label can be removed">
        <fix-attribute
            description="Delete label"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="label"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="50"
            column="13"
            startOffset="2220"
            endLine="50"
            endColumn="45"
            endOffset="2252"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of Gradle than 8.12 is available: 8.14.2">
        <fix-replace
            description="Update to 8.14.2"
            oldString="8.12"
            replacement="8.14.2"
            priority="0"/>
        <location
            file="$HOME/Downloads/Understanding Content in pasted_content (1)/FocusFlow/gradle/wrapper/gradle-wrapper.properties"
            line="3"
            column="17"
            startOffset="81"
            endLine="3"
            endColumn="79"
            endOffset="143"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of `compileSdkVersion` than 34 is available: 35">
        <fix-replace
            description="Set compileSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="11"
            column="5"
            startOffset="211"
            endLine="11"
            endColumn="18"
            endOffset="224"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="116"
            column="20"
            startOffset="2884"
            endLine="116"
            endColumn="51"
            endOffset="2915"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="117"
            column="20"
            startOffset="2935"
            endLine="117"
            endColumn="68"
            endOffset="2983"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="118"
            column="20"
            startOffset="3003"
            endLine="118"
            endColumn="62"
            endOffset="3045"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00">
        <fix-replace
            description="Change to 2025.06.00"
            family="Update versions"
            oldString="2023.10.01"
            replacement="2025.06.00"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="121"
            column="20"
            startOffset="3089"
            endLine="121"
            endColumn="71"
            endOffset="3140"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-compose than 2.7.5 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.5"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="129"
            column="20"
            startOffset="3467"
            endLine="129"
            endColumn="66"
            endOffset="3513"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-compose than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="132"
            column="20"
            startOffset="3555"
            endLine="132"
            endColumn="74"
            endOffset="3609"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-compose than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="133"
            column="20"
            startOffset="3629"
            endLine="133"
            endColumn="72"
            endOffset="3681"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.hilt:hilt-navigation-compose than 1.1.0 is available: 1.2.0">
        <fix-replace
            description="Change to 1.2.0"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="137"
            column="20"
            startOffset="3775"
            endLine="137"
            endColumn="65"
            endOffset="3820"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="141"
            column="20"
            startOffset="3905"
            endLine="141"
            endColumn="54"
            endOffset="3939"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="142"
            column="20"
            startOffset="3959"
            endLine="142"
            endColumn="50"
            endOffset="3989"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="143"
            column="10"
            startOffset="3999"
            endLine="143"
            endColumn="45"
            endOffset="4034"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-beta01">
        <fix-replace
            description="Change to 1.1.0-beta01"
            family="Update versions"
            oldString="1.1.0-alpha06"
            replacement="1.1.0-beta01"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="163"
            column="20"
            startOffset="4674"
            endLine="163"
            endColumn="69"
            endOffset="4723"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime-ktx than 2.9.0 is available: 2.10.1">
        <fix-replace
            description="Change to 2.10.1"
            family="Update versions"
            oldString="2.9.0"
            replacement="2.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="166"
            column="20"
            startOffset="4789"
            endLine="166"
            endColumn="58"
            endOffset="4827"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.hilt:hilt-work than 1.1.0 is available: 1.2.0">
        <fix-replace
            description="Change to 1.2.0"
            family="Update versions"
            oldString="1.1.0"
            replacement="1.2.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="167"
            column="20"
            startOffset="4847"
            endLine="167"
            endColumn="51"
            endOffset="4878"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="173"
            column="20"
            startOffset="5043"
            endLine="173"
            endColumn="51"
            endOffset="5074"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.android.tools:desugar_jdk_libs than 2.0.4 is available: 2.1.5">
        <fix-replace
            description="Change to 2.1.5"
            family="Update versions"
            oldString="2.0.4"
            replacement="2.1.5"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="184"
            column="27"
            startOffset="5521"
            endLine="184"
            endColumn="69"
            endOffset="5563"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test:core than 1.5.0 is available: 1.6.1">
        <fix-replace
            description="Change to 1.6.1"
            family="Update versions"
            oldString="1.5.0"
            replacement="1.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="188"
            column="24"
            startOffset="5651"
            endLine="188"
            endColumn="50"
            endOffset="5677"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="189"
            column="24"
            startOffset="5701"
            endLine="189"
            endColumn="55"
            endOffset="5732"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-testing than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="190"
            column="24"
            startOffset="5756"
            endLine="190"
            endColumn="58"
            endOffset="5790"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="196"
            column="31"
            startOffset="6043"
            endLine="196"
            endColumn="62"
            endOffset="6074"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="197"
            column="31"
            startOffset="6105"
            endLine="197"
            endColumn="75"
            endOffset="6149"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2023.10.01 is available: 2025.06.00">
        <fix-replace
            description="Change to 2025.06.00"
            family="Update versions"
            oldString="2023.10.01"
            replacement="2025.06.00"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="198"
            column="31"
            startOffset="6180"
            endLine="198"
            endColumn="82"
            endOffset="6231"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-testing than 2.7.5 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.7.5"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="200"
            column="31"
            startOffset="6329"
            endLine="200"
            endColumn="77"
            endOffset="6375"/>
    </incident>

    <incident
        id="ComposableModifierFactory"
        severity="warning"
        message="Modifier factory functions should not be marked as @Composable, and should use composed instead">
        <fix-replace
            description="Replace @Composable with composed call"
            robot="true"
            independent="true"
            replacement="/**&#xA;     * Responsive card layout&#xA;     */&#xA;    fun getCardModifier(): Modifier = composed {&#xA;        val config = getLayoutConfig()&#xA;        Modifier&#xA;            .fillMaxWidth()&#xA;            .padding(config.cardSpacing / 2)&#xA;    }"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt"
                startOffset="6406"
                endOffset="6643"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt"
            line="217"
            column="9"
            startOffset="6472"
            endLine="217"
            endColumn="24"
            endOffset="6487"/>
    </incident>

    <incident
        id="ModifierFactoryExtensionFunction"
        severity="warning"
        message="Modifier factory functions should be extensions on Modifier">
        <fix-replace
            description="Add Modifier receiver"
            robot="true"
            independent="true"
            oldString="getCardModifier"
            replacement="Modifier.getCardModifier"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt"
                startOffset="6406"
                endOffset="6643"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt"
            line="217"
            column="9"
            startOffset="6472"
            endLine="217"
            endColumn="24"
            endOffset="6487"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/DecisionSupportComponents.kt"
            line="29"
            column="5"
            startOffset="964"
            endLine="29"
            endColumn="13"
            endOffset="972"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/FocusTimerComponents.kt"
            line="41"
            column="5"
            startOffset="1430"
            endLine="41"
            endColumn="13"
            endOffset="1438"/>
    </incident>

    <incident
        id="ModifierParameter"
        severity="warning"
        message="Modifier parameter should be the first optional parameter">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlSettingsCard.kt"
            line="23"
            column="5"
            startOffset="903"
            endLine="23"
            endColumn="13"
            endOffset="911"/>
    </incident>

    <incident
        id="MutableCollectionMutableState"
        severity="warning"
        message="Creating a MutableState object with a mutable collection type">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
            line="493"
            column="9"
            startOffset="17867"
            endLine="493"
            endColumn="23"
            endOffset="17881"/>
    </incident>

    <incident
        id="MutableCollectionMutableState"
        severity="warning"
        message="Creating a MutableState object with a mutable collection type">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/PlaceholderScreens.kt"
            line="407"
            column="42"
            startOffset="14003"
            endLine="407"
            endColumn="56"
            endOffset="14017"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="52"
            column="13"
            startOffset="2316"
            endLine="52"
            endColumn="52"
            endOffset="2355"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="65"
            column="13"
            startOffset="2864"
            endLine="65"
            endColumn="52"
            endOffset="2903"/>
    </incident>

    <incident
        id="KaptUsageInsteadOfKsp"
        severity="warning"
        message="This library supports using KSP instead of kapt, which greatly improves performance. Learn more: https://developer.android.com/studio/build/migrate-to-ksp">
        <show-url
            description="Learn about how to enable KSP and use the KSP processor for this dependency instead"
            url="https://developer.android.com/studio/build/migrate-to-ksp"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="143"
            column="5"
            startOffset="3994"
            endLine="143"
            endColumn="45"
            endOffset="4034"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
                startOffset="865"
                endOffset="879"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="27"
            column="37"
            startOffset="865"
            endLine="27"
            endColumn="51"
            endOffset="879"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
                startOffset="16168"
                endOffset="16182"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="469"
            column="40"
            startOffset="16168"
            endLine="469"
            endColumn="54"
            endOffset="16182"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
                startOffset="16221"
                endOffset="16235"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlComponents.kt"
            line="470"
            column="34"
            startOffset="16221"
            endLine="470"
            endColumn="48"
            endOffset="16235"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableIntStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableIntStateOf"
            replacement="androidx.compose.runtime.mutableIntStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlSettingsCard.kt"
                startOffset="11866"
                endOffset="11880"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/ImpulseControlSettingsCard.kt"
            line="335"
            column="38"
            startOffset="11866"
            endLine="335"
            endColumn="52"
            endOffset="11880"/>
    </incident>

    <incident
        id="AutoboxingStateCreation"
        severity="informational"
        message="Prefer `mutableFloatStateOf` instead of `mutableStateOf`">
        <fix-replace
            description="Replace with mutableFloatStateOf"
            replacement="androidx.compose.runtime.mutableFloatStateOf"
            shortenNames="true"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/VisualAnalyticsComponents.kt"
                startOffset="1102"
                endOffset="1116"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/VisualAnalyticsComponents.kt"
            line="32"
            column="41"
            startOffset="1102"
            endLine="32"
            endColumn="55"
            endOffset="1116"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(color)"
            replacement="color.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
                startOffset="14121"
                endOffset="14161"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
            line="397"
            column="43"
            startOffset="14121"
            endLine="397"
            endColumn="83"
            endOffset="14161"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(color)"
            replacement="color.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
                startOffset="15674"
                endOffset="15714"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/screens/EnhancedBudgetScreen.kt"
            line="431"
            column="43"
            startOffset="15674"
            endLine="431"
            endColumn="83"
            endOffset="15714"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(category.categoryColor)"
            replacement="category.categoryColor.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
                startOffset="2516"
                endOffset="2573"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="71"
            column="15"
            startOffset="2516"
            endLine="71"
            endColumn="72"
            endOffset="2573"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `String.toColorInt` instead?">
        <fix-replace
            description="Replace with the toColorInt extension function"
            family="Replace with the toColorInt extension function"
            robot="true"
            independent="true"
            oldString="android.graphics.Color.parseColor(category.categoryColor)"
            replacement="category.categoryColor.toColorInt()"
            reformat="value"
            imports="androidx.core.graphics.toColorInt"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
                startOffset="20788"
                endOffset="20845"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/ui/components/EnvelopeBudgetComponents.kt"
            line="581"
            column="15"
            startOffset="20788"
            endLine="581"
            endColumn="72"
            endOffset="20845"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="5796"
                    endOffset="5801"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="5801"
                    endOffset="5802"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="5802"
                    endOffset="5803"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="5824"
                    endOffset="5832"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
            line="153"
            column="17"
            startOffset="5781"
            endLine="153"
            endColumn="38"
            endOffset="5802"/>
    </incident>

    <incident
        id="UseKtx"
        severity="warning"
        message="Use the KTX extension function `SharedPreferences.edit` instead?">
        <fix-composite
            description="Replace with the edit extension function"
            family="Replace with the edit extension function"
            robot="true"
            independent="true">
            <fix-replace
                description="Replace with edit"
                robot="true"
                independent="true"
                oldString="edit("
                replacement="edit"
                reformat="value"
                imports="androidx.core.content.edit"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="10296"
                    endOffset="10301"/>
            </fix-replace>
            <fix-replace
                description="Replace with  {"
                robot="true"
                independent="true"
                oldString=")"
                replacement=" {"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="10301"
                    endOffset="10302"/>
            </fix-replace>
            <fix-replace
                description="Delete &quot;.&quot;"
                robot="true"
                independent="true"
                oldString="."
                replacement=""
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="10302"
                    endOffset="10303"/>
            </fix-replace>
            <fix-replace
                description="Replace with }"
                robot="true"
                independent="true"
                oldString=".apply()"
                replacement="}"
                priority="0">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
                    startOffset="10310"
                    endOffset="10318"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/SecurityManager.kt"
            line="276"
            column="17"
            startOffset="10281"
            endLine="276"
            endColumn="38"
            endOffset="10302"/>
    </incident>

</incidents>
