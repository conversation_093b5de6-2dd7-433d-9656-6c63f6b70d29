package com.focusflow.di;

import android.content.Context;
import com.focusflow.service.CrashReportingManager;
import com.focusflow.service.SecurityManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductionModule_ProvideSecurityManagerFactory implements Factory<SecurityManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CrashReportingManager> crashReportingManagerProvider;

  public ProductionModule_ProvideSecurityManagerFactory(Provider<Context> contextProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    this.contextProvider = contextProvider;
    this.crashReportingManagerProvider = crashReportingManagerProvider;
  }

  @Override
  public SecurityManager get() {
    return provideSecurityManager(contextProvider.get(), crashReportingManagerProvider.get());
  }

  public static ProductionModule_ProvideSecurityManagerFactory create(
      Provider<Context> contextProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider) {
    return new ProductionModule_ProvideSecurityManagerFactory(contextProvider, crashReportingManagerProvider);
  }

  public static SecurityManager provideSecurityManager(Context context,
      CrashReportingManager crashReportingManager) {
    return Preconditions.checkNotNullFromProvides(ProductionModule.INSTANCE.provideSecurityManager(context, crashReportingManager));
  }
}
