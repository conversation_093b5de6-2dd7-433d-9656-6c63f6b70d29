/ Header Record For PersistentHashMapValueStorage8 7app/src/main/java/com/focusflow/FocusFlowApplication.kt0 /app/src/main/java/com/focusflow/MainActivity.kt0 /app/src/main/java/com/focusflow/MainActivity.kt= <app/src/main/java/com/focusflow/data/dao/AIInteractionDao.ktE Dapp/src/main/java/com/focusflow/data/dao/AccountabilityContactDao.ktB Aapp/src/main/java/com/focusflow/data/dao/AlternativeProductDao.kt? >app/src/main/java/com/focusflow/data/dao/BudgetAnalyticsDao.kt? >app/src/main/java/com/focusflow/data/dao/BudgetAnalyticsDao.kt> =app/src/main/java/com/focusflow/data/dao/BudgetCategoryDao.ktD Capp/src/main/java/com/focusflow/data/dao/BudgetRecommendationDao.kt: 9app/src/main/java/com/focusflow/data/dao/CreditCardDao.kt? >app/src/main/java/com/focusflow/data/dao/DashboardWidgetDao.kt7 6app/src/main/java/com/focusflow/data/dao/ExpenseDao.kt< ;app/src/main/java/com/focusflow/data/dao/FocusSessionDao.kt< ;app/src/main/java/com/focusflow/data/dao/FocusSessionDao.kt< ;app/src/main/java/com/focusflow/data/dao/GamificationDao.kt< ;app/src/main/java/com/focusflow/data/dao/GamificationDao.kt< ;app/src/main/java/com/focusflow/data/dao/GamificationDao.kt8 7app/src/main/java/com/focusflow/data/dao/HabitLogDao.kt? >app/src/main/java/com/focusflow/data/dao/PaymentScheduleDao.kt? >app/src/main/java/com/focusflow/data/dao/PayoffMilestoneDao.kt: 9app/src/main/java/com/focusflow/data/dao/PayoffPlanDao.kt? >app/src/main/java/com/focusflow/data/dao/SpendingPatternDao.kt? >app/src/main/java/com/focusflow/data/dao/SpendingPatternDao.kt? >app/src/main/java/com/focusflow/data/dao/SpendingPatternDao.ktB Aapp/src/main/java/com/focusflow/data/dao/SpendingReflectionDao.kt4 3app/src/main/java/com/focusflow/data/dao/TaskDao.kt? >app/src/main/java/com/focusflow/data/dao/UserPreferencesDao.kt< ;app/src/main/java/com/focusflow/data/dao/VoiceCommandDao.kt< ;app/src/main/java/com/focusflow/data/dao/VoiceCommandDao.kt< ;app/src/main/java/com/focusflow/data/dao/WishlistItemDao.kt< ;app/src/main/java/com/focusflow/data/database/Converters.ktC Bapp/src/main/java/com/focusflow/data/database/FocusFlowDatabase.ktC Bapp/src/main/java/com/focusflow/data/database/FocusFlowDatabase.kt< ;app/src/main/java/com/focusflow/data/model/AIInteraction.ktD Capp/src/main/java/com/focusflow/data/model/AccountabilityContact.kt: 9app/src/main/java/com/focusflow/data/model/Achievement.kt: 9app/src/main/java/com/focusflow/data/model/Achievement.kt: 9app/src/main/java/com/focusflow/data/model/Achievement.kt: 9app/src/main/java/com/focusflow/data/model/Achievement.ktA @app/src/main/java/com/focusflow/data/model/AlternativeProduct.kt> =app/src/main/java/com/focusflow/data/model/BudgetAnalytics.kt= <app/src/main/java/com/focusflow/data/model/BudgetCategory.ktC Bapp/src/main/java/com/focusflow/data/model/BudgetRecommendation.kt9 8app/src/main/java/com/focusflow/data/model/CreditCard.kt> =app/src/main/java/com/focusflow/data/model/DashboardWidget.kt6 5app/src/main/java/com/focusflow/data/model/Expense.kt; :app/src/main/java/com/focusflow/data/model/FocusSession.kt7 6app/src/main/java/com/focusflow/data/model/HabitLog.kt5 4app/src/main/java/com/focusflow/data/model/Income.kt? >app/src/main/java/com/focusflow/data/model/PayoffComparison.kt9 8app/src/main/java/com/focusflow/data/model/PayoffPlan.kt9 8app/src/main/java/com/focusflow/data/model/PayoffPlan.kt9 8app/src/main/java/com/focusflow/data/model/PayoffPlan.kt> =app/src/main/java/com/focusflow/data/model/SpendingPattern.ktA @app/src/main/java/com/focusflow/data/model/SpendingReflection.kt3 2app/src/main/java/com/focusflow/data/model/Task.kt> =app/src/main/java/com/focusflow/data/model/UserPreferences.kt; :app/src/main/java/com/focusflow/data/model/VoiceCommand.kt; :app/src/main/java/com/focusflow/data/model/WishlistItem.ktK Japp/src/main/java/com/focusflow/data/repository/AIInteractionRepository.ktM Lapp/src/main/java/com/focusflow/data/repository/BudgetAnalyticsRepository.ktL Kapp/src/main/java/com/focusflow/data/repository/BudgetCategoryRepository.ktR Qapp/src/main/java/com/focusflow/data/repository/BudgetRecommendationRepository.ktH Gapp/src/main/java/com/focusflow/data/repository/CreditCardRepository.ktE Dapp/src/main/java/com/focusflow/data/repository/ExpenseRepository.ktC Bapp/src/main/java/com/focusflow/data/repository/HabitRepository.ktC Bapp/src/main/java/com/focusflow/data/repository/HabitRepository.ktJ Iapp/src/main/java/com/focusflow/data/repository/NotificationRepository.ktN Mapp/src/main/java/com/focusflow/data/repository/OptimizedExpenseRepository.ktN Mapp/src/main/java/com/focusflow/data/repository/OptimizedExpenseRepository.ktH Gapp/src/main/java/com/focusflow/data/repository/PayoffPlanRepository.ktP Oapp/src/main/java/com/focusflow/data/repository/SpendingReflectionRepository.ktB Aapp/src/main/java/com/focusflow/data/repository/TaskRepository.ktB Aapp/src/main/java/com/focusflow/data/repository/TaskRepository.ktM Lapp/src/main/java/com/focusflow/data/repository/UserPreferencesRepository.ktH Gapp/src/main/java/com/focusflow/data/repository/VirtualPetRepository.ktF Eapp/src/main/java/com/focusflow/data/repository/WishlistRepository.kt/ .app/src/main/java/com/focusflow/di/AIModule.kt5 4app/src/main/java/com/focusflow/di/DatabaseModule.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt9 8app/src/main/java/com/focusflow/navigation/Navigation.kt: 9app/src/main/java/com/focusflow/receiver/AlarmReceiver.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.ktB Aapp/src/main/java/com/focusflow/security/DataProtectionService.kt< ;app/src/main/java/com/focusflow/security/SecurityManager.kt< ;app/src/main/java/com/focusflow/security/SecurityManager.kt< ;app/src/main/java/com/focusflow/security/SecurityManager.kt< ;app/src/main/java/com/focusflow/security/SecurityManager.kt< ;app/src/main/java/com/focusflow/security/SecurityManager.kt< ;app/src/main/java/com/focusflow/security/SecurityManager.kt< ;app/src/main/java/com/focusflow/security/SecurityManager.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.kt5 4app/src/main/java/com/focusflow/service/AIService.ktD Capp/src/main/java/com/focusflow/service/AdvancedAnalyticsService.ktD Capp/src/main/java/com/focusflow/service/AdvancedAnalyticsService.ktD Capp/src/main/java/com/focusflow/service/AdvancedAnalyticsService.ktD Capp/src/main/java/com/focusflow/service/AdvancedAnalyticsService.ktG Fapp/src/main/java/com/focusflow/service/BudgetRecommendationService.ktG Fapp/src/main/java/com/focusflow/service/BudgetRecommendationService.ktG Fapp/src/main/java/com/focusflow/service/BudgetRecommendationService.kt= <app/src/main/java/com/focusflow/service/FocusTimerService.kt= <app/src/main/java/com/focusflow/service/FocusTimerService.kt= <app/src/main/java/com/focusflow/service/FocusTimerService.kt= <app/src/main/java/com/focusflow/service/FocusTimerService.kt= <app/src/main/java/com/focusflow/service/FocusTimerService.kt? >app/src/main/java/com/focusflow/service/GamificationService.kt? >app/src/main/java/com/focusflow/service/NotificationManager.kt? >app/src/main/java/com/focusflow/service/NotificationManager.kt? >app/src/main/java/com/focusflow/service/NotificationService.kt? >app/src/main/java/com/focusflow/service/NotificationService.ktJ Iapp/src/main/java/com/focusflow/service/PerformanceOptimizationService.ktJ Iapp/src/main/java/com/focusflow/service/PerformanceOptimizationService.ktJ Iapp/src/main/java/com/focusflow/service/PerformanceOptimizationService.ktJ Iapp/src/main/java/com/focusflow/service/PerformanceOptimizationService.kt@ ?app/src/main/java/com/focusflow/service/PurchaseDelayService.kt@ ?app/src/main/java/com/focusflow/service/PurchaseDelayService.kt@ ?app/src/main/java/com/focusflow/service/PurchaseDelayService.kt@ ?app/src/main/java/com/focusflow/service/PurchaseDelayService.kt@ ?app/src/main/java/com/focusflow/service/PurchaseDelayService.kt= <app/src/main/java/com/focusflow/service/SampleDataService.kt= <app/src/main/java/com/focusflow/service/VoiceInputService.kt= <app/src/main/java/com/focusflow/service/VoiceInputService.ktH Gapp/src/main/java/com/focusflow/ui/components/ADHDFriendlyComponents.ktH Gapp/src/main/java/com/focusflow/ui/components/ADHDFriendlyComponents.ktE Dapp/src/main/java/com/focusflow/ui/components/BottomNavigationBar.ktK Japp/src/main/java/com/focusflow/ui/components/DecisionSupportComponents.ktK Japp/src/main/java/com/focusflow/ui/components/DecisionSupportComponents.ktK Japp/src/main/java/com/focusflow/ui/components/DecisionSupportComponents.ktH Gapp/src/main/java/com/focusflow/ui/components/EnhancedADHDComponents.ktH Gapp/src/main/java/com/focusflow/ui/components/EnhancedADHDComponents.ktH Gapp/src/main/java/com/focusflow/ui/components/EnhancedADHDComponents.ktH Gapp/src/main/java/com/focusflow/ui/components/EnhancedADHDComponents.ktH Gapp/src/main/java/com/focusflow/ui/components/EnhancedADHDComponents.ktJ Iapp/src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.ktJ Iapp/src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.ktG Fapp/src/main/java/com/focusflow/ui/components/EnhancedExpenseDialog.ktJ Iapp/src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.ktI Happ/src/main/java/com/focusflow/ui/components/ErrorHandlingComponents.ktF Eapp/src/main/java/com/focusflow/ui/components/FocusTimerComponents.ktJ Iapp/src/main/java/com/focusflow/ui/components/ImpulseControlComponents.ktL Kapp/src/main/java/com/focusflow/ui/components/ImpulseControlSettingsCard.ktE Dapp/src/main/java/com/focusflow/ui/components/OptimizedComponents.ktI Happ/src/main/java/com/focusflow/ui/components/PayoffPlannerComponents.ktA @app/src/main/java/com/focusflow/ui/components/SettingsDialogs.ktI Happ/src/main/java/com/focusflow/ui/components/ThemeSettingsComponents.ktI Happ/src/main/java/com/focusflow/ui/components/ThemeSettingsComponents.ktK Japp/src/main/java/com/focusflow/ui/components/VisualAnalyticsComponents.ktK Japp/src/main/java/com/focusflow/ui/components/VisualAnalyticsComponents.ktK Japp/src/main/java/com/focusflow/ui/components/VisualAnalyticsComponents.ktK Japp/src/main/java/com/focusflow/ui/components/VisualAnalyticsComponents.ktF Eapp/src/main/java/com/focusflow/ui/components/VoiceInputComponents.ktF Eapp/src/main/java/com/focusflow/ui/components/VoiceInputComponents.ktF Eapp/src/main/java/com/focusflow/ui/components/VoiceInputComponents.ktD Capp/src/main/java/com/focusflow/ui/onboarding/OnboardingActivity.ktB Aapp/src/main/java/com/focusflow/ui/onboarding/OnboardingScreen.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.ktI Happ/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt> =app/src/main/java/com/focusflow/ui/screens/DashboardScreen.kt9 8app/src/main/java/com/focusflow/ui/screens/DebtScreen.ktC Bapp/src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.ktC Bapp/src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.ktB Aapp/src/main/java/com/focusflow/ui/screens/PayoffPlannerScreen.ktB Aapp/src/main/java/com/focusflow/ui/screens/PayoffPlannerScreen.ktA @app/src/main/java/com/focusflow/ui/screens/PlaceholderScreens.kt= <app/src/main/java/com/focusflow/ui/screens/SettingsScreen.kt2 1app/src/main/java/com/focusflow/ui/theme/Color.kt2 1app/src/main/java/com/focusflow/ui/theme/Shape.kt2 1app/src/main/java/com/focusflow/ui/theme/Theme.kt2 1app/src/main/java/com/focusflow/ui/theme/Theme.kt1 0app/src/main/java/com/focusflow/ui/theme/Type.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktC Bapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktB Aapp/src/main/java/com/focusflow/ui/validation/ADHDDesignReport.ktB Aapp/src/main/java/com/focusflow/ui/validation/ADHDDesignReport.ktB Aapp/src/main/java/com/focusflow/ui/validation/ADHDDesignReport.ktB Aapp/src/main/java/com/focusflow/ui/validation/ADHDDesignReport.ktA @app/src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.ktA @app/src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.ktA @app/src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.kt@ ?app/src/main/java/com/focusflow/ui/viewmodel/BudgetViewModel.kt@ ?app/src/main/java/com/focusflow/ui/viewmodel/BudgetViewModel.kt@ ?app/src/main/java/com/focusflow/ui/viewmodel/BudgetViewModel.ktC Bapp/src/main/java/com/focusflow/ui/viewmodel/DashboardViewModel.ktC Bapp/src/main/java/com/focusflow/ui/viewmodel/DashboardViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.ktH Gapp/src/main/java/com/focusflow/ui/viewmodel/EnhancedBudgetViewModel.ktH Gapp/src/main/java/com/focusflow/ui/viewmodel/EnhancedBudgetViewModel.ktA @app/src/main/java/com/focusflow/ui/viewmodel/ExpenseViewModel.ktA @app/src/main/java/com/focusflow/ui/viewmodel/ExpenseViewModel.ktA @app/src/main/java/com/focusflow/ui/viewmodel/ExpenseViewModel.kt? >app/src/main/java/com/focusflow/ui/viewmodel/HabitViewModel.kt? >app/src/main/java/com/focusflow/ui/viewmodel/HabitViewModel.ktH Gapp/src/main/java/com/focusflow/ui/viewmodel/ImpulseControlViewModel.ktH Gapp/src/main/java/com/focusflow/ui/viewmodel/ImpulseControlViewModel.ktH Gapp/src/main/java/com/focusflow/ui/viewmodel/ImpulseControlViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/MainViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/MainViewModel.ktD Capp/src/main/java/com/focusflow/ui/viewmodel/OnboardingViewModel.ktD Capp/src/main/java/com/focusflow/ui/viewmodel/OnboardingViewModel.ktD Capp/src/main/java/com/focusflow/ui/viewmodel/OnboardingViewModel.ktG Fapp/src/main/java/com/focusflow/ui/viewmodel/PayoffPlannerViewModel.ktG Fapp/src/main/java/com/focusflow/ui/viewmodel/PayoffPlannerViewModel.ktB Aapp/src/main/java/com/focusflow/ui/viewmodel/SettingsViewModel.ktB Aapp/src/main/java/com/focusflow/ui/viewmodel/SettingsViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/TaskViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/TaskViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/TaskViewModel.kt> =app/src/main/java/com/focusflow/ui/viewmodel/TaskViewModel.ktD Capp/src/main/java/com/focusflow/ui/viewmodel/VirtualPetViewModel.ktD Capp/src/main/java/com/focusflow/ui/viewmodel/VirtualPetViewModel.ktD Capp/src/main/java/com/focusflow/ui/viewmodel/ZeroBudgetViewModel.ktD Capp/src/main/java/com/focusflow/ui/viewmodel/ZeroBudgetViewModel.kt= <app/src/main/java/com/focusflow/utils/ADHDDesignValidator.kt= <app/src/main/java/com/focusflow/utils/ADHDDesignValidator.kt= <app/src/main/java/com/focusflow/utils/ADHDDesignValidator.kt= <app/src/main/java/com/focusflow/utils/ADHDDesignValidator.ktB Aapp/src/main/java/com/focusflow/utils/DatabaseTransactionUtils.ktB Aapp/src/main/java/com/focusflow/utils/DatabaseTransactionUtils.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt7 6app/src/main/java/com/focusflow/utils/ErrorHandling.kt