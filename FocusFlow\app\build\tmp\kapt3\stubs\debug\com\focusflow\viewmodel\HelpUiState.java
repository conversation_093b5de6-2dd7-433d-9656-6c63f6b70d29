package com.focusflow.viewmodel;

/**
 * UI State for Help & Support system
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b*\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u00c7\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0006\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\t\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\n\u0012\u000e\b\u0002\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\f0\t\u0012\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\t\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\f\u0012\u000e\b\u0002\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\t\u0012\u000e\b\u0002\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000e0\t\u00a2\u0006\u0002\u0010\u001bJ\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\u000b\u00102\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000f\u00103\u001a\b\u0012\u0004\u0012\u00020\f0\tH\u00c6\u0003J\u000f\u00104\u001a\b\u0012\u0004\u0012\u00020\u00160\tH\u00c6\u0003J\u000b\u00105\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000f\u00106\u001a\b\u0012\u0004\u0012\u00020\u00190\tH\u00c6\u0003J\u000f\u00107\u001a\b\u0012\u0004\u0012\u00020\u000e0\tH\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\u000b\u00109\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010:\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010;\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u00c6\u0003J\u000f\u0010<\u001a\b\u0012\u0004\u0012\u00020\f0\tH\u00c6\u0003J\u000b\u0010=\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\t\u0010>\u001a\u00020\u0010H\u00c6\u0003J\t\u0010?\u001a\u00020\u0012H\u00c6\u0003J\u00cb\u0001\u0010@\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\t2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\n2\u000e\b\u0002\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\f0\t2\u000e\b\u0002\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\t2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\f2\u000e\b\u0002\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\t2\u000e\b\u0002\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000e0\tH\u00c6\u0001J\u0013\u0010A\u001a\u00020\u00032\b\u0010B\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010C\u001a\u00020DH\u00d6\u0001J\t\u0010E\u001a\u00020\u0006H\u00d6\u0001R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000e0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001dR\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001dR\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\f0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001dR\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\t\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001dR\u0013\u0010\u0017\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0013\u0010\u0013\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u001dR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010+R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010+R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010)R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.R\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100\u00a8\u0006F"}, d2 = {"Lcom/focusflow/viewmodel/HelpUiState;", "", "isLoading", "", "isSearching", "error", "", "searchQuery", "categories", "", "Lcom/focusflow/data/model/HelpCategory;", "featuredArticles", "Lcom/focusflow/data/model/HelpArticle;", "dailyTip", "Lcom/focusflow/data/model/HelpQuickTip;", "userProgress", "Lcom/focusflow/data/model/HelpUserProgress;", "searchResult", "Lcom/focusflow/data/model/HelpSearchResult;", "currentCategory", "categoryArticles", "categoryFAQs", "Lcom/focusflow/data/model/HelpFAQ;", "currentArticle", "articleTutorials", "Lcom/focusflow/data/model/HelpTutorial;", "adhdTips", "(ZZLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Lcom/focusflow/data/model/HelpQuickTip;Lcom/focusflow/data/model/HelpUserProgress;Lcom/focusflow/data/model/HelpSearchResult;Lcom/focusflow/data/model/HelpCategory;Ljava/util/List;Ljava/util/List;Lcom/focusflow/data/model/HelpArticle;Ljava/util/List;Ljava/util/List;)V", "getAdhdTips", "()Ljava/util/List;", "getArticleTutorials", "getCategories", "getCategoryArticles", "getCategoryFAQs", "getCurrentArticle", "()Lcom/focusflow/data/model/HelpArticle;", "getCurrentCategory", "()Lcom/focusflow/data/model/HelpCategory;", "getDailyTip", "()Lcom/focusflow/data/model/HelpQuickTip;", "getError", "()Ljava/lang/String;", "getFeaturedArticles", "()Z", "getSearchQuery", "getSearchResult", "()Lcom/focusflow/data/model/HelpSearchResult;", "getUserProgress", "()Lcom/focusflow/data/model/HelpUserProgress;", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class HelpUiState {
    private final boolean isLoading = false;
    private final boolean isSearching = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String searchQuery = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpCategory> categories = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpArticle> featuredArticles = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.HelpQuickTip dailyTip = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.model.HelpUserProgress userProgress = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.model.HelpSearchResult searchResult = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.HelpCategory currentCategory = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpArticle> categoryArticles = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpFAQ> categoryFAQs = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.HelpArticle currentArticle = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpTutorial> articleTutorials = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.HelpQuickTip> adhdTips = null;
    
    public HelpUiState(boolean isLoading, boolean isSearching, @org.jetbrains.annotations.Nullable
    java.lang.String error, @org.jetbrains.annotations.NotNull
    java.lang.String searchQuery, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpCategory> categories, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> featuredArticles, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.HelpQuickTip dailyTip, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpUserProgress userProgress, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpSearchResult searchResult, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.HelpCategory currentCategory, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> categoryArticles, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFAQ> categoryFAQs, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.HelpArticle currentArticle, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpTutorial> articleTutorials, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpQuickTip> adhdTips) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isSearching() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSearchQuery() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpCategory> getCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpArticle> getFeaturedArticles() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.HelpQuickTip getDailyTip() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpUserProgress getUserProgress() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpSearchResult getSearchResult() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.HelpCategory getCurrentCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpArticle> getCategoryArticles() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpFAQ> getCategoryFAQs() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.HelpArticle getCurrentArticle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpTutorial> getArticleTutorials() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpQuickTip> getAdhdTips() {
        return null;
    }
    
    public HelpUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.HelpCategory component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpArticle> component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpFAQ> component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.HelpArticle component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpTutorial> component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpQuickTip> component15() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpCategory> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.HelpArticle> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.HelpQuickTip component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpUserProgress component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.HelpSearchResult component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.viewmodel.HelpUiState copy(boolean isLoading, boolean isSearching, @org.jetbrains.annotations.Nullable
    java.lang.String error, @org.jetbrains.annotations.NotNull
    java.lang.String searchQuery, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpCategory> categories, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> featuredArticles, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.HelpQuickTip dailyTip, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpUserProgress userProgress, @org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HelpSearchResult searchResult, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.HelpCategory currentCategory, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpArticle> categoryArticles, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpFAQ> categoryFAQs, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.HelpArticle currentArticle, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpTutorial> articleTutorials, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.HelpQuickTip> adhdTips) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}