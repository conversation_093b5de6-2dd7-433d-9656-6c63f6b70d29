<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.POST_NOTIFICATIONS" requirement="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/receiver/AlarmReceiver.kt"
            line="78"
            column="9"
            startOffset="3444"
            endLine="78"
            endColumn="65"
            endOffset="3500"/>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="Call requires permission which may be rejected by user: code should explicitly check to see if permission is available (with `checkPermission`) or explicitly handle a potential `SecurityException`">
        <fix-data missing="android.permission.POST_NOTIFICATIONS" requirement="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/NotificationManager.kt"
            line="281"
            column="9"
            startOffset="10389"
            endLine="281"
            endColumn="65"
            endOffset="10445"/>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="20"
            column="36"
            startOffset="1030"
            endLine="20"
            endColumn="77"
            endOffset="1071"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="28"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/BudgetRecommendationService.kt"
            line="156"
            column="22"
            startOffset="7515"
            endLine="156"
            endColumn="27"
            endOffset="7520"/>
        <map>
            <entry
                name="message"
                string="Enum for switch requires API level 26 (current min is %1$s): `java.time.Month`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="owner"
                string="java.time.Month"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="34-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/DataProtectionService.kt"
            line="119"
            column="54"
            startOffset="4394"
            endLine="119"
            endColumn="64"
            endOffset="4404"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 34 (current min is %1$s): `java.util.regex.Matcher#replaceAll`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="replaceAll"/>
            <entry
                name="owner"
                string="java.util.regex.Matcher"/>
            <api-levels id="requiresApi"
                value="34-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="34-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/security/DataProtectionService.kt"
            line="132"
            column="55"
            startOffset="4910"
            endLine="132"
            endColumn="65"
            endOffset="4920"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 34 (current min is %1$s): `java.util.regex.Matcher#replaceAll`"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <entry
                name="name"
                string="replaceAll"/>
            <entry
                name="owner"
                string="java.util.regex.Matcher"/>
            <api-levels id="requiresApi"
                value="34-∞"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/receiver/AlarmReceiver.kt"
            line="78"
            column="9"
            startOffset="3444"
            endLine="78"
            endColumn="65"
            endOffset="3500"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/NotificationManager.kt"
            line="281"
            column="9"
            startOffset="10389"
            endLine="281"
            endColumn="65"
            endOffset="10445"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/focusflow/service/NotificationService.kt"
            line="65"
            column="9"
            startOffset="2493"
            endLine="65"
            endColumn="69"
            endOffset="2553"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="9"
            column="28"
            startOffset="297"
            endLine="9"
            endColumn="45"
            endOffset="314"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="13"
            column="28"
            startOffset="485"
            endLine="13"
            endColumn="40"
            endOffset="497"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml"
            line="17"
            column="28"
            startOffset="727"
            endLine="17"
            endColumn="45"
            endOffset="744"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="2082"
                endOffset="2102"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="45"
            column="9"
            startOffset="2082"
            endLine="45"
            endColumn="29"
            endOffset="2102"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
