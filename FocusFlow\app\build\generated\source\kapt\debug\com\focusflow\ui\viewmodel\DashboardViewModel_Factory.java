package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.CreditCardRepository;
import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DashboardViewModel_Factory implements Factory<DashboardViewModel> {
  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<CreditCardRepository> creditCardRepositoryProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  public DashboardViewModel_Factory(Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.creditCardRepositoryProvider = creditCardRepositoryProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
  }

  @Override
  public DashboardViewModel get() {
    return newInstance(expenseRepositoryProvider.get(), creditCardRepositoryProvider.get(), userPreferencesRepositoryProvider.get());
  }

  public static DashboardViewModel_Factory create(
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider) {
    return new DashboardViewModel_Factory(expenseRepositoryProvider, creditCardRepositoryProvider, userPreferencesRepositoryProvider);
  }

  public static DashboardViewModel newInstance(ExpenseRepository expenseRepository,
      CreditCardRepository creditCardRepository,
      UserPreferencesRepository userPreferencesRepository) {
    return new DashboardViewModel(expenseRepository, creditCardRepository, userPreferencesRepository);
  }
}
