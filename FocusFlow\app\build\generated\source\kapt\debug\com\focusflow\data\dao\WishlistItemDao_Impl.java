package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.WishlistItem;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class WishlistItemDao_Impl implements WishlistItemDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<WishlistItem> __insertionAdapterOfWishlistItem;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<WishlistItem> __deletionAdapterOfWishlistItem;

  private final EntityDeletionOrUpdateAdapter<WishlistItem> __updateAdapterOfWishlistItem;

  private final SharedSQLiteStatement __preparedStmtOfMarkAsPurchased;

  private final SharedSQLiteStatement __preparedStmtOfRemoveDelay;

  private final SharedSQLiteStatement __preparedStmtOfUpdateReflection;

  private final SharedSQLiteStatement __preparedStmtOfDeletePurchasedItemsOlderThan;

  public WishlistItemDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfWishlistItem = new EntityInsertionAdapter<WishlistItem>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `wishlist_items` (`id`,`itemName`,`estimatedPrice`,`category`,`description`,`merchant`,`addedDate`,`delayPeriodHours`,`isDelayActive`,`delayEndTime`,`priority`,`tags`,`imageUrl`,`productUrl`,`isPurchased`,`purchasedDate`,`actualPrice`,`reflectionNotes`,`stillWanted`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WishlistItem entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getItemName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getItemName());
        }
        statement.bindDouble(3, entity.getEstimatedPrice());
        if (entity.getCategory() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCategory());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getDescription());
        }
        if (entity.getMerchant() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getMerchant());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getAddedDate());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        statement.bindLong(8, entity.getDelayPeriodHours());
        final int _tmp_1 = entity.isDelayActive() ? 1 : 0;
        statement.bindLong(9, _tmp_1);
        final String _tmp_2 = __converters.fromLocalDateTime(entity.getDelayEndTime());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        if (entity.getPriority() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getPriority());
        }
        if (entity.getTags() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getTags());
        }
        if (entity.getImageUrl() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getImageUrl());
        }
        if (entity.getProductUrl() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getProductUrl());
        }
        final int _tmp_3 = entity.isPurchased() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        final String _tmp_4 = __converters.fromLocalDateTime(entity.getPurchasedDate());
        if (_tmp_4 == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, _tmp_4);
        }
        if (entity.getActualPrice() == null) {
          statement.bindNull(17);
        } else {
          statement.bindDouble(17, entity.getActualPrice());
        }
        if (entity.getReflectionNotes() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getReflectionNotes());
        }
        final Integer _tmp_5 = entity.getStillWanted() == null ? null : (entity.getStillWanted() ? 1 : 0);
        if (_tmp_5 == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, _tmp_5);
        }
      }
    };
    this.__deletionAdapterOfWishlistItem = new EntityDeletionOrUpdateAdapter<WishlistItem>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `wishlist_items` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WishlistItem entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfWishlistItem = new EntityDeletionOrUpdateAdapter<WishlistItem>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `wishlist_items` SET `id` = ?,`itemName` = ?,`estimatedPrice` = ?,`category` = ?,`description` = ?,`merchant` = ?,`addedDate` = ?,`delayPeriodHours` = ?,`isDelayActive` = ?,`delayEndTime` = ?,`priority` = ?,`tags` = ?,`imageUrl` = ?,`productUrl` = ?,`isPurchased` = ?,`purchasedDate` = ?,`actualPrice` = ?,`reflectionNotes` = ?,`stillWanted` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WishlistItem entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getItemName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getItemName());
        }
        statement.bindDouble(3, entity.getEstimatedPrice());
        if (entity.getCategory() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getCategory());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getDescription());
        }
        if (entity.getMerchant() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getMerchant());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getAddedDate());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, _tmp);
        }
        statement.bindLong(8, entity.getDelayPeriodHours());
        final int _tmp_1 = entity.isDelayActive() ? 1 : 0;
        statement.bindLong(9, _tmp_1);
        final String _tmp_2 = __converters.fromLocalDateTime(entity.getDelayEndTime());
        if (_tmp_2 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_2);
        }
        if (entity.getPriority() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getPriority());
        }
        if (entity.getTags() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getTags());
        }
        if (entity.getImageUrl() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getImageUrl());
        }
        if (entity.getProductUrl() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getProductUrl());
        }
        final int _tmp_3 = entity.isPurchased() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        final String _tmp_4 = __converters.fromLocalDateTime(entity.getPurchasedDate());
        if (_tmp_4 == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, _tmp_4);
        }
        if (entity.getActualPrice() == null) {
          statement.bindNull(17);
        } else {
          statement.bindDouble(17, entity.getActualPrice());
        }
        if (entity.getReflectionNotes() == null) {
          statement.bindNull(18);
        } else {
          statement.bindString(18, entity.getReflectionNotes());
        }
        final Integer _tmp_5 = entity.getStillWanted() == null ? null : (entity.getStillWanted() ? 1 : 0);
        if (_tmp_5 == null) {
          statement.bindNull(19);
        } else {
          statement.bindLong(19, _tmp_5);
        }
        statement.bindLong(20, entity.getId());
      }
    };
    this.__preparedStmtOfMarkAsPurchased = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE wishlist_items SET isPurchased = 1, purchasedDate = ?, actualPrice = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRemoveDelay = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE wishlist_items SET isDelayActive = 0 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateReflection = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE wishlist_items SET stillWanted = ?, reflectionNotes = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeletePurchasedItemsOlderThan = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM wishlist_items WHERE isPurchased = 1 AND purchasedDate < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertWishlistItem(final WishlistItem wishlistItem,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfWishlistItem.insertAndReturnId(wishlistItem);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWishlistItem(final WishlistItem wishlistItem,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWishlistItem.handle(wishlistItem);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWishlistItem(final WishlistItem wishlistItem,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWishlistItem.handle(wishlistItem);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object markAsPurchased(final long id, final LocalDateTime purchaseDate,
      final double actualPrice, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkAsPurchased.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(purchaseDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindDouble(_argIndex, actualPrice);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkAsPurchased.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object removeDelay(final long id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRemoveDelay.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRemoveDelay.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateReflection(final long id, final boolean stillWanted, final String notes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateReflection.acquire();
        int _argIndex = 1;
        final int _tmp = stillWanted ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        if (notes == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, notes);
        }
        _argIndex = 3;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateReflection.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePurchasedItemsOlderThan(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeletePurchasedItemsOlderThan.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeletePurchasedItemsOlderThan.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<WishlistItem>> getAllActiveWishlistItems() {
    final String _sql = "SELECT `wishlist_items`.`id` AS `id`, `wishlist_items`.`itemName` AS `itemName`, `wishlist_items`.`estimatedPrice` AS `estimatedPrice`, `wishlist_items`.`category` AS `category`, `wishlist_items`.`description` AS `description`, `wishlist_items`.`merchant` AS `merchant`, `wishlist_items`.`addedDate` AS `addedDate`, `wishlist_items`.`delayPeriodHours` AS `delayPeriodHours`, `wishlist_items`.`isDelayActive` AS `isDelayActive`, `wishlist_items`.`delayEndTime` AS `delayEndTime`, `wishlist_items`.`priority` AS `priority`, `wishlist_items`.`tags` AS `tags`, `wishlist_items`.`imageUrl` AS `imageUrl`, `wishlist_items`.`productUrl` AS `productUrl`, `wishlist_items`.`isPurchased` AS `isPurchased`, `wishlist_items`.`purchasedDate` AS `purchasedDate`, `wishlist_items`.`actualPrice` AS `actualPrice`, `wishlist_items`.`reflectionNotes` AS `reflectionNotes`, `wishlist_items`.`stillWanted` AS `stillWanted` FROM wishlist_items WHERE isPurchased = 0 ORDER BY addedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishlist_items"}, new Callable<List<WishlistItem>>() {
      @Override
      @NonNull
      public List<WishlistItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfItemName = 1;
          final int _cursorIndexOfEstimatedPrice = 2;
          final int _cursorIndexOfCategory = 3;
          final int _cursorIndexOfDescription = 4;
          final int _cursorIndexOfMerchant = 5;
          final int _cursorIndexOfAddedDate = 6;
          final int _cursorIndexOfDelayPeriodHours = 7;
          final int _cursorIndexOfIsDelayActive = 8;
          final int _cursorIndexOfDelayEndTime = 9;
          final int _cursorIndexOfPriority = 10;
          final int _cursorIndexOfTags = 11;
          final int _cursorIndexOfImageUrl = 12;
          final int _cursorIndexOfProductUrl = 13;
          final int _cursorIndexOfIsPurchased = 14;
          final int _cursorIndexOfPurchasedDate = 15;
          final int _cursorIndexOfActualPrice = 16;
          final int _cursorIndexOfReflectionNotes = 17;
          final int _cursorIndexOfStillWanted = 18;
          final List<WishlistItem> _result = new ArrayList<WishlistItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WishlistItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_1 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_2);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_3 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_4);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<WishlistItem>> getPurchasedWishlistItems() {
    final String _sql = "SELECT `wishlist_items`.`id` AS `id`, `wishlist_items`.`itemName` AS `itemName`, `wishlist_items`.`estimatedPrice` AS `estimatedPrice`, `wishlist_items`.`category` AS `category`, `wishlist_items`.`description` AS `description`, `wishlist_items`.`merchant` AS `merchant`, `wishlist_items`.`addedDate` AS `addedDate`, `wishlist_items`.`delayPeriodHours` AS `delayPeriodHours`, `wishlist_items`.`isDelayActive` AS `isDelayActive`, `wishlist_items`.`delayEndTime` AS `delayEndTime`, `wishlist_items`.`priority` AS `priority`, `wishlist_items`.`tags` AS `tags`, `wishlist_items`.`imageUrl` AS `imageUrl`, `wishlist_items`.`productUrl` AS `productUrl`, `wishlist_items`.`isPurchased` AS `isPurchased`, `wishlist_items`.`purchasedDate` AS `purchasedDate`, `wishlist_items`.`actualPrice` AS `actualPrice`, `wishlist_items`.`reflectionNotes` AS `reflectionNotes`, `wishlist_items`.`stillWanted` AS `stillWanted` FROM wishlist_items WHERE isPurchased = 1 ORDER BY purchasedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishlist_items"}, new Callable<List<WishlistItem>>() {
      @Override
      @NonNull
      public List<WishlistItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfItemName = 1;
          final int _cursorIndexOfEstimatedPrice = 2;
          final int _cursorIndexOfCategory = 3;
          final int _cursorIndexOfDescription = 4;
          final int _cursorIndexOfMerchant = 5;
          final int _cursorIndexOfAddedDate = 6;
          final int _cursorIndexOfDelayPeriodHours = 7;
          final int _cursorIndexOfIsDelayActive = 8;
          final int _cursorIndexOfDelayEndTime = 9;
          final int _cursorIndexOfPriority = 10;
          final int _cursorIndexOfTags = 11;
          final int _cursorIndexOfImageUrl = 12;
          final int _cursorIndexOfProductUrl = 13;
          final int _cursorIndexOfIsPurchased = 14;
          final int _cursorIndexOfPurchasedDate = 15;
          final int _cursorIndexOfActualPrice = 16;
          final int _cursorIndexOfReflectionNotes = 17;
          final int _cursorIndexOfStillWanted = 18;
          final List<WishlistItem> _result = new ArrayList<WishlistItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WishlistItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_1 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_2);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_3 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_4);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getItemsWithExpiredDelay(final LocalDateTime currentTime,
      final Continuation<? super List<WishlistItem>> $completion) {
    final String _sql = "SELECT * FROM wishlist_items WHERE isDelayActive = 1 AND delayEndTime <= ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDateTime(currentTime);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WishlistItem>>() {
      @Override
      @NonNull
      public List<WishlistItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfEstimatedPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedPrice");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfAddedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addedDate");
          final int _cursorIndexOfDelayPeriodHours = CursorUtil.getColumnIndexOrThrow(_cursor, "delayPeriodHours");
          final int _cursorIndexOfIsDelayActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelayActive");
          final int _cursorIndexOfDelayEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "delayEndTime");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfIsPurchased = CursorUtil.getColumnIndexOrThrow(_cursor, "isPurchased");
          final int _cursorIndexOfPurchasedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasedDate");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfReflectionNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionNotes");
          final int _cursorIndexOfStillWanted = CursorUtil.getColumnIndexOrThrow(_cursor, "stillWanted");
          final List<WishlistItem> _result = new ArrayList<WishlistItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WishlistItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp_1);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_2 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_3);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_4 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_5;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_5);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_6;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_6 = null;
            } else {
              _tmp_6 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_6 == null ? null : _tmp_6 != 0;
            _item = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<WishlistItem>> getActiveDelayItems() {
    final String _sql = "SELECT `wishlist_items`.`id` AS `id`, `wishlist_items`.`itemName` AS `itemName`, `wishlist_items`.`estimatedPrice` AS `estimatedPrice`, `wishlist_items`.`category` AS `category`, `wishlist_items`.`description` AS `description`, `wishlist_items`.`merchant` AS `merchant`, `wishlist_items`.`addedDate` AS `addedDate`, `wishlist_items`.`delayPeriodHours` AS `delayPeriodHours`, `wishlist_items`.`isDelayActive` AS `isDelayActive`, `wishlist_items`.`delayEndTime` AS `delayEndTime`, `wishlist_items`.`priority` AS `priority`, `wishlist_items`.`tags` AS `tags`, `wishlist_items`.`imageUrl` AS `imageUrl`, `wishlist_items`.`productUrl` AS `productUrl`, `wishlist_items`.`isPurchased` AS `isPurchased`, `wishlist_items`.`purchasedDate` AS `purchasedDate`, `wishlist_items`.`actualPrice` AS `actualPrice`, `wishlist_items`.`reflectionNotes` AS `reflectionNotes`, `wishlist_items`.`stillWanted` AS `stillWanted` FROM wishlist_items WHERE isDelayActive = 1 ORDER BY delayEndTime ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishlist_items"}, new Callable<List<WishlistItem>>() {
      @Override
      @NonNull
      public List<WishlistItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfItemName = 1;
          final int _cursorIndexOfEstimatedPrice = 2;
          final int _cursorIndexOfCategory = 3;
          final int _cursorIndexOfDescription = 4;
          final int _cursorIndexOfMerchant = 5;
          final int _cursorIndexOfAddedDate = 6;
          final int _cursorIndexOfDelayPeriodHours = 7;
          final int _cursorIndexOfIsDelayActive = 8;
          final int _cursorIndexOfDelayEndTime = 9;
          final int _cursorIndexOfPriority = 10;
          final int _cursorIndexOfTags = 11;
          final int _cursorIndexOfImageUrl = 12;
          final int _cursorIndexOfProductUrl = 13;
          final int _cursorIndexOfIsPurchased = 14;
          final int _cursorIndexOfPurchasedDate = 15;
          final int _cursorIndexOfActualPrice = 16;
          final int _cursorIndexOfReflectionNotes = 17;
          final int _cursorIndexOfStillWanted = 18;
          final List<WishlistItem> _result = new ArrayList<WishlistItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WishlistItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_1 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_2);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_3 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_4);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<WishlistItem>> getWishlistItemsByCategory(final String category) {
    final String _sql = "SELECT * FROM wishlist_items WHERE category = ? AND isPurchased = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishlist_items"}, new Callable<List<WishlistItem>>() {
      @Override
      @NonNull
      public List<WishlistItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfEstimatedPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedPrice");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfAddedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addedDate");
          final int _cursorIndexOfDelayPeriodHours = CursorUtil.getColumnIndexOrThrow(_cursor, "delayPeriodHours");
          final int _cursorIndexOfIsDelayActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelayActive");
          final int _cursorIndexOfDelayEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "delayEndTime");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfIsPurchased = CursorUtil.getColumnIndexOrThrow(_cursor, "isPurchased");
          final int _cursorIndexOfPurchasedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasedDate");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfReflectionNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionNotes");
          final int _cursorIndexOfStillWanted = CursorUtil.getColumnIndexOrThrow(_cursor, "stillWanted");
          final List<WishlistItem> _result = new ArrayList<WishlistItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WishlistItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_1 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_2);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_3 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_4);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<WishlistItem>> getWishlistItemsByPriority(final String priority) {
    final String _sql = "SELECT * FROM wishlist_items WHERE priority = ? AND isPurchased = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (priority == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, priority);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishlist_items"}, new Callable<List<WishlistItem>>() {
      @Override
      @NonNull
      public List<WishlistItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfEstimatedPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedPrice");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfAddedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addedDate");
          final int _cursorIndexOfDelayPeriodHours = CursorUtil.getColumnIndexOrThrow(_cursor, "delayPeriodHours");
          final int _cursorIndexOfIsDelayActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelayActive");
          final int _cursorIndexOfDelayEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "delayEndTime");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfIsPurchased = CursorUtil.getColumnIndexOrThrow(_cursor, "isPurchased");
          final int _cursorIndexOfPurchasedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasedDate");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfReflectionNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionNotes");
          final int _cursorIndexOfStillWanted = CursorUtil.getColumnIndexOrThrow(_cursor, "stillWanted");
          final List<WishlistItem> _result = new ArrayList<WishlistItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WishlistItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_1 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_2);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_3 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_4);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<WishlistItem>> getWishlistItemsByPriceRange(final double minPrice,
      final double maxPrice) {
    final String _sql = "SELECT * FROM wishlist_items WHERE estimatedPrice BETWEEN ? AND ? AND isPurchased = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minPrice);
    _argIndex = 2;
    _statement.bindDouble(_argIndex, maxPrice);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"wishlist_items"}, new Callable<List<WishlistItem>>() {
      @Override
      @NonNull
      public List<WishlistItem> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfEstimatedPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedPrice");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfAddedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addedDate");
          final int _cursorIndexOfDelayPeriodHours = CursorUtil.getColumnIndexOrThrow(_cursor, "delayPeriodHours");
          final int _cursorIndexOfIsDelayActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelayActive");
          final int _cursorIndexOfDelayEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "delayEndTime");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfIsPurchased = CursorUtil.getColumnIndexOrThrow(_cursor, "isPurchased");
          final int _cursorIndexOfPurchasedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasedDate");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfReflectionNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionNotes");
          final int _cursorIndexOfStillWanted = CursorUtil.getColumnIndexOrThrow(_cursor, "stillWanted");
          final List<WishlistItem> _result = new ArrayList<WishlistItem>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WishlistItem _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_1 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_2);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_3 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_4);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_5 == null ? null : _tmp_5 != 0;
            _item = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getWishlistItemById(final long id,
      final Continuation<? super WishlistItem> $completion) {
    final String _sql = "SELECT * FROM wishlist_items WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<WishlistItem>() {
      @Override
      @Nullable
      public WishlistItem call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfEstimatedPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "estimatedPrice");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfMerchant = CursorUtil.getColumnIndexOrThrow(_cursor, "merchant");
          final int _cursorIndexOfAddedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addedDate");
          final int _cursorIndexOfDelayPeriodHours = CursorUtil.getColumnIndexOrThrow(_cursor, "delayPeriodHours");
          final int _cursorIndexOfIsDelayActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isDelayActive");
          final int _cursorIndexOfDelayEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "delayEndTime");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfTags = CursorUtil.getColumnIndexOrThrow(_cursor, "tags");
          final int _cursorIndexOfImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "imageUrl");
          final int _cursorIndexOfProductUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "productUrl");
          final int _cursorIndexOfIsPurchased = CursorUtil.getColumnIndexOrThrow(_cursor, "isPurchased");
          final int _cursorIndexOfPurchasedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "purchasedDate");
          final int _cursorIndexOfActualPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "actualPrice");
          final int _cursorIndexOfReflectionNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "reflectionNotes");
          final int _cursorIndexOfStillWanted = CursorUtil.getColumnIndexOrThrow(_cursor, "stillWanted");
          final WishlistItem _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            final double _tmpEstimatedPrice;
            _tmpEstimatedPrice = _cursor.getDouble(_cursorIndexOfEstimatedPrice);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpMerchant;
            if (_cursor.isNull(_cursorIndexOfMerchant)) {
              _tmpMerchant = null;
            } else {
              _tmpMerchant = _cursor.getString(_cursorIndexOfMerchant);
            }
            final LocalDateTime _tmpAddedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfAddedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfAddedDate);
            }
            _tmpAddedDate = __converters.toLocalDateTime(_tmp);
            final int _tmpDelayPeriodHours;
            _tmpDelayPeriodHours = _cursor.getInt(_cursorIndexOfDelayPeriodHours);
            final boolean _tmpIsDelayActive;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDelayActive);
            _tmpIsDelayActive = _tmp_1 != 0;
            final LocalDateTime _tmpDelayEndTime;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDelayEndTime)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDelayEndTime);
            }
            _tmpDelayEndTime = __converters.toLocalDateTime(_tmp_2);
            final String _tmpPriority;
            if (_cursor.isNull(_cursorIndexOfPriority)) {
              _tmpPriority = null;
            } else {
              _tmpPriority = _cursor.getString(_cursorIndexOfPriority);
            }
            final String _tmpTags;
            if (_cursor.isNull(_cursorIndexOfTags)) {
              _tmpTags = null;
            } else {
              _tmpTags = _cursor.getString(_cursorIndexOfTags);
            }
            final String _tmpImageUrl;
            if (_cursor.isNull(_cursorIndexOfImageUrl)) {
              _tmpImageUrl = null;
            } else {
              _tmpImageUrl = _cursor.getString(_cursorIndexOfImageUrl);
            }
            final String _tmpProductUrl;
            if (_cursor.isNull(_cursorIndexOfProductUrl)) {
              _tmpProductUrl = null;
            } else {
              _tmpProductUrl = _cursor.getString(_cursorIndexOfProductUrl);
            }
            final boolean _tmpIsPurchased;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsPurchased);
            _tmpIsPurchased = _tmp_3 != 0;
            final LocalDateTime _tmpPurchasedDate;
            final String _tmp_4;
            if (_cursor.isNull(_cursorIndexOfPurchasedDate)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getString(_cursorIndexOfPurchasedDate);
            }
            _tmpPurchasedDate = __converters.toLocalDateTime(_tmp_4);
            final Double _tmpActualPrice;
            if (_cursor.isNull(_cursorIndexOfActualPrice)) {
              _tmpActualPrice = null;
            } else {
              _tmpActualPrice = _cursor.getDouble(_cursorIndexOfActualPrice);
            }
            final String _tmpReflectionNotes;
            if (_cursor.isNull(_cursorIndexOfReflectionNotes)) {
              _tmpReflectionNotes = null;
            } else {
              _tmpReflectionNotes = _cursor.getString(_cursorIndexOfReflectionNotes);
            }
            final Boolean _tmpStillWanted;
            final Integer _tmp_5;
            if (_cursor.isNull(_cursorIndexOfStillWanted)) {
              _tmp_5 = null;
            } else {
              _tmp_5 = _cursor.getInt(_cursorIndexOfStillWanted);
            }
            _tmpStillWanted = _tmp_5 == null ? null : _tmp_5 != 0;
            _result = new WishlistItem(_tmpId,_tmpItemName,_tmpEstimatedPrice,_tmpCategory,_tmpDescription,_tmpMerchant,_tmpAddedDate,_tmpDelayPeriodHours,_tmpIsDelayActive,_tmpDelayEndTime,_tmpPriority,_tmpTags,_tmpImageUrl,_tmpProductUrl,_tmpIsPurchased,_tmpPurchasedDate,_tmpActualPrice,_tmpReflectionNotes,_tmpStillWanted);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getActiveDelayCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM wishlist_items WHERE isDelayActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageWishlistPrice(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(estimatedPrice) FROM wishlist_items WHERE isPurchased = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalWishlistValue(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(estimatedPrice) FROM wishlist_items WHERE isPurchased = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
