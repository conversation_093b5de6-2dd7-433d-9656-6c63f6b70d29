package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.PayoffMilestone;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDate;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PayoffMilestoneDao_Impl implements PayoffMilestoneDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<PayoffMilestone> __insertionAdapterOfPayoffMilestone;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<PayoffMilestone> __deletionAdapterOfPayoffMilestone;

  private final EntityDeletionOrUpdateAdapter<PayoffMilestone> __updateAdapterOfPayoffMilestone;

  private final SharedSQLiteStatement __preparedStmtOfDeleteMilestonesByPlan;

  private final SharedSQLiteStatement __preparedStmtOfMarkMilestoneCompleted;

  public PayoffMilestoneDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPayoffMilestone = new EntityInsertionAdapter<PayoffMilestone>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `payoff_milestones` (`id`,`payoffPlanId`,`milestoneType`,`targetDate`,`targetAmount`,`description`,`isCompleted`,`completedDate`,`celebrationMessage`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PayoffMilestone entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getPayoffPlanId());
        if (entity.getMilestoneType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getMilestoneType());
        }
        final String _tmp = __converters.fromLocalDate(entity.getTargetDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        statement.bindDouble(5, entity.getTargetAmount());
        if (entity.getDescription() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getDescription());
        }
        final int _tmp_1 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(7, _tmp_1);
        final String _tmp_2 = __converters.fromLocalDate(entity.getCompletedDate());
        if (_tmp_2 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_2);
        }
        if (entity.getCelebrationMessage() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCelebrationMessage());
        }
      }
    };
    this.__deletionAdapterOfPayoffMilestone = new EntityDeletionOrUpdateAdapter<PayoffMilestone>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `payoff_milestones` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PayoffMilestone entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfPayoffMilestone = new EntityDeletionOrUpdateAdapter<PayoffMilestone>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `payoff_milestones` SET `id` = ?,`payoffPlanId` = ?,`milestoneType` = ?,`targetDate` = ?,`targetAmount` = ?,`description` = ?,`isCompleted` = ?,`completedDate` = ?,`celebrationMessage` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PayoffMilestone entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getPayoffPlanId());
        if (entity.getMilestoneType() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getMilestoneType());
        }
        final String _tmp = __converters.fromLocalDate(entity.getTargetDate());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        statement.bindDouble(5, entity.getTargetAmount());
        if (entity.getDescription() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getDescription());
        }
        final int _tmp_1 = entity.isCompleted() ? 1 : 0;
        statement.bindLong(7, _tmp_1);
        final String _tmp_2 = __converters.fromLocalDate(entity.getCompletedDate());
        if (_tmp_2 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_2);
        }
        if (entity.getCelebrationMessage() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getCelebrationMessage());
        }
        statement.bindLong(10, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteMilestonesByPlan = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM payoff_milestones WHERE payoffPlanId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkMilestoneCompleted = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE payoff_milestones SET isCompleted = 1, completedDate = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertMilestone(final PayoffMilestone milestone,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfPayoffMilestone.insertAndReturnId(milestone);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertMilestones(final List<PayoffMilestone> milestones,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPayoffMilestone.insert(milestones);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteMilestone(final PayoffMilestone milestone,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPayoffMilestone.handle(milestone);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateMilestone(final PayoffMilestone milestone,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPayoffMilestone.handle(milestone);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteMilestonesByPlan(final long planId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteMilestonesByPlan.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, planId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteMilestonesByPlan.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object markMilestoneCompleted(final long milestoneId, final LocalDate completedDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkMilestoneCompleted.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDate(completedDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, milestoneId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkMilestoneCompleted.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PayoffMilestone>> getMilestonesByPlan(final long planId) {
    final String _sql = "SELECT * FROM payoff_milestones WHERE payoffPlanId = ? ORDER BY targetDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payoff_milestones"}, new Callable<List<PayoffMilestone>>() {
      @Override
      @NonNull
      public List<PayoffMilestone> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfMilestoneType = CursorUtil.getColumnIndexOrThrow(_cursor, "milestoneType");
          final int _cursorIndexOfTargetDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetDate");
          final int _cursorIndexOfTargetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetAmount");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completedDate");
          final int _cursorIndexOfCelebrationMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "celebrationMessage");
          final List<PayoffMilestone> _result = new ArrayList<PayoffMilestone>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PayoffMilestone _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final String _tmpMilestoneType;
            if (_cursor.isNull(_cursorIndexOfMilestoneType)) {
              _tmpMilestoneType = null;
            } else {
              _tmpMilestoneType = _cursor.getString(_cursorIndexOfMilestoneType);
            }
            final LocalDate _tmpTargetDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTargetDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTargetDate);
            }
            _tmpTargetDate = __converters.toLocalDate(_tmp);
            final double _tmpTargetAmount;
            _tmpTargetAmount = _cursor.getDouble(_cursorIndexOfTargetAmount);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final LocalDate _tmpCompletedDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCompletedDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCompletedDate);
            }
            _tmpCompletedDate = __converters.toLocalDate(_tmp_2);
            final String _tmpCelebrationMessage;
            if (_cursor.isNull(_cursorIndexOfCelebrationMessage)) {
              _tmpCelebrationMessage = null;
            } else {
              _tmpCelebrationMessage = _cursor.getString(_cursorIndexOfCelebrationMessage);
            }
            _item = new PayoffMilestone(_tmpId,_tmpPayoffPlanId,_tmpMilestoneType,_tmpTargetDate,_tmpTargetAmount,_tmpDescription,_tmpIsCompleted,_tmpCompletedDate,_tmpCelebrationMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getMilestonesByPlanSync(final long planId,
      final Continuation<? super List<PayoffMilestone>> $completion) {
    final String _sql = "SELECT * FROM payoff_milestones WHERE payoffPlanId = ? ORDER BY targetDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PayoffMilestone>>() {
      @Override
      @NonNull
      public List<PayoffMilestone> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfMilestoneType = CursorUtil.getColumnIndexOrThrow(_cursor, "milestoneType");
          final int _cursorIndexOfTargetDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetDate");
          final int _cursorIndexOfTargetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetAmount");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completedDate");
          final int _cursorIndexOfCelebrationMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "celebrationMessage");
          final List<PayoffMilestone> _result = new ArrayList<PayoffMilestone>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PayoffMilestone _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final String _tmpMilestoneType;
            if (_cursor.isNull(_cursorIndexOfMilestoneType)) {
              _tmpMilestoneType = null;
            } else {
              _tmpMilestoneType = _cursor.getString(_cursorIndexOfMilestoneType);
            }
            final LocalDate _tmpTargetDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTargetDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTargetDate);
            }
            _tmpTargetDate = __converters.toLocalDate(_tmp);
            final double _tmpTargetAmount;
            _tmpTargetAmount = _cursor.getDouble(_cursorIndexOfTargetAmount);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final LocalDate _tmpCompletedDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCompletedDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCompletedDate);
            }
            _tmpCompletedDate = __converters.toLocalDate(_tmp_2);
            final String _tmpCelebrationMessage;
            if (_cursor.isNull(_cursorIndexOfCelebrationMessage)) {
              _tmpCelebrationMessage = null;
            } else {
              _tmpCelebrationMessage = _cursor.getString(_cursorIndexOfCelebrationMessage);
            }
            _item = new PayoffMilestone(_tmpId,_tmpPayoffPlanId,_tmpMilestoneType,_tmpTargetDate,_tmpTargetAmount,_tmpDescription,_tmpIsCompleted,_tmpCompletedDate,_tmpCelebrationMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PayoffMilestone>> getUpcomingMilestones(final LocalDate date) {
    final String _sql = "SELECT * FROM payoff_milestones WHERE isCompleted = 0 AND targetDate <= ? ORDER BY targetDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDate(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payoff_milestones"}, new Callable<List<PayoffMilestone>>() {
      @Override
      @NonNull
      public List<PayoffMilestone> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfMilestoneType = CursorUtil.getColumnIndexOrThrow(_cursor, "milestoneType");
          final int _cursorIndexOfTargetDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetDate");
          final int _cursorIndexOfTargetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetAmount");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completedDate");
          final int _cursorIndexOfCelebrationMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "celebrationMessage");
          final List<PayoffMilestone> _result = new ArrayList<PayoffMilestone>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PayoffMilestone _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final String _tmpMilestoneType;
            if (_cursor.isNull(_cursorIndexOfMilestoneType)) {
              _tmpMilestoneType = null;
            } else {
              _tmpMilestoneType = _cursor.getString(_cursorIndexOfMilestoneType);
            }
            final LocalDate _tmpTargetDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfTargetDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfTargetDate);
            }
            _tmpTargetDate = __converters.toLocalDate(_tmp_1);
            final double _tmpTargetAmount;
            _tmpTargetAmount = _cursor.getDouble(_cursorIndexOfTargetAmount);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_2 != 0;
            final LocalDate _tmpCompletedDate;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfCompletedDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfCompletedDate);
            }
            _tmpCompletedDate = __converters.toLocalDate(_tmp_3);
            final String _tmpCelebrationMessage;
            if (_cursor.isNull(_cursorIndexOfCelebrationMessage)) {
              _tmpCelebrationMessage = null;
            } else {
              _tmpCelebrationMessage = _cursor.getString(_cursorIndexOfCelebrationMessage);
            }
            _item = new PayoffMilestone(_tmpId,_tmpPayoffPlanId,_tmpMilestoneType,_tmpTargetDate,_tmpTargetAmount,_tmpDescription,_tmpIsCompleted,_tmpCompletedDate,_tmpCelebrationMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PayoffMilestone>> getCompletedMilestones() {
    final String _sql = "SELECT `payoff_milestones`.`id` AS `id`, `payoff_milestones`.`payoffPlanId` AS `payoffPlanId`, `payoff_milestones`.`milestoneType` AS `milestoneType`, `payoff_milestones`.`targetDate` AS `targetDate`, `payoff_milestones`.`targetAmount` AS `targetAmount`, `payoff_milestones`.`description` AS `description`, `payoff_milestones`.`isCompleted` AS `isCompleted`, `payoff_milestones`.`completedDate` AS `completedDate`, `payoff_milestones`.`celebrationMessage` AS `celebrationMessage` FROM payoff_milestones WHERE isCompleted = 1 ORDER BY completedDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payoff_milestones"}, new Callable<List<PayoffMilestone>>() {
      @Override
      @NonNull
      public List<PayoffMilestone> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfPayoffPlanId = 1;
          final int _cursorIndexOfMilestoneType = 2;
          final int _cursorIndexOfTargetDate = 3;
          final int _cursorIndexOfTargetAmount = 4;
          final int _cursorIndexOfDescription = 5;
          final int _cursorIndexOfIsCompleted = 6;
          final int _cursorIndexOfCompletedDate = 7;
          final int _cursorIndexOfCelebrationMessage = 8;
          final List<PayoffMilestone> _result = new ArrayList<PayoffMilestone>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PayoffMilestone _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final String _tmpMilestoneType;
            if (_cursor.isNull(_cursorIndexOfMilestoneType)) {
              _tmpMilestoneType = null;
            } else {
              _tmpMilestoneType = _cursor.getString(_cursorIndexOfMilestoneType);
            }
            final LocalDate _tmpTargetDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTargetDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTargetDate);
            }
            _tmpTargetDate = __converters.toLocalDate(_tmp);
            final double _tmpTargetAmount;
            _tmpTargetAmount = _cursor.getDouble(_cursorIndexOfTargetAmount);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final LocalDate _tmpCompletedDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCompletedDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCompletedDate);
            }
            _tmpCompletedDate = __converters.toLocalDate(_tmp_2);
            final String _tmpCelebrationMessage;
            if (_cursor.isNull(_cursorIndexOfCelebrationMessage)) {
              _tmpCelebrationMessage = null;
            } else {
              _tmpCelebrationMessage = _cursor.getString(_cursorIndexOfCelebrationMessage);
            }
            _item = new PayoffMilestone(_tmpId,_tmpPayoffPlanId,_tmpMilestoneType,_tmpTargetDate,_tmpTargetAmount,_tmpDescription,_tmpIsCompleted,_tmpCompletedDate,_tmpCelebrationMessage);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getNextMilestone(final long planId,
      final Continuation<? super PayoffMilestone> $completion) {
    final String _sql = "SELECT * FROM payoff_milestones WHERE payoffPlanId = ? AND isCompleted = 0 ORDER BY targetDate ASC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PayoffMilestone>() {
      @Override
      @Nullable
      public PayoffMilestone call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfMilestoneType = CursorUtil.getColumnIndexOrThrow(_cursor, "milestoneType");
          final int _cursorIndexOfTargetDate = CursorUtil.getColumnIndexOrThrow(_cursor, "targetDate");
          final int _cursorIndexOfTargetAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetAmount");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "completedDate");
          final int _cursorIndexOfCelebrationMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "celebrationMessage");
          final PayoffMilestone _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final String _tmpMilestoneType;
            if (_cursor.isNull(_cursorIndexOfMilestoneType)) {
              _tmpMilestoneType = null;
            } else {
              _tmpMilestoneType = _cursor.getString(_cursorIndexOfMilestoneType);
            }
            final LocalDate _tmpTargetDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfTargetDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfTargetDate);
            }
            _tmpTargetDate = __converters.toLocalDate(_tmp);
            final double _tmpTargetAmount;
            _tmpTargetAmount = _cursor.getDouble(_cursorIndexOfTargetAmount);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final boolean _tmpIsCompleted;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp_1 != 0;
            final LocalDate _tmpCompletedDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCompletedDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfCompletedDate);
            }
            _tmpCompletedDate = __converters.toLocalDate(_tmp_2);
            final String _tmpCelebrationMessage;
            if (_cursor.isNull(_cursorIndexOfCelebrationMessage)) {
              _tmpCelebrationMessage = null;
            } else {
              _tmpCelebrationMessage = _cursor.getString(_cursorIndexOfCelebrationMessage);
            }
            _result = new PayoffMilestone(_tmpId,_tmpPayoffPlanId,_tmpMilestoneType,_tmpTargetDate,_tmpTargetAmount,_tmpDescription,_tmpIsCompleted,_tmpCompletedDate,_tmpCelebrationMessage);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedMilestoneCount(final long planId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM payoff_milestones WHERE payoffPlanId = ? AND isCompleted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalMilestoneCount(final long planId,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM payoff_milestones WHERE payoffPlanId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
