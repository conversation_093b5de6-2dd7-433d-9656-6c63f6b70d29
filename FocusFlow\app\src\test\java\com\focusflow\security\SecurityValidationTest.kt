package com.focusflow.security

import com.focusflow.service.SecurityManager
import com.focusflow.service.ValidationResult
import com.focusflow.service.CrashReportingManager
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import io.mockk.mockk
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Comprehensive security validation tests for FocusFlow
 * Tests encryption, input validation, and security measures
 */
@HiltAndroidTest
@RunWith(RobolectricTestRunner::class)
class SecurityValidationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    private lateinit var securityManager: SecurityManager
    private lateinit var mockCrashReporting: CrashReportingManager

    @Before
    fun setup() {
        hiltRule.inject()
        mockCrashReporting = mockk(relaxed = true)
        securityManager = SecurityManager(
            RuntimeEnvironment.getApplication(),
            mockCrashReporting
        )
    }

    @Test
    fun dataEncryption_worksCorrectly() {
        val sensitiveData = "User's financial information: $1234.56"
        
        // Test encryption
        val encryptedData = securityManager.encryptData(sensitiveData)
        assertNotNull(encryptedData)
        assertNotNull(encryptedData?.encryptedData)
        assertNotNull(encryptedData?.iv)
        
        // Encrypted data should not contain original text
        assertFalse(encryptedData?.encryptedData?.contains("1234.56") ?: true)
        
        // Test decryption
        val decryptedData = securityManager.decryptData(encryptedData!!)
        assertEquals(sensitiveData, decryptedData)
    }

    @Test
    fun inputValidation_preventsInjectionAttacks() {
        // Test SQL injection attempts
        val sqlInjection = "'; DROP TABLE users; --"
        val result1 = securityManager.validateInput(sqlInjection)
        assertTrue(result1 is ValidationResult.Invalid)
        
        // Test XSS attempts
        val xssAttempt = "<script>alert('xss')</script>"
        val result2 = securityManager.validateInput(xssAttempt)
        assertTrue(result2 is ValidationResult.Invalid)
        
        // Test JavaScript injection
        val jsInjection = "javascript:alert('hack')"
        val result3 = securityManager.validateInput(jsInjection)
        assertTrue(result3 is ValidationResult.Invalid)
        
        // Test valid input
        val validInput = "Grocery shopping at Whole Foods"
        val result4 = securityManager.validateInput(validInput)
        assertTrue(result4 is ValidationResult.Valid)
    }

    @Test
    fun inputSanitization_removesHarmfulContent() {
        // Test HTML tag removal
        val htmlInput = "<div>Hello <script>alert('xss')</script> World</div>"
        val sanitized1 = securityManager.sanitizeInput(htmlInput)
        assertFalse(sanitized1.contains("<script>"))
        assertFalse(sanitized1.contains("<div>"))
        assertTrue(sanitized1.contains("Hello"))
        assertTrue(sanitized1.contains("World"))
        
        // Test special character handling
        val specialChars = "Amount: $50.00 & some \"quotes\""
        val sanitized2 = securityManager.sanitizeInput(specialChars)
        assertFalse(sanitized2.contains("\""))
        assertFalse(sanitized2.contains("&"))
        
        // Test length limiting
        val longInput = "a".repeat(2000)
        val sanitized3 = securityManager.sanitizeInput(longInput)
        assertTrue(sanitized3.length <= 1000)
    }

    @Test
    fun secureStorage_protectsData() {
        val sensitiveKey = "user_pin"
        val sensitiveValue = "123456"
        
        // Test storing sensitive data
        val stored = securityManager.storeSecureData(sensitiveKey, sensitiveValue)
        assertTrue(stored)
        
        // Test retrieving sensitive data
        val retrieved = securityManager.getSecureData(sensitiveKey)
        assertEquals(sensitiveValue, retrieved)
        
        // Test removing sensitive data
        val removed = securityManager.removeSecureData(sensitiveKey)
        assertTrue(removed)
        
        // Verify data is actually removed
        val afterRemoval = securityManager.getSecureData(sensitiveKey)
        assertEquals(null, afterRemoval)
    }

    @Test
    fun tokenGeneration_createsSecureTokens() {
        // Test token generation
        val token1 = securityManager.generateSecureToken()
        val token2 = securityManager.generateSecureToken()
        
        // Tokens should be different
        assertNotNull(token1)
        assertNotNull(token2)
        assertTrue(token1 != token2)
        
        // Tokens should be of appropriate length
        assertTrue(token1.length >= 32)
        assertTrue(token2.length >= 32)
        
        // Test custom length
        val shortToken = securityManager.generateSecureToken(16)
        assertTrue(shortToken.length >= 16)
    }

    @Test
    fun dataHashing_worksConsistently() {
        val data = "sensitive_password"
        val salt = "random_salt"
        
        // Test hashing
        val hash1 = securityManager.hashData(data, salt)
        val hash2 = securityManager.hashData(data, salt)
        
        // Same input should produce same hash
        assertEquals(hash1, hash2)
        
        // Different salt should produce different hash
        val hash3 = securityManager.hashData(data, "different_salt")
        assertTrue(hash1 != hash3)
        
        // Hash should not contain original data
        assertFalse(hash1.contains("password"))
    }

    @Test
    fun financialDataValidation_preventsDataLeaks() {
        // Test credit card number validation
        val ccNumber = "4111-1111-1111-1111"
        val sanitizedCC = securityManager.sanitizeInput(ccNumber)
        
        // Should not store full credit card numbers
        // (In real implementation, this would be tokenized)
        
        // Test SSN validation
        val ssn = "***********"
        val sanitizedSSN = securityManager.sanitizeInput(ssn)
        
        // Should not store full SSNs
        
        // Test bank account validation
        val bankAccount = "Account: **********"
        val sanitizedBank = securityManager.sanitizeInput(bankAccount)
        
        // Should sanitize sensitive financial data
        assertTrue(sanitizedBank.length < bankAccount.length)
    }

    @Test
    fun sessionSecurity_maintainsIntegrity() {
        // Test session token generation
        val sessionToken = securityManager.generateSecureToken()
        assertNotNull(sessionToken)
        
        // Test storing session data securely
        val sessionStored = securityManager.storeSecureData("session_token", sessionToken)
        assertTrue(sessionStored)
        
        // Test session validation
        val retrievedToken = securityManager.getSecureData("session_token")
        assertEquals(sessionToken, retrievedToken)
        
        // Test session cleanup
        val sessionCleared = securityManager.removeSecureData("session_token")
        assertTrue(sessionCleared)
    }

    @Test
    fun errorHandling_doesntLeakInformation() {
        // Test that security errors don't reveal sensitive information
        
        // Test with invalid encryption data
        val invalidEncryptedData = com.focusflow.service.EncryptedData("invalid", "invalid")
        val decryptResult = securityManager.decryptData(invalidEncryptedData)
        
        // Should fail gracefully without revealing system details
        assertEquals(null, decryptResult)
        
        // Test with malformed input
        val malformedInput = "\u0000\u0001\u0002"
        val validationResult = securityManager.validateInput(malformedInput)
        assertTrue(validationResult is ValidationResult.Invalid)
    }

    @Test
    fun adhdSpecificSecurity_protectsVulnerableUsers() {
        // Test that security measures don't overwhelm ADHD users
        
        // Validation messages should be clear and helpful
        val invalidInput = "<script>alert('test')</script>"
        val result = securityManager.validateInput(invalidInput)
        
        assertTrue(result is ValidationResult.Invalid)
        val message = (result as ValidationResult.Invalid).reason
        
        // Message should be user-friendly, not technical
        assertFalse(message.contains("XSS"))
        assertFalse(message.contains("injection"))
        assertTrue(message.contains("Invalid") || message.contains("characters"))
        
        // Security measures should not create cognitive burden
        val normalInput = "Bought groceries for $45.67"
        val normalResult = securityManager.validateInput(normalInput)
        assertTrue(normalResult is ValidationResult.Valid)
    }

    @Test
    fun dataMinimization_followsPrivacyPrinciples() {
        // Test that only necessary data is collected and stored
        
        // Financial data should be minimal
        val expenseData = "Coffee shop - $4.50"
        val sanitized = securityManager.sanitizeInput(expenseData)
        
        // Should preserve essential information
        assertTrue(sanitized.contains("Coffee") || sanitized.contains("4.50"))
        
        // Should not require excessive personal information
        val personalInfo = "John Doe, SSN: ***********, DOB: 01/01/1990"
        val sanitizedPersonal = securityManager.sanitizeInput(personalInfo)
        
        // Should remove sensitive identifiers
        assertFalse(sanitizedPersonal.contains("***********"))
    }

    @Test
    fun encryptionKeyManagement_isSecure() {
        // Test that encryption keys are properly managed
        
        // Multiple encryption operations should use proper key rotation
        val data1 = "First sensitive data"
        val data2 = "Second sensitive data"
        
        val encrypted1 = securityManager.encryptData(data1)
        val encrypted2 = securityManager.encryptData(data2)
        
        assertNotNull(encrypted1)
        assertNotNull(encrypted2)
        
        // IVs should be different (proper randomization)
        assertTrue(encrypted1?.iv != encrypted2?.iv)
        
        // Both should decrypt correctly
        assertEquals(data1, securityManager.decryptData(encrypted1!!))
        assertEquals(data2, securityManager.decryptData(encrypted2!!))
    }

    @Test
    fun securityEnvironment_isValidated() {
        // Test security environment checks
        val environmentCheck = securityManager.isSecureEnvironment()
        
        assertNotNull(environmentCheck)
        
        // In test environment, some warnings are expected
        // This validates the checking mechanism works
        
        // Should provide actionable information
        environmentCheck.warnings.forEach { warning ->
            assertTrue(warning.isNotEmpty())
            // Warnings should be user-understandable
            assertFalse(warning.contains("0x"))
            assertFalse(warning.contains("null pointer"))
        }
    }

    @Test
    fun biometricSecurity_integrationWorks() {
        // Test biometric security integration
        
        // Should be able to store biometric preference securely
        val biometricEnabled = securityManager.storeSecureData("biometric_enabled", "true")
        assertTrue(biometricEnabled)
        
        // Should be able to retrieve preference
        val preference = securityManager.getSecureData("biometric_enabled")
        assertEquals("true", preference)
        
        // Should handle biometric unavailability gracefully
        val fallbackStored = securityManager.storeSecureData("fallback_auth", "pin")
        assertTrue(fallbackStored)
    }

    @Test
    fun dataRetention_followsPrivacyLaws() {
        // Test data retention and deletion capabilities
        
        // Should be able to store data with metadata
        val userData = "user_financial_data"
        val stored = securityManager.storeSecureData("user_data", userData)
        assertTrue(stored)
        
        // Should be able to completely remove user data
        val removed = securityManager.removeSecureData("user_data")
        assertTrue(removed)
        
        // Data should be completely gone
        val afterRemoval = securityManager.getSecureData("user_data")
        assertEquals(null, afterRemoval)
        
        // Should be able to clear all data (GDPR right to erasure)
        securityManager.storeSecureData("test1", "data1")
        securityManager.storeSecureData("test2", "data2")
        
        val allCleared = securityManager.clearAllSecureData()
        assertTrue(allCleared)
        
        assertEquals(null, securityManager.getSecureData("test1"))
        assertEquals(null, securityManager.getSecureData("test2"))
    }
}
