package com.focusflow.ui.components.help

import androidx.compose.animation.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.focusflow.data.model.*
import com.focusflow.ui.theme.*

/**
 * ADHD-friendly help system UI components
 * Designed for cognitive load reduction and clear visual hierarchy
 */

@Composable
fun HelpCategoryCard(
    category: HelpCategory,
    articleCount: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .semantics {
                contentDescription = "${category.title} category with $articleCount articles"
                role = Role.Button
            },
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(android.graphics.Color.parseColor(category.color)).copy(alpha = 0.1f)
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Category icon
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color(android.graphics.Color.parseColor(category.color)).copy(alpha = 0.2f)),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = getCategoryIcon(category.icon),
                    contentDescription = null,
                    tint = Color(android.graphics.Color.parseColor(category.color)),
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Category content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = category.title,
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colors.onSurface
                )
                
                Text(
                    text = category.description,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                if (category.adhdTips != null) {
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Psychology,
                            contentDescription = "ADHD tip",
                            tint = ADHDFriendlyColors.focusBlue,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "ADHD-optimized",
                            style = MaterialTheme.typography.caption,
                            color = ADHDFriendlyColors.focusBlue,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // Article count and arrow
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "$articleCount",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold,
                    color = Color(android.graphics.Color.parseColor(category.color))
                )
                Text(
                    text = if (articleCount == 1) "article" else "articles",
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "Open category",
                    tint = MaterialTheme.colors.onSurface.copy(alpha = 0.4f)
                )
            }
        }
    }
}

@Composable
fun HelpArticleCard(
    article: HelpArticle,
    isCompleted: Boolean,
    isBookmarked: Boolean,
    onArticleClick: () -> Unit,
    onBookmarkClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onArticleClick() }
            .semantics {
                contentDescription = "${article.title}. ${if (isCompleted) "Completed" else "Not completed"}. ${article.estimatedReadTime} minute read."
                role = Role.Button
            },
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                // Content type badge
                Surface(
                    color = getContentTypeColor(article.contentType),
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier.semantics {
                        contentDescription = "Content type: ${article.contentType.name}"
                    }
                ) {
                    Text(
                        text = getContentTypeLabel(article.contentType),
                        style = MaterialTheme.typography.caption,
                        color = Color.White,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // Bookmark button
                IconButton(
                    onClick = onBookmarkClick,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = if (isBookmarked) Icons.Default.Bookmark else Icons.Default.BookmarkBorder,
                        contentDescription = if (isBookmarked) "Remove bookmark" else "Add bookmark",
                        tint = if (isBookmarked) ADHDFriendlyColors.successGreen else MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Title and subtitle
            Text(
                text = article.title,
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colors.onSurface,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            if (article.subtitle != null) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = article.subtitle,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Footer row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Reading time and difficulty
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "Reading time",
                        tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${article.estimatedReadTime} min",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    DifficultyIndicator(
                        difficulty = article.difficulty,
                        modifier = Modifier.semantics {
                            contentDescription = "Difficulty: ${article.difficulty.name}"
                        }
                    )
                }
                
                // Completion status
                if (isCompleted) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "Completed",
                            tint = ADHDFriendlyColors.successGreen,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "Completed",
                            style = MaterialTheme.typography.caption,
                            color = ADHDFriendlyColors.successGreen,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
            
            // ADHD tips indicator
            if (article.adhdFriendlyTips.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Psychology,
                        contentDescription = "Contains ADHD tips",
                        tint = ADHDFriendlyColors.focusBlue,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${article.adhdFriendlyTips.size} ADHD-specific tips included",
                        style = MaterialTheme.typography.caption,
                        color = ADHDFriendlyColors.focusBlue,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
fun DifficultyIndicator(
    difficulty: DifficultyLevel,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (difficulty) {
        DifficultyLevel.BEGINNER -> ADHDFriendlyColors.successGreen to "Beginner"
        DifficultyLevel.INTERMEDIATE -> ADHDFriendlyColors.warningOrange to "Intermediate"
        DifficultyLevel.ADVANCED -> ADHDFriendlyColors.urgentRed to "Advanced"
    }
    
    Surface(
        color = color.copy(alpha = 0.1f),
        shape = RoundedCornerShape(4.dp),
        modifier = modifier
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.caption,
            color = color,
            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp),
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun HelpSearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onSearch: () -> Unit,
    placeholder: String = "Search help articles...",
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        placeholder = {
            Text(
                text = placeholder,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
            )
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.Default.Search,
                contentDescription = "Search",
                tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
            )
        },
        trailingIcon = if (query.isNotEmpty()) {
            {
                IconButton(onClick = { onQueryChange("") }) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "Clear search",
                        tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        } else null,
        singleLine = true,
        shape = RoundedCornerShape(12.dp),
        colors = TextFieldDefaults.outlinedTextFieldColors(
            backgroundColor = MaterialTheme.colors.surface,
            focusedBorderColor = ADHDFriendlyColors.focusBlue,
            unfocusedBorderColor = MaterialTheme.colors.onSurface.copy(alpha = 0.2f)
        ),
        modifier = modifier
            .fillMaxWidth()
            .semantics {
                contentDescription = "Search help articles and FAQs"
            }
    )
}

@Composable
fun QuickTipCard(
    tip: HelpQuickTip,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(android.graphics.Color.parseColor(tip.color)).copy(alpha = 0.1f)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = getTipIcon(tip.icon),
                        contentDescription = null,
                        tint = Color(android.graphics.Color.parseColor(tip.color)),
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = tip.title,
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colors.onSurface
                    )
                }
                
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Dismiss tip",
                        tint = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = tip.content,
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f),
                lineHeight = 20.sp
            )
            
            if (tip.isADHDSpecific) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Psychology,
                        contentDescription = "ADHD-specific tip",
                        tint = ADHDFriendlyColors.focusBlue,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "ADHD-specific tip",
                        style = MaterialTheme.typography.caption,
                        color = ADHDFriendlyColors.focusBlue,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
fun HelpEmptyState(
    title: String,
    description: String,
    icon: ImageVector,
    actionText: String? = null,
    onActionClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colors.onSurface.copy(alpha = 0.4f),
            modifier = Modifier.size(64.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = title,
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = description,
            style = MaterialTheme.typography.body2,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
            textAlign = TextAlign.Center,
            lineHeight = 20.sp
        )
        
        if (actionText != null && onActionClick != null) {
            Spacer(modifier = Modifier.height(16.dp))
            
            Button(
                onClick = onActionClick,
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = ADHDFriendlyColors.focusBlue
                )
            ) {
                Text(
                    text = actionText,
                    color = Color.White
                )
            }
        }
    }
}

// Helper functions
private fun getCategoryIcon(iconName: String): ImageVector {
    return when (iconName) {
        "budget" -> Icons.Default.AccountBalance
        "spending" -> Icons.Default.ShoppingCart
        "debt" -> Icons.Default.CreditCard
        "tasks" -> Icons.Default.Task
        "focus" -> Icons.Default.Psychology
        "habits" -> Icons.Default.Repeat
        "settings" -> Icons.Default.Settings
        else -> Icons.Default.Help
    }
}

private fun getTipIcon(iconName: String): ImageVector {
    return when (iconName) {
        "lightbulb" -> Icons.Default.Lightbulb
        "star" -> Icons.Default.Star
        "psychology" -> Icons.Default.Psychology
        "tips" -> Icons.Default.TipsAndUpdates
        else -> Icons.Default.Info
    }
}

private fun getContentTypeColor(contentType: HelpContentType): Color {
    return when (contentType) {
        HelpContentType.QUICK_START -> ADHDFriendlyColors.successGreen
        HelpContentType.HOW_TO_GUIDE -> ADHDFriendlyColors.focusBlue
        HelpContentType.TROUBLESHOOTING -> ADHDFriendlyColors.warningOrange
        HelpContentType.BEST_PRACTICES -> ADHDFriendlyColors.motivationPurple
        HelpContentType.ADHD_TIPS -> ADHDFriendlyColors.calmTeal
        HelpContentType.FAQ -> MaterialTheme.colors.primary
        HelpContentType.VIDEO_TUTORIAL -> ADHDFriendlyColors.energyYellow
        HelpContentType.INTERACTIVE_DEMO -> ADHDFriendlyColors.focusBlue
        HelpContentType.FEATURE_OVERVIEW -> MaterialTheme.colors.secondary
    }
}

private fun getContentTypeLabel(contentType: HelpContentType): String {
    return when (contentType) {
        HelpContentType.QUICK_START -> "Quick Start"
        HelpContentType.HOW_TO_GUIDE -> "How To"
        HelpContentType.TROUBLESHOOTING -> "Troubleshooting"
        HelpContentType.BEST_PRACTICES -> "Best Practices"
        HelpContentType.ADHD_TIPS -> "ADHD Tips"
        HelpContentType.FAQ -> "FAQ"
        HelpContentType.VIDEO_TUTORIAL -> "Video"
        HelpContentType.INTERACTIVE_DEMO -> "Interactive"
        HelpContentType.FEATURE_OVERVIEW -> "Overview"
    }
}
