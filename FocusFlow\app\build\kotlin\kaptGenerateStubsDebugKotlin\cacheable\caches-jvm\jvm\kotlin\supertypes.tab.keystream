"com.focusflow.FocusFlowApplicationcom.focusflow.MainActivity-com.focusflow.data.database.FocusFlowDatabase(com.focusflow.data.model.HelpContentType(com.focusflow.data.model.DifficultyLevel%com.focusflow.data.model.TipFrequencycom.focusflow.di.MockAIcom.focusflow.di.RealAI)com.focusflow.navigation.Screen.Dashboard(com.focusflow.navigation.Screen.Expenses$com.focusflow.navigation.Screen.Debt&com.focusflow.navigation.Screen.Budget&com.focusflow.navigation.Screen.Habits%com.focusflow.navigation.Screen.Tasks'com.focusflow.navigation.Screen.AICoach(com.focusflow.navigation.Screen.Settings-com.focusflow.navigation.Screen.PayoffPlanner$com.focusflow.receiver.AlarmReceiver1com.focusflow.security.DataValidationResult.Valid3com.focusflow.security.DataValidationResult.Invalidcom.focusflow.security.DataType1com.focusflow.security.SecurityInitResult.Success>com.focusflow.security.SecurityInitResult.EncryptionTestFailed>com.focusflow.security.SecurityInitResult.InitializationFailed#com.focusflow.service.MockAIService!com.focusflow.service.InsightType%com.focusflow.service.ClaudeAIService com.focusflow.service.TimerState.com.focusflow.service.DataExportResult.Success,com.focusflow.service.DataExportResult.Error0com.focusflow.service.DataDeletionResult.Success.com.focusflow.service.DataDeletionResult.Error"com.focusflow.service.DataCategory(com.focusflow.service.CacheCleanupWorker/com.focusflow.service.AnalyticsPrecomputeWorker,com.focusflow.service.ValidationResult.Valid.com.focusflow.service.ValidationResult.Invalid*com.focusflow.ui.components.ADHDButtonType+com.focusflow.ui.components.ADHDButtonStyle(com.focusflow.ui.components.ADHDPriority&com.focusflow.ui.components.ADHDStatus)com.focusflow.ui.components.ADHDBadgeSize+com.focusflow.ui.components.VoiceButtonSize.com.focusflow.ui.onboarding.OnboardingActivity>com.focusflow.ui.responsive.ResponsiveLayoutManager.ScreenSize?com.focusflow.ui.responsive.ResponsiveLayoutManager.Orientation>com.focusflow.ui.responsive.ResponsiveLayoutManager.DeviceType!com.focusflow.ui.screens.ViewMode'com.focusflow.ui.screens.PayoffGoalType com.focusflow.ui.theme.ThemeMode;com.focusflow.ui.validation.ADHDDesignAuditor.IssueSeverityDcom.focusflow.ui.validation.ADHDDesignAuditor.RecommendationPriority;com.focusflow.ui.validation.ADHDDesignAuditor.IssueCategory9com.focusflow.ui.validation.ADHDDesignAuditor.ElementType<com.focusflow.ui.validation.ADHDDesignReport.ComplianceLevel+com.focusflow.ui.viewmodel.AICoachViewModel*com.focusflow.ui.viewmodel.BudgetViewModel-com.focusflow.ui.viewmodel.DashboardViewModel(com.focusflow.ui.viewmodel.DebtViewModel)com.focusflow.ui.viewmodel.PayoffStrategy2com.focusflow.ui.viewmodel.EnhancedBudgetViewModel+com.focusflow.ui.viewmodel.ExpenseViewModel-com.focusflow.ui.viewmodel.FocusModeViewModel)com.focusflow.ui.viewmodel.HabitViewModel2com.focusflow.ui.viewmodel.ImpulseControlViewModel(com.focusflow.ui.viewmodel.MainViewModel.com.focusflow.ui.viewmodel.OnboardingViewModel)com.focusflow.ui.viewmodel.OnboardingStep1com.focusflow.ui.viewmodel.PayoffPlannerViewModel,com.focusflow.ui.viewmodel.SettingsViewModel(com.focusflow.ui.viewmodel.TaskViewModel%com.focusflow.ui.viewmodel.TaskFilter.com.focusflow.ui.viewmodel.VirtualPetViewModel.com.focusflow.ui.viewmodel.ZeroBudgetViewModel$com.focusflow.utils.PasswordStrength%com.focusflow.viewmodel.HelpViewModel)com.focusflow.navigation.Screen.FocusMode                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       