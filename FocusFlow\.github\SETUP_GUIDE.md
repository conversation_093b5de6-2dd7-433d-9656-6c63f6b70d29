# FocusFlow CI/CD Setup Guide

## Overview

This guide provides step-by-step instructions for setting up the complete CI/CD pipeline for FocusFlow, including Android keystore generation, Google Play Console service account creation, and GitHub repository secret configuration.

## Prerequisites

- **Android Studio** installed with Android SDK
- **Java Development Kit (JDK)** 8 or higher
- **Google Play Console** developer account
- **GitHub repository** with admin access
- **Command line access** (Terminal/Command Prompt)

## Part 1: Generate Android Keystore

### Step 1: Create Production Keystore

Open terminal/command prompt and run:

```bash
keytool -genkey -v -keystore focusflow-release.keystore -alias focusflow-release-key -keyalg RSA -keysize 2048 -validity 10000
```

### Step 2: Provide Keystore Information

When prompted, enter the following information:

```
Enter keystore password: [Create strong password - save this as SIGNING_STORE_PASSWORD]
Re-enter new password: [Confirm password]
What is your first and last name?
  [CN]: FocusFlow Development Team
What is the name of your organizational unit?
  [OU]: Mobile Development
What is the name of your organization?
  [O]: FocusFlow
What is the name of your City or Locality?
  [L]: [Your City]
What is the name of your State or Province?
  [ST]: [Your State/Province]
What is the two-letter country code for this unit?
  [C]: [Your Country Code]
Is CN=FocusFlow Development Team, OU=Mobile Development, O=FocusFlow, L=[City], ST=[State], C=[Country] correct?
  [no]: yes

Enter key password for <focusflow-release-key>
  (RETURN if same as keystore password): [Create different password - save this as SIGNING_KEY_PASSWORD]
```

### Step 3: Secure the Keystore

```bash
# Move keystore to secure location
mkdir -p ~/.android/keystores
mv focusflow-release.keystore ~/.android/keystores/

# Set restrictive permissions (Linux/macOS)
chmod 600 ~/.android/keystores/focusflow-release.keystore

# Backup keystore to secure location
cp ~/.android/keystores/focusflow-release.keystore /path/to/secure/backup/
```

### Step 4: Convert Keystore to Base64

```bash
# Linux/macOS
base64 -i ~/.android/keystores/focusflow-release.keystore | tr -d '\n' > keystore_base64.txt

# Windows (PowerShell)
[Convert]::ToBase64String([IO.File]::ReadAllBytes("C:\path\to\focusflow-release.keystore")) | Out-File -Encoding ASCII keystore_base64.txt
```

**Save the contents of `keystore_base64.txt` as the `KEYSTORE_BASE64` secret.**

## Part 2: Create Google Play Console Service Account

### Step 1: Access Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing project
3. Enable the **Google Play Android Developer API**

### Step 2: Create Service Account

1. Navigate to **IAM & Admin** > **Service Accounts**
2. Click **Create Service Account**
3. Fill in details:
   ```
   Service account name: focusflow-play-console
   Service account ID: focusflow-play-console
   Description: Service account for FocusFlow Google Play Console API access
   ```
4. Click **Create and Continue**

### Step 3: Generate Service Account Key

1. Click on the created service account
2. Go to **Keys** tab
3. Click **Add Key** > **Create new key**
4. Select **JSON** format
5. Click **Create** and download the JSON file

### Step 4: Configure Google Play Console Access

1. Go to [Google Play Console](https://play.google.com/console/)
2. Navigate to **Setup** > **API access**
3. Click **Link** next to your Google Cloud project
4. Find your service account and click **Grant access**
5. Set permissions:
   ```
   Account permissions:
   - View app information and download bulk reports (read-only)
   
   App permissions:
   - Edit and delete draft apps
   - Manage production releases
   - Manage testing track releases
   ```
6. Click **Invite user**

### Step 5: Prepare Service Account JSON

```bash
# Remove all whitespace and newlines from JSON file
cat downloaded-service-account.json | jq -c . > service_account_compact.json
```

**Save the contents of `service_account_compact.json` as the `GOOGLE_PLAY_SERVICE_ACCOUNT_JSON` secret.**

## Part 3: Configure GitHub Repository Secrets

### Step 1: Access Repository Settings

1. Go to your GitHub repository
2. Click **Settings** tab
3. Navigate to **Secrets and variables** > **Actions**

### Step 2: Add Required Secrets

Click **New repository secret** for each of the following:

#### Android Signing Secrets

**Secret Name**: `KEYSTORE_BASE64`
**Value**: Contents of `keystore_base64.txt` file
```
[Paste the base64 encoded keystore content here]
```

**Secret Name**: `SIGNING_KEY_ALIAS`
**Value**: `focusflow-release-key`

**Secret Name**: `SIGNING_KEY_PASSWORD`
**Value**: Password you created for the signing key
```
[Your signing key password]
```

**Secret Name**: `SIGNING_STORE_PASSWORD`
**Value**: Password you created for the keystore
```
[Your keystore password]
```

#### Google Play Console Secret

**Secret Name**: `GOOGLE_PLAY_SERVICE_ACCOUNT_JSON`
**Value**: Contents of `service_account_compact.json`
```
{"type":"service_account","project_id":"...","private_key_id":"..."}
```

### Step 3: Verify Secret Configuration

After adding all secrets, you should see:
- ✅ KEYSTORE_BASE64
- ✅ SIGNING_KEY_ALIAS  
- ✅ SIGNING_KEY_PASSWORD
- ✅ SIGNING_STORE_PASSWORD
- ✅ GOOGLE_PLAY_SERVICE_ACCOUNT_JSON

## Part 4: Test the Setup

### Step 1: Trigger Workflow

1. Create a new release tag:
   ```bash
   git tag v1.0.0-alpha
   git push origin v1.0.0-alpha
   ```

2. Or manually trigger the workflow:
   - Go to **Actions** tab
   - Select **FocusFlow Android CI/CD**
   - Click **Run workflow**

### Step 2: Monitor Build Process

1. Watch the workflow execution in the **Actions** tab
2. Verify each job completes successfully:
   - ✅ Unit Tests
   - ✅ Lint Check
   - ✅ Security Scan
   - ✅ Build Debug APK
   - ✅ UI Tests
   - ✅ Build Release (if triggered by release)
   - ✅ Deploy (if configured)

### Step 3: Troubleshoot Issues

If any step fails, check the logs for specific error messages and refer to the troubleshooting section in `SECRETS_DOCUMENTATION.md`.

## Security Best Practices

### Password Management
- **Use unique passwords** for keystore and signing key
- **Store passwords securely** in a password manager
- **Never commit passwords** to version control
- **Rotate passwords annually**

### Keystore Security
- **Backup keystore** to multiple secure locations
- **Restrict file permissions** (600 on Unix systems)
- **Never lose the keystore** - it cannot be recovered
- **Document keystore location** for team access

### Service Account Security
- **Limit permissions** to minimum required
- **Monitor API usage** in Google Cloud Console
- **Rotate service account keys** annually
- **Review access logs** regularly

### GitHub Security
- **Limit repository access** to necessary team members
- **Use branch protection** rules for main branch
- **Enable two-factor authentication** for all team members
- **Review secret access** in audit logs

## Maintenance Procedures

### Monthly Tasks
- [ ] Review workflow execution logs
- [ ] Check for failed builds or deployments
- [ ] Monitor Google Play Console API usage
- [ ] Verify all secrets are still functional

### Quarterly Tasks
- [ ] Test complete CI/CD pipeline end-to-end
- [ ] Review and update service account permissions
- [ ] Audit repository access and secret usage
- [ ] Update documentation if needed

### Annual Tasks
- [ ] Rotate all passwords and regenerate keystore if needed
- [ ] Update service account keys
- [ ] Review and update security policies
- [ ] Backup verification and recovery testing

## Emergency Procedures

### Compromised Keystore
1. **Immediately revoke** the compromised keystore
2. **Generate new keystore** following this guide
3. **Update all secrets** in GitHub repository
4. **Notify Google Play Console** if necessary
5. **Document incident** for future reference

### Lost Service Account Access
1. **Create new service account** following Part 2
2. **Update Google Play Console** permissions
3. **Replace secret** in GitHub repository
4. **Test deployment** to verify functionality
5. **Revoke old service account** access

### GitHub Security Incident
1. **Rotate all secrets** immediately
2. **Review access logs** for unauthorized usage
3. **Update passwords** and service account keys
4. **Notify team members** of security incident
5. **Implement additional security measures**

## Support and Contact

### Documentation Issues
- **Create GitHub issue** for documentation problems
- **Submit pull request** for corrections or improvements

### Technical Support
- **Check troubleshooting section** in SECRETS_DOCUMENTATION.md
- **Review workflow logs** for specific error messages
- **Contact repository administrators** for access issues

### Security Concerns
- **Report immediately** to repository administrators
- **Do not share details** in public channels
- **Follow incident response procedures**

---

**⚠️ Important**: Keep this setup information secure and do not share keystore passwords or service account keys. Always follow security best practices when handling production credentials.
