package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.VirtualPetRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VirtualPetViewModel_Factory implements Factory<VirtualPetViewModel> {
  private final Provider<VirtualPetRepository> virtualPetRepositoryProvider;

  public VirtualPetViewModel_Factory(Provider<VirtualPetRepository> virtualPetRepositoryProvider) {
    this.virtualPetRepositoryProvider = virtualPetRepositoryProvider;
  }

  @Override
  public VirtualPetViewModel get() {
    return newInstance(virtualPetRepositoryProvider.get());
  }

  public static VirtualPetViewModel_Factory create(
      Provider<VirtualPetRepository> virtualPetRepositoryProvider) {
    return new VirtualPetViewModel_Factory(virtualPetRepositoryProvider);
  }

  public static VirtualPetViewModel newInstance(VirtualPetRepository virtualPetRepository) {
    return new VirtualPetViewModel(virtualPetRepository);
  }
}
