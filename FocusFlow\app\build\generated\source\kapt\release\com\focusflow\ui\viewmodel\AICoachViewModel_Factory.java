package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.CreditCardRepository;
import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.service.AIService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AICoachViewModel_Factory implements Factory<AICoachViewModel> {
  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<CreditCardRepository> creditCardRepositoryProvider;

  private final Provider<AIService> aiServiceProvider;

  public AICoachViewModel_Factory(
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<AIService> aiServiceProvider) {
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.creditCardRepositoryProvider = creditCardRepositoryProvider;
    this.aiServiceProvider = aiServiceProvider;
  }

  @Override
  public AICoachViewModel get() {
    return newInstance(userPreferencesRepositoryProvider.get(), expenseRepositoryProvider.get(), creditCardRepositoryProvider.get(), aiServiceProvider.get());
  }

  public static AICoachViewModel_Factory create(
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<AIService> aiServiceProvider) {
    return new AICoachViewModel_Factory(userPreferencesRepositoryProvider, expenseRepositoryProvider, creditCardRepositoryProvider, aiServiceProvider);
  }

  public static AICoachViewModel newInstance(UserPreferencesRepository userPreferencesRepository,
      ExpenseRepository expenseRepository, CreditCardRepository creditCardRepository,
      AIService aiService) {
    return new AICoachViewModel(userPreferencesRepository, expenseRepository, creditCardRepository, aiService);
  }
}
