package com.focusflow.service;

import android.content.Context;
import com.focusflow.data.repository.BudgetCategoryRepository;
import com.focusflow.data.repository.CreditCardRepository;
import com.focusflow.data.repository.ExpenseRepository;
import com.focusflow.data.repository.HabitRepository;
import com.focusflow.data.repository.TaskRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.data.repository.WishlistRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GDPRComplianceManager_Factory implements Factory<GDPRComplianceManager> {
  private final Provider<Context> contextProvider;

  private final Provider<SecurityManager> securityManagerProvider;

  private final Provider<CrashReportingManager> crashReportingManagerProvider;

  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<ExpenseRepository> expenseRepositoryProvider;

  private final Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider;

  private final Provider<CreditCardRepository> creditCardRepositoryProvider;

  private final Provider<TaskRepository> taskRepositoryProvider;

  private final Provider<HabitRepository> habitRepositoryProvider;

  private final Provider<WishlistRepository> wishlistRepositoryProvider;

  public GDPRComplianceManager_Factory(Provider<Context> contextProvider,
      Provider<SecurityManager> securityManagerProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<WishlistRepository> wishlistRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.securityManagerProvider = securityManagerProvider;
    this.crashReportingManagerProvider = crashReportingManagerProvider;
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.expenseRepositoryProvider = expenseRepositoryProvider;
    this.budgetCategoryRepositoryProvider = budgetCategoryRepositoryProvider;
    this.creditCardRepositoryProvider = creditCardRepositoryProvider;
    this.taskRepositoryProvider = taskRepositoryProvider;
    this.habitRepositoryProvider = habitRepositoryProvider;
    this.wishlistRepositoryProvider = wishlistRepositoryProvider;
  }

  @Override
  public GDPRComplianceManager get() {
    return newInstance(contextProvider.get(), securityManagerProvider.get(), crashReportingManagerProvider.get(), userPreferencesRepositoryProvider.get(), expenseRepositoryProvider.get(), budgetCategoryRepositoryProvider.get(), creditCardRepositoryProvider.get(), taskRepositoryProvider.get(), habitRepositoryProvider.get(), wishlistRepositoryProvider.get());
  }

  public static GDPRComplianceManager_Factory create(Provider<Context> contextProvider,
      Provider<SecurityManager> securityManagerProvider,
      Provider<CrashReportingManager> crashReportingManagerProvider,
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<ExpenseRepository> expenseRepositoryProvider,
      Provider<BudgetCategoryRepository> budgetCategoryRepositoryProvider,
      Provider<CreditCardRepository> creditCardRepositoryProvider,
      Provider<TaskRepository> taskRepositoryProvider,
      Provider<HabitRepository> habitRepositoryProvider,
      Provider<WishlistRepository> wishlistRepositoryProvider) {
    return new GDPRComplianceManager_Factory(contextProvider, securityManagerProvider, crashReportingManagerProvider, userPreferencesRepositoryProvider, expenseRepositoryProvider, budgetCategoryRepositoryProvider, creditCardRepositoryProvider, taskRepositoryProvider, habitRepositoryProvider, wishlistRepositoryProvider);
  }

  public static GDPRComplianceManager newInstance(Context context, SecurityManager securityManager,
      CrashReportingManager crashReportingManager,
      UserPreferencesRepository userPreferencesRepository, ExpenseRepository expenseRepository,
      BudgetCategoryRepository budgetCategoryRepository, CreditCardRepository creditCardRepository,
      TaskRepository taskRepository, HabitRepository habitRepository,
      WishlistRepository wishlistRepository) {
    return new GDPRComplianceManager(context, securityManager, crashReportingManager, userPreferencesRepository, expenseRepository, budgetCategoryRepository, creditCardRepository, taskRepository, habitRepository, wishlistRepository);
  }
}
