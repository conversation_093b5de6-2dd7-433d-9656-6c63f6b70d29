package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000J\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\\\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00072\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00072\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u001a(\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u00042\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u000e\u001a\u00020\u000fH\u0007\u001a*\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u0014H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0015\u0010\u0016\u001a\u001a\u0010\u0017\u001a\u00020\u00012\b\u0010\u0018\u001a\u0004\u0018\u00010\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0007\u001a\"\u0010\u001c\u001a\u00020\u00012\b\b\u0002\u0010\u001d\u001a\u00020\u001e2\u000e\b\u0002\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\nH\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006 "}, d2 = {"ADHDQuickActionsCard", "", "recommendedTasks", "", "Lcom/focusflow/data/model/Task;", "overdueTasks", "onTaskClick", "Lkotlin/Function1;", "onBreakdownClick", "onFocusModeClick", "Lkotlin/Function0;", "QuickTaskItem", "task", "onClick", "actionText", "", "StatisticItem", "label", "value", "color", "Landroidx/compose/ui/graphics/Color;", "StatisticItem-mxwnekA", "(Ljava/lang/String;Ljava/lang/String;J)V", "TaskHeaderCard", "statistics", "Lcom/focusflow/data/repository/TaskStatistics;", "isLoading", "", "TaskScreen", "viewModel", "Lcom/focusflow/ui/viewmodel/TaskViewModel;", "onNavigateToFocusMode", "app_debug"})
public final class TaskScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void TaskScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.TaskViewModel viewModel, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToFocusMode) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TaskHeaderCard(@org.jetbrains.annotations.Nullable
    com.focusflow.data.repository.TaskStatistics statistics, boolean isLoading) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ADHDQuickActionsCard(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Task> recommendedTasks, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.Task> overdueTasks, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.Task, kotlin.Unit> onTaskClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.data.model.Task, kotlin.Unit> onBreakdownClick, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onFocusModeClick) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void QuickTaskItem(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull
    java.lang.String actionText) {
    }
}