package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u001b\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bs\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u0012\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J\u000f\u0010 \u001a\b\u0012\u0004\u0012\u00020\n0\tH\u00c6\u0003J\u000f\u0010!\u001a\b\u0012\u0004\u0012\u00020\n0\tH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\nH\u00c6\u0003Jw\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\u000e\b\u0002\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\nH\u00c6\u0001J\u0013\u0010&\u001a\u00020\u00032\b\u0010\'\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010(\u001a\u00020)H\u00d6\u0001J\t\u0010*\u001a\u00020\nH\u00d6\u0001R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0015R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0013\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001b\u00a8\u0006+"}, d2 = {"Lcom/focusflow/ui/viewmodel/TaskUiState;", "", "isLoading", "", "isCreating", "isBreakingDownTask", "taskStatistics", "Lcom/focusflow/data/repository/TaskStatistics;", "availableCategories", "", "", "suggestedSubtasks", "taskBeingBrokenDown", "Lcom/focusflow/data/model/Task;", "lastAction", "error", "(ZZZLcom/focusflow/data/repository/TaskStatistics;Ljava/util/List;Ljava/util/List;Lcom/focusflow/data/model/Task;Ljava/lang/String;Ljava/lang/String;)V", "getAvailableCategories", "()Ljava/util/List;", "getError", "()Ljava/lang/String;", "()Z", "getLastAction", "getSuggestedSubtasks", "getTaskBeingBrokenDown", "()Lcom/focusflow/data/model/Task;", "getTaskStatistics", "()Lcom/focusflow/data/repository/TaskStatistics;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class TaskUiState {
    private final boolean isLoading = false;
    private final boolean isCreating = false;
    private final boolean isBreakingDownTask = false;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.repository.TaskStatistics taskStatistics = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> availableCategories = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<java.lang.String> suggestedSubtasks = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.data.model.Task taskBeingBrokenDown = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String lastAction = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    
    public TaskUiState(boolean isLoading, boolean isCreating, boolean isBreakingDownTask, @org.jetbrains.annotations.Nullable
    com.focusflow.data.repository.TaskStatistics taskStatistics, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> availableCategories, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> suggestedSubtasks, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.Task taskBeingBrokenDown, @org.jetbrains.annotations.Nullable
    java.lang.String lastAction, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isCreating() {
        return false;
    }
    
    public final boolean isBreakingDownTask() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.repository.TaskStatistics getTaskStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getAvailableCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getSuggestedSubtasks() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.Task getTaskBeingBrokenDown() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getLastAction() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    public TaskUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.repository.TaskStatistics component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.data.model.Task component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.TaskUiState copy(boolean isLoading, boolean isCreating, boolean isBreakingDownTask, @org.jetbrains.annotations.Nullable
    com.focusflow.data.repository.TaskStatistics taskStatistics, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> availableCategories, @org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> suggestedSubtasks, @org.jetbrains.annotations.Nullable
    com.focusflow.data.model.Task taskBeingBrokenDown, @org.jetbrains.annotations.Nullable
    java.lang.String lastAction, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}