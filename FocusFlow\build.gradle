buildscript {
    ext {
        compose_version = '1.5.4'
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.10.1'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.20'
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.48'
        classpath 'org.jetbrains.kotlin:kotlin-serialization:1.9.20'
        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath 'com.google.firebase:perf-plugin:1.4.2'
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}


