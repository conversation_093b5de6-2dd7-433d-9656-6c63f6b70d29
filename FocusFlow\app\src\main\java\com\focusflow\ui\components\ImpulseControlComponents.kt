package com.focusflow.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.focusflow.ui.viewmodel.BudgetImpactPreview
import kotlinx.coroutines.delay

@Composable
fun ImpulseControlDialog(
    amount: Double,
    category: String,
    onConfirm: () -> Unit,
    onCancel: () -> Unit,
    onAddToWishlist: () -> Unit,
    onDelay: () -> Unit,
    budgetImpact: BudgetImpactPreview? = null
) {
    var timeRemaining by remember { mutableStateOf(10) }
    var isDelayActive by remember { mutableStateOf(false) }

    LaunchedEffect(isDelayActive) {
        if (isDelayActive) {
            while (timeRemaining > 0) {
                delay(1000)
                timeRemaining--
            }
            isDelayActive = false
            timeRemaining = 10
        }
    }

    Dialog(onDismissRequest = onCancel) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            elevation = 8.dp
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    modifier = Modifier.size(48.dp),
                    tint = Color(0xFFFF9800)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "Hold on! 🤔",
                    style = MaterialTheme.typography.h5,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "You're about to spend $${String.format("%.2f", amount)} on $category",
                    style = MaterialTheme.typography.body1,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(16.dp))

                // Budget impact preview
                budgetImpact?.let { impact ->
                    BudgetImpactCard(budgetImpact = impact)
                    Spacer(modifier = Modifier.height(16.dp))
                }

                // Impulse control questions
                ImpulseControlQuestions(amount = amount)

                Spacer(modifier = Modifier.height(24.dp))
                
                if (isDelayActive) {
                    // Delay countdown
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            progress = (10 - timeRemaining) / 10f,
                            modifier = Modifier.size(48.dp),
                            color = MaterialTheme.colors.primary
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Take a moment to think... $timeRemaining",
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                } else {
                    // Action buttons
                    Column {
                        Button(
                            onClick = onConfirm,
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(
                                backgroundColor = MaterialTheme.colors.primary
                            )
                        ) {
                            Text("Yes, I need this")
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        OutlinedButton(
                            onClick = {
                                isDelayActive = true
                                timeRemaining = 10
                            },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(Icons.Default.Star, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Wait 10 seconds")
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))

                        OutlinedButton(
                            onClick = onAddToWishlist,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Icon(Icons.Default.FavoriteBorder, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Add to Wishlist")
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        TextButton(
                            onClick = onCancel,
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("Cancel purchase")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun ImpulseControlQuestions(amount: Double) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        backgroundColor = MaterialTheme.colors.surface,
        elevation = 2.dp,
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Quick check:",
                style = MaterialTheme.typography.subtitle2,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            val questions = listOf(
                "Do I really need this right now?",
                "Will I still want this tomorrow?",
                "Do I have something similar already?",
                "Is this within my budget?"
            )
            
            questions.forEach { question ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colors.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = question,
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f)
                    )
                }
                Spacer(modifier = Modifier.height(4.dp))
            }
        }
    }
}

@Composable
fun SpendingWatchlistCard(
    onAddToWatchlist: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(0xFFFFF3E0)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Add,
                    contentDescription = null,
                    tint = Color(0xFFFF9800)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Spending Watchlist",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Add items you're considering buying. Come back in 24-48 hours to see if you still want them.",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Button(
                onClick = onAddToWatchlist,
                colors = ButtonDefaults.buttonColors(
                    backgroundColor = Color(0xFFFF9800)
                ),
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(Icons.Default.Add, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Add to Watchlist")
            }
        }
    }
}

@Composable
fun BudgetWarningCard(
    remainingBudget: Double,
    expenseAmount: Double
) {
    val willExceedBudget = expenseAmount > remainingBudget
    
    if (willExceedBudget || remainingBudget < 50) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = 4.dp,
            shape = RoundedCornerShape(12.dp),
            backgroundColor = if (willExceedBudget) Color(0xFFFFEBEE) else Color(0xFFFFF3E0)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        if (willExceedBudget) Icons.Default.Warning else Icons.Default.Info,
                        contentDescription = null,
                        tint = if (willExceedBudget) Color(0xFFF44336) else Color(0xFFFF9800)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (willExceedBudget) "Budget Alert!" else "Budget Warning",
                        style = MaterialTheme.typography.subtitle1,
                        fontWeight = FontWeight.Bold,
                        color = if (willExceedBudget) Color(0xFFF44336) else Color(0xFFFF9800)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = if (willExceedBudget) {
                        "This purchase will put you $${String.format("%.2f", expenseAmount - remainingBudget)} over budget."
                    } else {
                        "You only have $${String.format("%.2f", remainingBudget)} left in your budget."
                    },
                    style = MaterialTheme.typography.body2
                )
                
                if (willExceedBudget) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "💡 Consider waiting until next week or adjusting your budget.",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
fun CooldownPeriodCard(
    hoursRemaining: Int,
    onRemoveFromCooldown: () -> Unit,
    onExtendDelay: () -> Unit = {}
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(0xFFE3F2FD)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Star,
                        contentDescription = null,
                        tint = Color(0xFF2196F3)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Column {
                        Text(
                            text = "Cooling Down",
                            style = MaterialTheme.typography.subtitle1,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "$hoursRemaining hours remaining",
                            style = MaterialTheme.typography.body2,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }

                Row {
                    TextButton(onClick = onExtendDelay) {
                        Text("Extend")
                    }
                    TextButton(onClick = onRemoveFromCooldown) {
                        Text("Remove")
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            LinearProgressIndicator(
                progress = (24 - hoursRemaining) / 24f,
                modifier = Modifier.fillMaxWidth(),
                color = Color(0xFF2196F3)
            )
        }
    }
}

@Composable
fun DelayPeriodSelector(
    selectedHours: Int,
    onPeriodSelected: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    val delayOptions = listOf(
        1 to "1 Hour",
        24 to "24 Hours",
        48 to "48 Hours",
        168 to "1 Week"
    )

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Choose Delay Period",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(12.dp))

            delayOptions.forEach { (hours, label) ->
                DelayOptionItem(
                    hours = hours,
                    label = label,
                    isSelected = selectedHours == hours,
                    onClick = { onPeriodSelected(hours) }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun DelayOptionItem(
    hours: Int,
    label: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = if (isSelected) 4.dp else 1.dp,
        backgroundColor = if (isSelected) MaterialTheme.colors.primary.copy(alpha = 0.1f) else MaterialTheme.colors.surface,
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = MaterialTheme.colors.primary
                )
            )
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = label,
                    style = MaterialTheme.typography.subtitle1,
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                )
                Text(
                    text = getDelayDescription(hours),
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
        }
    }
}

private fun getDelayDescription(hours: Int): String {
    return when (hours) {
        1 -> "Quick reflection for small purchases"
        24 -> "Standard cooling-off period"
        48 -> "Extended consideration time"
        168 -> "Major purchase evaluation"
        else -> "Custom delay period"
    }
}

@Composable
fun MindfulnessBreathingExercise(
    onComplete: () -> Unit,
    modifier: Modifier = Modifier
) {
    var currentPhase by remember { mutableStateOf("inhale") }
    var secondsRemaining by remember { mutableStateOf(4) }
    var cycleCount by remember { mutableStateOf(0) }
    val totalCycles = 3

    LaunchedEffect(Unit) {
        while (cycleCount < totalCycles) {
            // Inhale phase
            currentPhase = "inhale"
            for (i in 4 downTo 1) {
                secondsRemaining = i
                delay(1000)
            }

            // Hold phase
            currentPhase = "hold"
            for (i in 4 downTo 1) {
                secondsRemaining = i
                delay(1000)
            }

            // Exhale phase
            currentPhase = "exhale"
            for (i in 4 downTo 1) {
                secondsRemaining = i
                delay(1000)
            }

            cycleCount++
        }
        onComplete()
    }

    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = Color(0xFFE8F5E8)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "Mindful Breathing",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF4CAF50)
            )

            Spacer(modifier = Modifier.height(16.dp))

            Box(
                modifier = Modifier.size(120.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    progress = (4 - secondsRemaining) / 4f,
                    modifier = Modifier.fillMaxSize(),
                    color = Color(0xFF4CAF50),
                    strokeWidth = 8.dp
                )

                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = currentPhase.uppercase(),
                        style = MaterialTheme.typography.h6,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF4CAF50)
                    )
                    Text(
                        text = "$secondsRemaining",
                        style = MaterialTheme.typography.h4,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF4CAF50)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "Cycle ${cycleCount + 1} of $totalCycles",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = when (currentPhase) {
                    "inhale" -> "Breathe in slowly through your nose"
                    "hold" -> "Hold your breath gently"
                    "exhale" -> "Breathe out slowly through your mouth"
                    else -> ""
                },
                style = MaterialTheme.typography.body2,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.8f)
            )
        }
    }
}

@Composable
fun BudgetImpactCard(
    budgetImpact: BudgetImpactPreview,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp),
        backgroundColor = if (budgetImpact.wouldExceedBudget) Color(0xFFFFEBEE) else Color(0xFFE8F5E8)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    if (budgetImpact.wouldExceedBudget) Icons.Default.Warning else Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = if (budgetImpact.wouldExceedBudget) Color(0xFFF44336) else Color(0xFF4CAF50)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Budget Impact",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Target category impact
            if (budgetImpact.targetCategory != null) {
                val category = budgetImpact.targetCategory
                Text(
                    text = "${category.name}: $${String.format("%.0f", budgetImpact.remainingAfterPurchase)} remaining",
                    style = MaterialTheme.typography.body1,
                    color = if (budgetImpact.wouldExceedBudget) Color(0xFFF44336) else Color(0xFF4CAF50),
                    fontWeight = FontWeight.Medium
                )
            } else {
                Text(
                    text = "No matching budget category found",
                    style = MaterialTheme.typography.body1,
                    color = Color(0xFFFF9800),
                    fontWeight = FontWeight.Medium
                )
            }

            // Alternative categories suggestion
            if (budgetImpact.wouldExceedBudget && budgetImpact.alternativeCategories.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "💡 Consider using: ${budgetImpact.alternativeCategories.first().name}",
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }

            // Total budget impact
            Spacer(modifier = Modifier.height(12.dp))
            Divider()
            Spacer(modifier = Modifier.height(8.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column {
                    Text(
                        text = "Total Remaining",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "$${String.format("%.0f", budgetImpact.totalBudgetImpact.totalRemaining)}",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium,
                        color = if (budgetImpact.totalBudgetImpact.totalRemaining < 0) Color(0xFFF44336) else Color(0xFF4CAF50)
                    )
                }

                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "Budget Used",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    val usagePercentage = if (budgetImpact.totalBudgetImpact.totalAllocated > 0) {
                        (budgetImpact.totalBudgetImpact.totalSpent / budgetImpact.totalBudgetImpact.totalAllocated * 100).toInt()
                    } else 0
                    Text(
                        text = "$usagePercentage%",
                        style = MaterialTheme.typography.body1,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

