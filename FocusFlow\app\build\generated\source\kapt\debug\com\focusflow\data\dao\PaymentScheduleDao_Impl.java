package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.PaymentSchedule;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDate;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PaymentScheduleDao_Impl implements PaymentScheduleDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<PaymentSchedule> __insertionAdapterOfPaymentSchedule;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<PaymentSchedule> __deletionAdapterOfPaymentSchedule;

  private final EntityDeletionOrUpdateAdapter<PaymentSchedule> __updateAdapterOfPaymentSchedule;

  private final SharedSQLiteStatement __preparedStmtOfDeletePaymentSchedulesByPlan;

  public PaymentScheduleDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPaymentSchedule = new EntityInsertionAdapter<PaymentSchedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `payment_schedules` (`id`,`payoffPlanId`,`creditCardId`,`month`,`scheduledPayment`,`principalAmount`,`interestAmount`,`remainingBalance`,`isExtraPayment`,`dueDate`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PaymentSchedule entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getPayoffPlanId());
        statement.bindLong(3, entity.getCreditCardId());
        statement.bindLong(4, entity.getMonth());
        statement.bindDouble(5, entity.getScheduledPayment());
        statement.bindDouble(6, entity.getPrincipalAmount());
        statement.bindDouble(7, entity.getInterestAmount());
        statement.bindDouble(8, entity.getRemainingBalance());
        final int _tmp = entity.isExtraPayment() ? 1 : 0;
        statement.bindLong(9, _tmp);
        final String _tmp_1 = __converters.fromLocalDate(entity.getDueDate());
        if (_tmp_1 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_1);
        }
      }
    };
    this.__deletionAdapterOfPaymentSchedule = new EntityDeletionOrUpdateAdapter<PaymentSchedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `payment_schedules` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PaymentSchedule entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfPaymentSchedule = new EntityDeletionOrUpdateAdapter<PaymentSchedule>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `payment_schedules` SET `id` = ?,`payoffPlanId` = ?,`creditCardId` = ?,`month` = ?,`scheduledPayment` = ?,`principalAmount` = ?,`interestAmount` = ?,`remainingBalance` = ?,`isExtraPayment` = ?,`dueDate` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PaymentSchedule entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getPayoffPlanId());
        statement.bindLong(3, entity.getCreditCardId());
        statement.bindLong(4, entity.getMonth());
        statement.bindDouble(5, entity.getScheduledPayment());
        statement.bindDouble(6, entity.getPrincipalAmount());
        statement.bindDouble(7, entity.getInterestAmount());
        statement.bindDouble(8, entity.getRemainingBalance());
        final int _tmp = entity.isExtraPayment() ? 1 : 0;
        statement.bindLong(9, _tmp);
        final String _tmp_1 = __converters.fromLocalDate(entity.getDueDate());
        if (_tmp_1 == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, _tmp_1);
        }
        statement.bindLong(11, entity.getId());
      }
    };
    this.__preparedStmtOfDeletePaymentSchedulesByPlan = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM payment_schedules WHERE payoffPlanId = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertPaymentSchedule(final PaymentSchedule paymentSchedule,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfPaymentSchedule.insertAndReturnId(paymentSchedule);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertPaymentSchedules(final List<PaymentSchedule> paymentSchedules,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPaymentSchedule.insert(paymentSchedules);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePaymentSchedule(final PaymentSchedule paymentSchedule,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPaymentSchedule.handle(paymentSchedule);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePaymentSchedule(final PaymentSchedule paymentSchedule,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPaymentSchedule.handle(paymentSchedule);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePaymentSchedulesByPlan(final long planId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeletePaymentSchedulesByPlan.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, planId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeletePaymentSchedulesByPlan.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PaymentSchedule>> getPaymentScheduleByPlan(final long planId) {
    final String _sql = "SELECT * FROM payment_schedules WHERE payoffPlanId = ? ORDER BY month ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payment_schedules"}, new Callable<List<PaymentSchedule>>() {
      @Override
      @NonNull
      public List<PaymentSchedule> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfCreditCardId = CursorUtil.getColumnIndexOrThrow(_cursor, "creditCardId");
          final int _cursorIndexOfMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "month");
          final int _cursorIndexOfScheduledPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledPayment");
          final int _cursorIndexOfPrincipalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "principalAmount");
          final int _cursorIndexOfInterestAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "interestAmount");
          final int _cursorIndexOfRemainingBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingBalance");
          final int _cursorIndexOfIsExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "isExtraPayment");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final List<PaymentSchedule> _result = new ArrayList<PaymentSchedule>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PaymentSchedule _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final long _tmpCreditCardId;
            _tmpCreditCardId = _cursor.getLong(_cursorIndexOfCreditCardId);
            final int _tmpMonth;
            _tmpMonth = _cursor.getInt(_cursorIndexOfMonth);
            final double _tmpScheduledPayment;
            _tmpScheduledPayment = _cursor.getDouble(_cursorIndexOfScheduledPayment);
            final double _tmpPrincipalAmount;
            _tmpPrincipalAmount = _cursor.getDouble(_cursorIndexOfPrincipalAmount);
            final double _tmpInterestAmount;
            _tmpInterestAmount = _cursor.getDouble(_cursorIndexOfInterestAmount);
            final double _tmpRemainingBalance;
            _tmpRemainingBalance = _cursor.getDouble(_cursorIndexOfRemainingBalance);
            final boolean _tmpIsExtraPayment;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsExtraPayment);
            _tmpIsExtraPayment = _tmp != 0;
            final LocalDate _tmpDueDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.toLocalDate(_tmp_1);
            _item = new PaymentSchedule(_tmpId,_tmpPayoffPlanId,_tmpCreditCardId,_tmpMonth,_tmpScheduledPayment,_tmpPrincipalAmount,_tmpInterestAmount,_tmpRemainingBalance,_tmpIsExtraPayment,_tmpDueDate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getPaymentScheduleByPlanSync(final long planId,
      final Continuation<? super List<PaymentSchedule>> $completion) {
    final String _sql = "SELECT * FROM payment_schedules WHERE payoffPlanId = ? ORDER BY month ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PaymentSchedule>>() {
      @Override
      @NonNull
      public List<PaymentSchedule> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfCreditCardId = CursorUtil.getColumnIndexOrThrow(_cursor, "creditCardId");
          final int _cursorIndexOfMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "month");
          final int _cursorIndexOfScheduledPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledPayment");
          final int _cursorIndexOfPrincipalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "principalAmount");
          final int _cursorIndexOfInterestAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "interestAmount");
          final int _cursorIndexOfRemainingBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingBalance");
          final int _cursorIndexOfIsExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "isExtraPayment");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final List<PaymentSchedule> _result = new ArrayList<PaymentSchedule>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PaymentSchedule _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final long _tmpCreditCardId;
            _tmpCreditCardId = _cursor.getLong(_cursorIndexOfCreditCardId);
            final int _tmpMonth;
            _tmpMonth = _cursor.getInt(_cursorIndexOfMonth);
            final double _tmpScheduledPayment;
            _tmpScheduledPayment = _cursor.getDouble(_cursorIndexOfScheduledPayment);
            final double _tmpPrincipalAmount;
            _tmpPrincipalAmount = _cursor.getDouble(_cursorIndexOfPrincipalAmount);
            final double _tmpInterestAmount;
            _tmpInterestAmount = _cursor.getDouble(_cursorIndexOfInterestAmount);
            final double _tmpRemainingBalance;
            _tmpRemainingBalance = _cursor.getDouble(_cursorIndexOfRemainingBalance);
            final boolean _tmpIsExtraPayment;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsExtraPayment);
            _tmpIsExtraPayment = _tmp != 0;
            final LocalDate _tmpDueDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.toLocalDate(_tmp_1);
            _item = new PaymentSchedule(_tmpId,_tmpPayoffPlanId,_tmpCreditCardId,_tmpMonth,_tmpScheduledPayment,_tmpPrincipalAmount,_tmpInterestAmount,_tmpRemainingBalance,_tmpIsExtraPayment,_tmpDueDate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PaymentSchedule>> getPaymentScheduleByCard(final long cardId) {
    final String _sql = "SELECT * FROM payment_schedules WHERE creditCardId = ? ORDER BY month ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, cardId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payment_schedules"}, new Callable<List<PaymentSchedule>>() {
      @Override
      @NonNull
      public List<PaymentSchedule> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfCreditCardId = CursorUtil.getColumnIndexOrThrow(_cursor, "creditCardId");
          final int _cursorIndexOfMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "month");
          final int _cursorIndexOfScheduledPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledPayment");
          final int _cursorIndexOfPrincipalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "principalAmount");
          final int _cursorIndexOfInterestAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "interestAmount");
          final int _cursorIndexOfRemainingBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingBalance");
          final int _cursorIndexOfIsExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "isExtraPayment");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final List<PaymentSchedule> _result = new ArrayList<PaymentSchedule>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PaymentSchedule _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final long _tmpCreditCardId;
            _tmpCreditCardId = _cursor.getLong(_cursorIndexOfCreditCardId);
            final int _tmpMonth;
            _tmpMonth = _cursor.getInt(_cursorIndexOfMonth);
            final double _tmpScheduledPayment;
            _tmpScheduledPayment = _cursor.getDouble(_cursorIndexOfScheduledPayment);
            final double _tmpPrincipalAmount;
            _tmpPrincipalAmount = _cursor.getDouble(_cursorIndexOfPrincipalAmount);
            final double _tmpInterestAmount;
            _tmpInterestAmount = _cursor.getDouble(_cursorIndexOfInterestAmount);
            final double _tmpRemainingBalance;
            _tmpRemainingBalance = _cursor.getDouble(_cursorIndexOfRemainingBalance);
            final boolean _tmpIsExtraPayment;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsExtraPayment);
            _tmpIsExtraPayment = _tmp != 0;
            final LocalDate _tmpDueDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.toLocalDate(_tmp_1);
            _item = new PaymentSchedule(_tmpId,_tmpPayoffPlanId,_tmpCreditCardId,_tmpMonth,_tmpScheduledPayment,_tmpPrincipalAmount,_tmpInterestAmount,_tmpRemainingBalance,_tmpIsExtraPayment,_tmpDueDate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PaymentSchedule>> getPaymentsDueInRange(final LocalDate startDate,
      final LocalDate endDate) {
    final String _sql = "SELECT * FROM payment_schedules WHERE dueDate BETWEEN ? AND ? ORDER BY dueDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDate(startDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    _argIndex = 2;
    final String _tmp_1 = __converters.fromLocalDate(endDate);
    if (_tmp_1 == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp_1);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payment_schedules"}, new Callable<List<PaymentSchedule>>() {
      @Override
      @NonNull
      public List<PaymentSchedule> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfCreditCardId = CursorUtil.getColumnIndexOrThrow(_cursor, "creditCardId");
          final int _cursorIndexOfMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "month");
          final int _cursorIndexOfScheduledPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledPayment");
          final int _cursorIndexOfPrincipalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "principalAmount");
          final int _cursorIndexOfInterestAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "interestAmount");
          final int _cursorIndexOfRemainingBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingBalance");
          final int _cursorIndexOfIsExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "isExtraPayment");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final List<PaymentSchedule> _result = new ArrayList<PaymentSchedule>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PaymentSchedule _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final long _tmpCreditCardId;
            _tmpCreditCardId = _cursor.getLong(_cursorIndexOfCreditCardId);
            final int _tmpMonth;
            _tmpMonth = _cursor.getInt(_cursorIndexOfMonth);
            final double _tmpScheduledPayment;
            _tmpScheduledPayment = _cursor.getDouble(_cursorIndexOfScheduledPayment);
            final double _tmpPrincipalAmount;
            _tmpPrincipalAmount = _cursor.getDouble(_cursorIndexOfPrincipalAmount);
            final double _tmpInterestAmount;
            _tmpInterestAmount = _cursor.getDouble(_cursorIndexOfInterestAmount);
            final double _tmpRemainingBalance;
            _tmpRemainingBalance = _cursor.getDouble(_cursorIndexOfRemainingBalance);
            final boolean _tmpIsExtraPayment;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsExtraPayment);
            _tmpIsExtraPayment = _tmp_2 != 0;
            final LocalDate _tmpDueDate;
            final String _tmp_3;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getString(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.toLocalDate(_tmp_3);
            _item = new PaymentSchedule(_tmpId,_tmpPayoffPlanId,_tmpCreditCardId,_tmpMonth,_tmpScheduledPayment,_tmpPrincipalAmount,_tmpInterestAmount,_tmpRemainingBalance,_tmpIsExtraPayment,_tmpDueDate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PaymentSchedule>> getOverduePayments(final LocalDate date) {
    final String _sql = "SELECT * FROM payment_schedules WHERE dueDate <= ? ORDER BY dueDate ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDate(date);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"payment_schedules"}, new Callable<List<PaymentSchedule>>() {
      @Override
      @NonNull
      public List<PaymentSchedule> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPayoffPlanId = CursorUtil.getColumnIndexOrThrow(_cursor, "payoffPlanId");
          final int _cursorIndexOfCreditCardId = CursorUtil.getColumnIndexOrThrow(_cursor, "creditCardId");
          final int _cursorIndexOfMonth = CursorUtil.getColumnIndexOrThrow(_cursor, "month");
          final int _cursorIndexOfScheduledPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledPayment");
          final int _cursorIndexOfPrincipalAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "principalAmount");
          final int _cursorIndexOfInterestAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "interestAmount");
          final int _cursorIndexOfRemainingBalance = CursorUtil.getColumnIndexOrThrow(_cursor, "remainingBalance");
          final int _cursorIndexOfIsExtraPayment = CursorUtil.getColumnIndexOrThrow(_cursor, "isExtraPayment");
          final int _cursorIndexOfDueDate = CursorUtil.getColumnIndexOrThrow(_cursor, "dueDate");
          final List<PaymentSchedule> _result = new ArrayList<PaymentSchedule>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PaymentSchedule _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpPayoffPlanId;
            _tmpPayoffPlanId = _cursor.getLong(_cursorIndexOfPayoffPlanId);
            final long _tmpCreditCardId;
            _tmpCreditCardId = _cursor.getLong(_cursorIndexOfCreditCardId);
            final int _tmpMonth;
            _tmpMonth = _cursor.getInt(_cursorIndexOfMonth);
            final double _tmpScheduledPayment;
            _tmpScheduledPayment = _cursor.getDouble(_cursorIndexOfScheduledPayment);
            final double _tmpPrincipalAmount;
            _tmpPrincipalAmount = _cursor.getDouble(_cursorIndexOfPrincipalAmount);
            final double _tmpInterestAmount;
            _tmpInterestAmount = _cursor.getDouble(_cursorIndexOfInterestAmount);
            final double _tmpRemainingBalance;
            _tmpRemainingBalance = _cursor.getDouble(_cursorIndexOfRemainingBalance);
            final boolean _tmpIsExtraPayment;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsExtraPayment);
            _tmpIsExtraPayment = _tmp_1 != 0;
            final LocalDate _tmpDueDate;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfDueDate)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfDueDate);
            }
            _tmpDueDate = __converters.toLocalDate(_tmp_2);
            _item = new PaymentSchedule(_tmpId,_tmpPayoffPlanId,_tmpCreditCardId,_tmpMonth,_tmpScheduledPayment,_tmpPrincipalAmount,_tmpInterestAmount,_tmpRemainingBalance,_tmpIsExtraPayment,_tmpDueDate);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getTotalScheduledPayments(final long planId,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(scheduledPayment) FROM payment_schedules WHERE payoffPlanId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalPrincipalPayments(final long planId,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(principalAmount) FROM payment_schedules WHERE payoffPlanId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalInterestPayments(final long planId,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT SUM(interestAmount) FROM payment_schedules WHERE payoffPlanId = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, planId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
