package com.focusflow.di;

import com.focusflow.data.dao.VoiceCommandDao;
import com.focusflow.data.database.FocusFlowDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideVoiceCommandDaoFactory implements Factory<VoiceCommandDao> {
  private final Provider<FocusFlowDatabase> databaseProvider;

  public DatabaseModule_ProvideVoiceCommandDaoFactory(
      Provider<FocusFlowDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public VoiceCommandDao get() {
    return provideVoiceCommandDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideVoiceCommandDaoFactory create(
      Provider<FocusFlowDatabase> databaseProvider) {
    return new DatabaseModule_ProvideVoiceCommandDaoFactory(databaseProvider);
  }

  public static VoiceCommandDao provideVoiceCommandDao(FocusFlowDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideVoiceCommandDao(database));
  }
}
