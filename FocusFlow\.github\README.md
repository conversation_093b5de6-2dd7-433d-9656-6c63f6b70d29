# FocusFlow CI/CD Pipeline

## Overview

This directory contains the complete CI/CD pipeline configuration for FocusFlow, including GitHub Actions workflows, setup documentation, and validation scripts. The pipeline is designed with security best practices and graceful fallback behavior for development environments.

## 🏗️ **Pipeline Architecture**

### Workflow Structure
```
validate-secrets → test → lint → security-scan → build-debug → ui-tests
                                                      ↓
                                              build-release (if secrets available)
                                                      ↓
                                              deploy-internal/deploy-production
```

### Key Features
- **Secret Validation**: Checks for required secrets before attempting to use them
- **Graceful Fallbacks**: Continues with available functionality when secrets are missing
- **Security First**: Validates all inputs and provides clear error messages
- **Development Friendly**: Works in forked repositories without production secrets
- **Comprehensive Testing**: Unit tests, UI tests, security scans, and performance checks

## 📁 **File Structure**

```
.github/
├── workflows/
│   └── android-ci-cd.yml          # Main CI/CD workflow
├── scripts/
│   └── validate-secrets.sh        # Local secret validation script
├── SETUP_GUIDE.md                 # Complete setup instructions
├── SECRETS_DOCUMENTATION.md       # Security documentation
└── README.md                      # This file
```

## 🚀 **Quick Start**

### For Development (No Secrets Required)
```bash
# Clone repository
git clone https://github.com/your-org/focusflow.git
cd focusflow

# Push changes - basic pipeline will run
git add .
git commit -m "Your changes"
git push origin main
```

**Available without secrets:**
- ✅ Debug builds
- ✅ Unit and UI tests  
- ✅ Code quality checks
- ✅ Security scans

### For Production (Requires Setup)
1. **Read the setup guide**: `.github/SETUP_GUIDE.md`
2. **Generate Android keystore** for app signing
3. **Create Google Play Console service account**
4. **Add secrets to repository settings**
5. **Create release tag** to trigger deployment

## 🔐 **Required Secrets**

### Android App Signing
- `KEYSTORE_BASE64` - Base64 encoded keystore file
- `SIGNING_KEY_ALIAS` - Keystore key alias
- `SIGNING_KEY_PASSWORD` - Key password
- `SIGNING_STORE_PASSWORD` - Keystore password

### Google Play Deployment  
- `GOOGLE_PLAY_SERVICE_ACCOUNT_JSON` - Service account JSON

## 🔍 **Secret Validation**

### Automatic Validation
The pipeline automatically validates secrets and provides helpful feedback:

```yaml
# Example workflow output
✅ All release build secrets are available
✅ Google Play deployment secrets are available  
✅ Full CI/CD pipeline available
```

### Local Validation
Test your setup locally before pushing:

```bash
# Make script executable
chmod +x .github/scripts/validate-secrets.sh

# Run validation
./.github/scripts/validate-secrets.sh
```

### Manual Validation
Check secrets in repository settings:
1. Go to **Repository Settings**
2. Navigate to **Secrets and variables** → **Actions**
3. Verify all required secrets are present

## 🔄 **Workflow Triggers**

### Automatic Triggers
- **Push to main/develop**: Runs tests and builds
- **Pull Request**: Runs tests and quality checks
- **Release Published**: Builds and deploys to appropriate track

### Manual Triggers
- **Workflow Dispatch**: Manual trigger with options
- **Repository Dispatch**: API-triggered builds

### Release Tracks
- **Alpha releases** (`v1.0.0-alpha`): Deploy to internal testing
- **Beta releases** (`v1.0.0-beta`): Deploy to closed testing  
- **Production releases** (`v1.0.0`): Deploy to production

## 🛡️ **Security Features**

### Input Validation
- All secrets validated before use
- Keystore format verification
- Service account JSON validation
- Clear error messages for invalid inputs

### Secure Handling
- Secrets masked in logs
- Temporary files cleaned up
- No secret exposure in error messages
- Audit trail for all secret access

### Fallback Behavior
- Graceful degradation when secrets missing
- Clear instructions for setup
- Development-friendly error messages
- Skip deployment steps safely

## 🧪 **Testing Strategy**

### Test Types
- **Unit Tests**: Core business logic
- **UI Tests**: User interface and interactions
- **Integration Tests**: Component integration
- **Security Tests**: Vulnerability scanning
- **Performance Tests**: Response times and memory usage

### Test Environments
- **Multiple Android API levels**: 24, 28, 33
- **Different device configurations**: Phone and tablet
- **Accessibility testing**: Screen readers and navigation
- **ADHD-specific testing**: Cognitive load and usability

## 📊 **Monitoring & Analytics**

### Build Monitoring
- **Success/failure rates** tracked over time
- **Build duration** monitoring and optimization
- **Test coverage** reporting and trends
- **Security scan** results and remediation

### Deployment Monitoring  
- **Release deployment** success tracking
- **Google Play Console** integration status
- **User adoption** of new releases
- **Crash reporting** integration

## 🔧 **Troubleshooting**

### Common Issues

#### "Secret not found" Error
```
Error: Secret KEYSTORE_BASE64 not found
Solution: Add secret to repository settings with exact name
```

#### "Invalid keystore" Error  
```
Error: Invalid keystore format
Solution: Ensure keystore is properly base64 encoded
```

#### "Google Play API permission denied"
```
Error: The caller does not have permission
Solution: Verify service account permissions in Play Console
```

### Debug Steps
1. **Check secret names**: Ensure exact case-sensitive match
2. **Validate secret content**: No extra whitespace or characters
3. **Test locally**: Use validation script before pushing
4. **Review logs**: Check workflow logs for specific errors
5. **Verify permissions**: Confirm Google Play Console access

### Getting Help
- **Setup Issues**: Review `.github/SETUP_GUIDE.md`
- **Security Questions**: Check `.github/SECRETS_DOCUMENTATION.md`
- **Workflow Problems**: Examine GitHub Actions logs
- **Local Testing**: Use `.github/scripts/validate-secrets.sh`

## 📈 **Performance Optimization**

### Build Speed
- **Gradle caching** for faster builds
- **Dependency caching** across workflow runs
- **Parallel job execution** where possible
- **Incremental builds** for development

### Resource Usage
- **Efficient artifact handling** with compression
- **Selective test execution** based on changes
- **Optimized Docker images** for faster startup
- **Smart caching strategies** for dependencies

## 🔄 **Maintenance**

### Regular Tasks
- **Monthly**: Review workflow execution logs
- **Quarterly**: Update dependencies and actions
- **Annually**: Rotate secrets and review security

### Updates
- **GitHub Actions**: Keep actions up to date
- **Android Tools**: Update SDK and build tools
- **Dependencies**: Regular security updates
- **Documentation**: Keep guides current

## 📚 **Additional Resources**

### Documentation
- **Complete Setup Guide**: `.github/SETUP_GUIDE.md`
- **Security Documentation**: `.github/SECRETS_DOCUMENTATION.md`
- **Validation Script**: `.github/scripts/validate-secrets.sh`

### External Resources
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Android App Signing](https://developer.android.com/studio/publish/app-signing)
- [Google Play Console API](https://developers.google.com/android-publisher)
- [Security Best Practices](https://docs.github.com/en/actions/security-guides)

---

## 🎯 **Status: Production Ready**

The FocusFlow CI/CD pipeline is fully configured and ready for production use. It provides:

- ✅ **Comprehensive testing** across multiple environments
- ✅ **Secure secret management** with validation
- ✅ **Automated deployment** to Google Play Store
- ✅ **Development-friendly** fallback behavior
- ✅ **Security-first** approach with best practices
- ✅ **Clear documentation** and troubleshooting guides

**Ready to deploy!** 🚀
