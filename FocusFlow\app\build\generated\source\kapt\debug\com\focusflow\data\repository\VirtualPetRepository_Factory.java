package com.focusflow.data.repository;

import com.focusflow.data.dao.VirtualPetDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class VirtualPetRepository_Factory implements Factory<VirtualPetRepository> {
  private final Provider<VirtualPetDao> virtualPetDaoProvider;

  public VirtualPetRepository_Factory(Provider<VirtualPetDao> virtualPetDaoProvider) {
    this.virtualPetDaoProvider = virtualPetDaoProvider;
  }

  @Override
  public VirtualPetRepository get() {
    return newInstance(virtualPetDaoProvider.get());
  }

  public static VirtualPetRepository_Factory create(Provider<VirtualPetDao> virtualPetDaoProvider) {
    return new VirtualPetRepository_Factory(virtualPetDaoProvider);
  }

  public static VirtualPetRepository newInstance(VirtualPetDao virtualPetDao) {
    return new VirtualPetRepository(virtualPetDao);
  }
}
