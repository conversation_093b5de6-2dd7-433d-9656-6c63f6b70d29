package com.focusflow.data.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.Achievement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AchievementDao_Impl implements AchievementDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Achievement> __insertionAdapterOfAchievement;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<Achievement> __updateAdapterOfAchievement;

  private final SharedSQLiteStatement __preparedStmtOfUnlockAchievement;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAchievementProgress;

  public AchievementDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAchievement = new EntityInsertionAdapter<Achievement>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `achievements` (`id`,`type`,`title`,`description`,`iconEmoji`,`pointsAwarded`,`isUnlocked`,`unlockedAt`,`targetValue`,`currentProgress`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Achievement entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getType());
        }
        if (entity.getTitle() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTitle());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getIconEmoji() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getIconEmoji());
        }
        statement.bindLong(6, entity.getPointsAwarded());
        final int _tmp = entity.isUnlocked() ? 1 : 0;
        statement.bindLong(7, _tmp);
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getUnlockedAt());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_1);
        }
        if (entity.getTargetValue() == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, entity.getTargetValue());
        }
        statement.bindLong(10, entity.getCurrentProgress());
      }
    };
    this.__updateAdapterOfAchievement = new EntityDeletionOrUpdateAdapter<Achievement>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `achievements` SET `id` = ?,`type` = ?,`title` = ?,`description` = ?,`iconEmoji` = ?,`pointsAwarded` = ?,`isUnlocked` = ?,`unlockedAt` = ?,`targetValue` = ?,`currentProgress` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Achievement entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getType());
        }
        if (entity.getTitle() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTitle());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getIconEmoji() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getIconEmoji());
        }
        statement.bindLong(6, entity.getPointsAwarded());
        final int _tmp = entity.isUnlocked() ? 1 : 0;
        statement.bindLong(7, _tmp);
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getUnlockedAt());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, _tmp_1);
        }
        if (entity.getTargetValue() == null) {
          statement.bindNull(9);
        } else {
          statement.bindLong(9, entity.getTargetValue());
        }
        statement.bindLong(10, entity.getCurrentProgress());
        statement.bindLong(11, entity.getId());
      }
    };
    this.__preparedStmtOfUnlockAchievement = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE achievements SET isUnlocked = 1, unlockedAt = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAchievementProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE achievements SET currentProgress = ? WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertAchievement(final Achievement achievement,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfAchievement.insertAndReturnId(achievement);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAchievement(final Achievement achievement,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfAchievement.handle(achievement);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object unlockAchievement(final long achievementId, final LocalDateTime unlockedAt,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUnlockAchievement.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(unlockedAt);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, achievementId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUnlockAchievement.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAchievementProgress(final long achievementId, final int progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAchievementProgress.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, progress);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, achievementId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAchievementProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Achievement>> getAllAchievements() {
    final String _sql = "SELECT `achievements`.`id` AS `id`, `achievements`.`type` AS `type`, `achievements`.`title` AS `title`, `achievements`.`description` AS `description`, `achievements`.`iconEmoji` AS `iconEmoji`, `achievements`.`pointsAwarded` AS `pointsAwarded`, `achievements`.`isUnlocked` AS `isUnlocked`, `achievements`.`unlockedAt` AS `unlockedAt`, `achievements`.`targetValue` AS `targetValue`, `achievements`.`currentProgress` AS `currentProgress` FROM achievements ORDER BY isUnlocked DESC, pointsAwarded DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"achievements"}, new Callable<List<Achievement>>() {
      @Override
      @NonNull
      public List<Achievement> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfType = 1;
          final int _cursorIndexOfTitle = 2;
          final int _cursorIndexOfDescription = 3;
          final int _cursorIndexOfIconEmoji = 4;
          final int _cursorIndexOfPointsAwarded = 5;
          final int _cursorIndexOfIsUnlocked = 6;
          final int _cursorIndexOfUnlockedAt = 7;
          final int _cursorIndexOfTargetValue = 8;
          final int _cursorIndexOfCurrentProgress = 9;
          final List<Achievement> _result = new ArrayList<Achievement>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Achievement _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconEmoji;
            if (_cursor.isNull(_cursorIndexOfIconEmoji)) {
              _tmpIconEmoji = null;
            } else {
              _tmpIconEmoji = _cursor.getString(_cursorIndexOfIconEmoji);
            }
            final int _tmpPointsAwarded;
            _tmpPointsAwarded = _cursor.getInt(_cursorIndexOfPointsAwarded);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            final LocalDateTime _tmpUnlockedAt;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUnlockedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfUnlockedAt);
            }
            _tmpUnlockedAt = __converters.toLocalDateTime(_tmp_1);
            final Integer _tmpTargetValue;
            if (_cursor.isNull(_cursorIndexOfTargetValue)) {
              _tmpTargetValue = null;
            } else {
              _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            }
            final int _tmpCurrentProgress;
            _tmpCurrentProgress = _cursor.getInt(_cursorIndexOfCurrentProgress);
            _item = new Achievement(_tmpId,_tmpType,_tmpTitle,_tmpDescription,_tmpIconEmoji,_tmpPointsAwarded,_tmpIsUnlocked,_tmpUnlockedAt,_tmpTargetValue,_tmpCurrentProgress);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Achievement>> getUnlockedAchievements() {
    final String _sql = "SELECT `achievements`.`id` AS `id`, `achievements`.`type` AS `type`, `achievements`.`title` AS `title`, `achievements`.`description` AS `description`, `achievements`.`iconEmoji` AS `iconEmoji`, `achievements`.`pointsAwarded` AS `pointsAwarded`, `achievements`.`isUnlocked` AS `isUnlocked`, `achievements`.`unlockedAt` AS `unlockedAt`, `achievements`.`targetValue` AS `targetValue`, `achievements`.`currentProgress` AS `currentProgress` FROM achievements WHERE isUnlocked = 1 ORDER BY unlockedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"achievements"}, new Callable<List<Achievement>>() {
      @Override
      @NonNull
      public List<Achievement> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfType = 1;
          final int _cursorIndexOfTitle = 2;
          final int _cursorIndexOfDescription = 3;
          final int _cursorIndexOfIconEmoji = 4;
          final int _cursorIndexOfPointsAwarded = 5;
          final int _cursorIndexOfIsUnlocked = 6;
          final int _cursorIndexOfUnlockedAt = 7;
          final int _cursorIndexOfTargetValue = 8;
          final int _cursorIndexOfCurrentProgress = 9;
          final List<Achievement> _result = new ArrayList<Achievement>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Achievement _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconEmoji;
            if (_cursor.isNull(_cursorIndexOfIconEmoji)) {
              _tmpIconEmoji = null;
            } else {
              _tmpIconEmoji = _cursor.getString(_cursorIndexOfIconEmoji);
            }
            final int _tmpPointsAwarded;
            _tmpPointsAwarded = _cursor.getInt(_cursorIndexOfPointsAwarded);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            final LocalDateTime _tmpUnlockedAt;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUnlockedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfUnlockedAt);
            }
            _tmpUnlockedAt = __converters.toLocalDateTime(_tmp_1);
            final Integer _tmpTargetValue;
            if (_cursor.isNull(_cursorIndexOfTargetValue)) {
              _tmpTargetValue = null;
            } else {
              _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            }
            final int _tmpCurrentProgress;
            _tmpCurrentProgress = _cursor.getInt(_cursorIndexOfCurrentProgress);
            _item = new Achievement(_tmpId,_tmpType,_tmpTitle,_tmpDescription,_tmpIconEmoji,_tmpPointsAwarded,_tmpIsUnlocked,_tmpUnlockedAt,_tmpTargetValue,_tmpCurrentProgress);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<Achievement>> getAchievementsByType(final String type) {
    final String _sql = "SELECT * FROM achievements WHERE type = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (type == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, type);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"achievements"}, new Callable<List<Achievement>>() {
      @Override
      @NonNull
      public List<Achievement> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfType = CursorUtil.getColumnIndexOrThrow(_cursor, "type");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfIconEmoji = CursorUtil.getColumnIndexOrThrow(_cursor, "iconEmoji");
          final int _cursorIndexOfPointsAwarded = CursorUtil.getColumnIndexOrThrow(_cursor, "pointsAwarded");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "isUnlocked");
          final int _cursorIndexOfUnlockedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "unlockedAt");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "targetValue");
          final int _cursorIndexOfCurrentProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "currentProgress");
          final List<Achievement> _result = new ArrayList<Achievement>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Achievement _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpType;
            if (_cursor.isNull(_cursorIndexOfType)) {
              _tmpType = null;
            } else {
              _tmpType = _cursor.getString(_cursorIndexOfType);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpIconEmoji;
            if (_cursor.isNull(_cursorIndexOfIconEmoji)) {
              _tmpIconEmoji = null;
            } else {
              _tmpIconEmoji = _cursor.getString(_cursorIndexOfIconEmoji);
            }
            final int _tmpPointsAwarded;
            _tmpPointsAwarded = _cursor.getInt(_cursorIndexOfPointsAwarded);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            final LocalDateTime _tmpUnlockedAt;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUnlockedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfUnlockedAt);
            }
            _tmpUnlockedAt = __converters.toLocalDateTime(_tmp_1);
            final Integer _tmpTargetValue;
            if (_cursor.isNull(_cursorIndexOfTargetValue)) {
              _tmpTargetValue = null;
            } else {
              _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            }
            final int _tmpCurrentProgress;
            _tmpCurrentProgress = _cursor.getInt(_cursorIndexOfCurrentProgress);
            _item = new Achievement(_tmpId,_tmpType,_tmpTitle,_tmpDescription,_tmpIconEmoji,_tmpPointsAwarded,_tmpIsUnlocked,_tmpUnlockedAt,_tmpTargetValue,_tmpCurrentProgress);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
