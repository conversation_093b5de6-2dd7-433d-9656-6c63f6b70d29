package com.focusflow.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B\u001f\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0002\u0010\u0007J\u000f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000b\u0010\r\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J%\u0010\u000e\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u00c6\u0001J\u0013\u0010\u000f\u001a\u00020\u00102\b\u0010\u0011\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0016"}, d2 = {"Lcom/focusflow/service/ClaudeResponse;", "", "content", "", "Lcom/focusflow/service/ClaudeContent;", "usage", "Lcom/focusflow/service/ClaudeUsage;", "(Ljava/util/List;Lcom/focusflow/service/ClaudeUsage;)V", "getContent", "()Ljava/util/List;", "getUsage", "()Lcom/focusflow/service/ClaudeUsage;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class ClaudeResponse {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.service.ClaudeContent> content = null;
    @org.jetbrains.annotations.Nullable
    private final com.focusflow.service.ClaudeUsage usage = null;
    
    public ClaudeResponse(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.ClaudeContent> content, @org.jetbrains.annotations.Nullable
    com.focusflow.service.ClaudeUsage usage) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.service.ClaudeContent> getContent() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.service.ClaudeUsage getUsage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.service.ClaudeContent> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final com.focusflow.service.ClaudeUsage component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.service.ClaudeResponse copy(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.service.ClaudeContent> content, @org.jetbrains.annotations.Nullable
    com.focusflow.service.ClaudeUsage usage) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}