com.focusflow.navigation.Screen+com.focusflow.security.DataValidationResult)com.focusflow.security.SecurityInitResultandroidx.work.CoroutineWorker#androidx.activity.ComponentActivityandroidx.lifecycle.ViewModelkotlin.Enumandroid.app.Applicationandroidx.room.RoomDatabase!android.content.BroadcastReceivercom.focusflow.service.AIService                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          