#!/bin/bash

# FocusFlow CI/CD Secret Validation Script
# This script validates that all required secrets are properly configured
# for the GitHub Actions CI/CD pipeline

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "warning")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "error")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "info")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Function to check if a secret exists (for local testing)
check_secret_file() {
    local secret_name=$1
    local file_path="$HOME/.focusflow-secrets/$secret_name"
    
    if [[ -f "$file_path" && -s "$file_path" ]]; then
        return 0
    else
        return 1
    fi
}

# Function to validate keystore format
validate_keystore() {
    local keystore_path=$1
    
    if [[ ! -f "$keystore_path" ]]; then
        print_status "error" "Keystore file not found: $keystore_path"
        return 1
    fi
    
    # Check if file is a valid keystore
    if keytool -list -keystore "$keystore_path" -storepass "test" 2>/dev/null >/dev/null; then
        print_status "success" "Keystore format is valid"
        return 0
    else
        print_status "error" "Invalid keystore format or incorrect password"
        return 1
    fi
}

# Function to validate service account JSON
validate_service_account_json() {
    local json_file=$1
    
    if [[ ! -f "$json_file" ]]; then
        print_status "error" "Service account JSON file not found: $json_file"
        return 1
    fi
    
    # Check if file is valid JSON
    if jq empty "$json_file" 2>/dev/null; then
        print_status "success" "Service account JSON format is valid"
        
        # Check required fields
        local required_fields=("type" "project_id" "private_key_id" "private_key" "client_email")
        local missing_fields=()
        
        for field in "${required_fields[@]}"; do
            if ! jq -e ".$field" "$json_file" >/dev/null 2>&1; then
                missing_fields+=("$field")
            fi
        done
        
        if [[ ${#missing_fields[@]} -eq 0 ]]; then
            print_status "success" "All required fields present in service account JSON"
            return 0
        else
            print_status "error" "Missing required fields in service account JSON: ${missing_fields[*]}"
            return 1
        fi
    else
        print_status "error" "Invalid JSON format in service account file"
        return 1
    fi
}

# Main validation function
main() {
    echo "🔍 FocusFlow CI/CD Secret Validation"
    echo "===================================="
    echo ""
    
    local validation_mode="github"
    local secrets_dir="$HOME/.focusflow-secrets"
    local has_errors=false
    
    # Check if running locally or in GitHub Actions
    if [[ -z "$GITHUB_ACTIONS" ]]; then
        validation_mode="local"
        print_status "info" "Running in local validation mode"
        print_status "info" "Looking for secrets in: $secrets_dir"
        echo ""
        
        if [[ ! -d "$secrets_dir" ]]; then
            print_status "warning" "Local secrets directory not found: $secrets_dir"
            print_status "info" "Create this directory and add secret files for local testing"
            echo ""
        fi
    else
        print_status "info" "Running in GitHub Actions environment"
        echo ""
    fi
    
    # Define required secrets
    declare -A secrets=(
        ["KEYSTORE_BASE64"]="Base64 encoded Android keystore for app signing"
        ["SIGNING_KEY_ALIAS"]="Alias name for the signing key within keystore"
        ["SIGNING_KEY_PASSWORD"]="Password for the signing key"
        ["SIGNING_STORE_PASSWORD"]="Password for the keystore file"
        ["GOOGLE_PLAY_SERVICE_ACCOUNT_JSON"]="Service account JSON for Google Play Console API"
    )
    
    # Validate each secret
    echo "📋 Checking Required Secrets:"
    echo "----------------------------"
    
    for secret_name in "${!secrets[@]}"; do
        local description="${secrets[$secret_name]}"
        
        if [[ "$validation_mode" == "github" ]]; then
            # In GitHub Actions, we can't directly check secret values
            # This would be handled by the workflow itself
            print_status "info" "$secret_name: $description"
        else
            # Local validation
            if check_secret_file "$secret_name"; then
                print_status "success" "$secret_name: Available"
                
                # Additional validation for specific secrets
                case "$secret_name" in
                    "KEYSTORE_BASE64")
                        # Decode and validate keystore
                        local temp_keystore="/tmp/test_keystore.jks"
                        if base64 -d "$secrets_dir/$secret_name" > "$temp_keystore" 2>/dev/null; then
                            print_status "success" "KEYSTORE_BASE64: Valid base64 encoding"
                            rm -f "$temp_keystore"
                        else
                            print_status "error" "KEYSTORE_BASE64: Invalid base64 encoding"
                            has_errors=true
                        fi
                        ;;
                    "GOOGLE_PLAY_SERVICE_ACCOUNT_JSON")
                        validate_service_account_json "$secrets_dir/$secret_name" || has_errors=true
                        ;;
                esac
            else
                print_status "error" "$secret_name: Missing"
                has_errors=true
            fi
        fi
    done
    
    echo ""
    echo "🔧 Pipeline Capabilities:"
    echo "------------------------"
    
    if [[ "$validation_mode" == "local" ]]; then
        local can_build_release=true
        local can_deploy=true
        
        # Check release build capability
        for secret in "KEYSTORE_BASE64" "SIGNING_KEY_ALIAS" "SIGNING_KEY_PASSWORD" "SIGNING_STORE_PASSWORD"; do
            if ! check_secret_file "$secret"; then
                can_build_release=false
                break
            fi
        done
        
        # Check deployment capability
        if ! check_secret_file "GOOGLE_PLAY_SERVICE_ACCOUNT_JSON"; then
            can_deploy=false
        fi
        
        if [[ "$can_build_release" == true ]]; then
            print_status "success" "Release builds: Available"
        else
            print_status "warning" "Release builds: Missing keystore secrets"
        fi
        
        if [[ "$can_deploy" == true ]]; then
            print_status "success" "Google Play deployment: Available"
        else
            print_status "warning" "Google Play deployment: Missing service account"
        fi
        
        if [[ "$can_build_release" == true && "$can_deploy" == true ]]; then
            print_status "success" "Full CI/CD pipeline: Available"
        else
            print_status "warning" "Limited CI/CD pipeline: Debug builds and tests only"
        fi
    else
        print_status "info" "Debug builds and tests: Always available"
        print_status "info" "Release builds: Requires keystore secrets"
        print_status "info" "Deployment: Requires Google Play Console secrets"
    fi
    
    echo ""
    echo "📖 Setup Instructions:"
    echo "---------------------"
    
    if [[ "$has_errors" == true || "$validation_mode" == "github" ]]; then
        echo "1. 📄 Read the complete setup guide:"
        echo "   .github/SETUP_GUIDE.md"
        echo ""
        echo "2. 🔐 Generate Android keystore:"
        echo "   keytool -genkey -v -keystore focusflow-release.keystore ..."
        echo ""
        echo "3. ☁️  Create Google Play Console service account:"
        echo "   - Google Cloud Console → IAM & Admin → Service Accounts"
        echo "   - Enable Google Play Android Developer API"
        echo "   - Configure permissions in Play Console"
        echo ""
        echo "4. 🔧 Add secrets to GitHub repository:"
        echo "   Repository Settings → Secrets and variables → Actions"
        echo ""
        echo "5. 📚 Review security documentation:"
        echo "   .github/SECRETS_DOCUMENTATION.md"
    else
        print_status "success" "All secrets are properly configured!"
        echo "Your CI/CD pipeline is ready for production use."
    fi
    
    echo ""
    echo "🔒 Security Reminders:"
    echo "---------------------"
    echo "• Never commit secrets to version control"
    echo "• Use strong, unique passwords for all secrets"
    echo "• Rotate secrets annually"
    echo "• Monitor secret access in audit logs"
    echo "• Report security incidents immediately"
    
    if [[ "$has_errors" == true ]]; then
        echo ""
        print_status "error" "Validation failed - please address the issues above"
        exit 1
    else
        echo ""
        print_status "success" "Validation completed successfully"
        exit 0
    fi
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi
    
    if ! command -v base64 &> /dev/null; then
        missing_deps+=("base64")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_status "error" "Missing required dependencies: ${missing_deps[*]}"
        echo "Please install the missing dependencies and try again."
        exit 1
    fi
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi
