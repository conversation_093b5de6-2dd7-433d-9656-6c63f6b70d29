package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\r\n\u0002\u0010\b\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\'\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u001b\u001a\u00020\u001cJ\u0006\u0010\u001d\u001a\u00020\u001cJ\b\u0010\u001e\u001a\u00020\u001cH\u0002J\b\u0010\u001f\u001a\u00020\u001cH\u0002J\b\u0010 \u001a\u00020\u001cH\u0014J\b\u0010!\u001a\u00020\u001cH\u0002J\u0006\u0010\"\u001a\u00020\u001cJ\b\u0010#\u001a\u00020\u001cH\u0002J\u0006\u0010$\u001a\u00020\u001cJ\b\u0010%\u001a\u00020\u001cH\u0002J\u000e\u0010&\u001a\u00020\u001c2\u0006\u0010\'\u001a\u00020\rJ\u000e\u0010(\u001a\u00020\u001c2\u0006\u0010)\u001a\u00020*J\u000e\u0010+\u001a\u00020\u001c2\u0006\u0010)\u001a\u00020*J\u0006\u0010,\u001a\u00020\u001cJ\b\u0010-\u001a\u00020\u001cH\u0002J\u0006\u0010.\u001a\u00020\u001cR\u0016\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\r0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0004\n\u0002\u0010\u0014R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0013R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/focusflow/ui/viewmodel/FocusModeViewModel;", "Landroidx/lifecycle/ViewModel;", "taskRepository", "Lcom/focusflow/data/repository/TaskRepository;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "gamificationService", "Lcom/focusflow/service/GamificationService;", "notificationManager", "error/NonExistentClass", "(Lcom/focusflow/data/repository/TaskRepository;Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/service/GamificationService;Lerror/NonExistentClass;)V", "_currentTask", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/data/model/Task;", "_uiState", "Lcom/focusflow/ui/viewmodel/FocusModeUiState;", "currentTask", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentTask", "()Lkotlinx/coroutines/flow/StateFlow;", "Lerror/NonExistentClass;", "sessionStartTime", "", "timerJob", "Lkotlinx/coroutines/Job;", "uiState", "getUiState", "clearError", "", "completeCurrentTask", "loadAvailableTasks", "loadUserPreferences", "onCleared", "onTimerFinished", "pauseSession", "resetSession", "resumeSession", "saveUserPreferences", "selectTask", "task", "setBreakDuration", "minutes", "", "setSessionDuration", "startFocusSession", "startTimer", "stopSession", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class FocusModeViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.TaskRepository taskRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.GamificationService gamificationService = null;
    @org.jetbrains.annotations.NotNull
    private final error.NonExistentClass notificationManager = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.FocusModeUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.FocusModeUiState> uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.data.model.Task> _currentTask = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.data.model.Task> currentTask = null;
    @org.jetbrains.annotations.Nullable
    private kotlinx.coroutines.Job timerJob;
    private long sessionStartTime = 0L;
    
    @javax.inject.Inject
    public FocusModeViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.TaskRepository taskRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.GamificationService gamificationService, @org.jetbrains.annotations.NotNull
    error.NonExistentClass notificationManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.FocusModeUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.data.model.Task> getCurrentTask() {
        return null;
    }
    
    private final void loadAvailableTasks() {
    }
    
    private final void loadUserPreferences() {
    }
    
    public final void selectTask(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.Task task) {
    }
    
    public final void setSessionDuration(int minutes) {
    }
    
    public final void setBreakDuration(int minutes) {
    }
    
    private final void saveUserPreferences() {
    }
    
    public final void startFocusSession() {
    }
    
    public final void pauseSession() {
    }
    
    public final void resumeSession() {
    }
    
    public final void stopSession() {
    }
    
    public final void completeCurrentTask() {
    }
    
    private final void startTimer() {
    }
    
    private final void onTimerFinished() {
    }
    
    private final void resetSession() {
    }
    
    public final void clearError() {
    }
    
    @java.lang.Override
    protected void onCleared() {
    }
}