package com.focusflow.data.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.focusflow.data.database.Converters;
import com.focusflow.data.model.SpendingPattern;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;
import kotlinx.datetime.LocalDateTime;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class SpendingPatternDao_Impl implements SpendingPatternDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SpendingPattern> __insertionAdapterOfSpendingPattern;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<SpendingPattern> __deletionAdapterOfSpendingPattern;

  private final EntityDeletionOrUpdateAdapter<SpendingPattern> __updateAdapterOfSpendingPattern;

  private final SharedSQLiteStatement __preparedStmtOfUpdatePatternConfirmation;

  private final SharedSQLiteStatement __preparedStmtOfRecordPatternOccurrence;

  private final SharedSQLiteStatement __preparedStmtOfRecordSuccessfulPrevention;

  private final SharedSQLiteStatement __preparedStmtOfRecordPreventionAttempt;

  private final SharedSQLiteStatement __preparedStmtOfUpdateInterventionEffectiveness;

  private final SharedSQLiteStatement __preparedStmtOfDeactivateSpendingPattern;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldUnconfirmedPatterns;

  public SpendingPatternDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSpendingPattern = new EntityInsertionAdapter<SpendingPattern>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `spending_patterns` (`id`,`patternType`,`patternName`,`description`,`triggerConditions`,`averageAmount`,`frequency`,`category`,`emotionalTriggers`,`timePatterns`,`locationTriggers`,`socialTriggers`,`detectedDate`,`lastOccurrence`,`occurrenceCount`,`confidenceScore`,`isActive`,`userConfirmed`,`interventionStrategy`,`interventionEffectiveness`,`notes`,`severity`,`impactOnBudget`,`relatedPatterns`,`seasonalFactor`,`stressLevel`,`preventionSuccess`,`preventionAttempts`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SpendingPattern entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPatternType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPatternType());
        }
        if (entity.getPatternName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getPatternName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getTriggerConditions() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getTriggerConditions());
        }
        statement.bindDouble(6, entity.getAverageAmount());
        if (entity.getFrequency() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFrequency());
        }
        if (entity.getCategory() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getCategory());
        }
        if (entity.getEmotionalTriggers() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getEmotionalTriggers());
        }
        if (entity.getTimePatterns() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getTimePatterns());
        }
        if (entity.getLocationTriggers() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getLocationTriggers());
        }
        if (entity.getSocialTriggers() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getSocialTriggers());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getDetectedDate());
        if (_tmp == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getLastOccurrence());
        if (_tmp_1 == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, _tmp_1);
        }
        statement.bindLong(15, entity.getOccurrenceCount());
        statement.bindDouble(16, entity.getConfidenceScore());
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(17, _tmp_2);
        final Integer _tmp_3 = entity.getUserConfirmed() == null ? null : (entity.getUserConfirmed() ? 1 : 0);
        if (_tmp_3 == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, _tmp_3);
        }
        if (entity.getInterventionStrategy() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getInterventionStrategy());
        }
        if (entity.getInterventionEffectiveness() == null) {
          statement.bindNull(20);
        } else {
          statement.bindDouble(20, entity.getInterventionEffectiveness());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getNotes());
        }
        if (entity.getSeverity() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getSeverity());
        }
        if (entity.getImpactOnBudget() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getImpactOnBudget());
        }
        if (entity.getRelatedPatterns() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getRelatedPatterns());
        }
        statement.bindDouble(25, entity.getSeasonalFactor());
        if (entity.getStressLevel() == null) {
          statement.bindNull(26);
        } else {
          statement.bindLong(26, entity.getStressLevel());
        }
        statement.bindLong(27, entity.getPreventionSuccess());
        statement.bindLong(28, entity.getPreventionAttempts());
      }
    };
    this.__deletionAdapterOfSpendingPattern = new EntityDeletionOrUpdateAdapter<SpendingPattern>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `spending_patterns` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SpendingPattern entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfSpendingPattern = new EntityDeletionOrUpdateAdapter<SpendingPattern>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `spending_patterns` SET `id` = ?,`patternType` = ?,`patternName` = ?,`description` = ?,`triggerConditions` = ?,`averageAmount` = ?,`frequency` = ?,`category` = ?,`emotionalTriggers` = ?,`timePatterns` = ?,`locationTriggers` = ?,`socialTriggers` = ?,`detectedDate` = ?,`lastOccurrence` = ?,`occurrenceCount` = ?,`confidenceScore` = ?,`isActive` = ?,`userConfirmed` = ?,`interventionStrategy` = ?,`interventionEffectiveness` = ?,`notes` = ?,`severity` = ?,`impactOnBudget` = ?,`relatedPatterns` = ?,`seasonalFactor` = ?,`stressLevel` = ?,`preventionSuccess` = ?,`preventionAttempts` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SpendingPattern entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getPatternType() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getPatternType());
        }
        if (entity.getPatternName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getPatternName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDescription());
        }
        if (entity.getTriggerConditions() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getTriggerConditions());
        }
        statement.bindDouble(6, entity.getAverageAmount());
        if (entity.getFrequency() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getFrequency());
        }
        if (entity.getCategory() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getCategory());
        }
        if (entity.getEmotionalTriggers() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getEmotionalTriggers());
        }
        if (entity.getTimePatterns() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getTimePatterns());
        }
        if (entity.getLocationTriggers() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getLocationTriggers());
        }
        if (entity.getSocialTriggers() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getSocialTriggers());
        }
        final String _tmp = __converters.fromLocalDateTime(entity.getDetectedDate());
        if (_tmp == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, _tmp);
        }
        final String _tmp_1 = __converters.fromLocalDateTime(entity.getLastOccurrence());
        if (_tmp_1 == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, _tmp_1);
        }
        statement.bindLong(15, entity.getOccurrenceCount());
        statement.bindDouble(16, entity.getConfidenceScore());
        final int _tmp_2 = entity.isActive() ? 1 : 0;
        statement.bindLong(17, _tmp_2);
        final Integer _tmp_3 = entity.getUserConfirmed() == null ? null : (entity.getUserConfirmed() ? 1 : 0);
        if (_tmp_3 == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, _tmp_3);
        }
        if (entity.getInterventionStrategy() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getInterventionStrategy());
        }
        if (entity.getInterventionEffectiveness() == null) {
          statement.bindNull(20);
        } else {
          statement.bindDouble(20, entity.getInterventionEffectiveness());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(21);
        } else {
          statement.bindString(21, entity.getNotes());
        }
        if (entity.getSeverity() == null) {
          statement.bindNull(22);
        } else {
          statement.bindString(22, entity.getSeverity());
        }
        if (entity.getImpactOnBudget() == null) {
          statement.bindNull(23);
        } else {
          statement.bindString(23, entity.getImpactOnBudget());
        }
        if (entity.getRelatedPatterns() == null) {
          statement.bindNull(24);
        } else {
          statement.bindString(24, entity.getRelatedPatterns());
        }
        statement.bindDouble(25, entity.getSeasonalFactor());
        if (entity.getStressLevel() == null) {
          statement.bindNull(26);
        } else {
          statement.bindLong(26, entity.getStressLevel());
        }
        statement.bindLong(27, entity.getPreventionSuccess());
        statement.bindLong(28, entity.getPreventionAttempts());
        statement.bindLong(29, entity.getId());
      }
    };
    this.__preparedStmtOfUpdatePatternConfirmation = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE spending_patterns SET userConfirmed = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordPatternOccurrence = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE spending_patterns SET lastOccurrence = ?, occurrenceCount = occurrenceCount + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordSuccessfulPrevention = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE spending_patterns SET preventionSuccess = preventionSuccess + 1, preventionAttempts = preventionAttempts + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfRecordPreventionAttempt = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE spending_patterns SET preventionAttempts = preventionAttempts + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateInterventionEffectiveness = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE spending_patterns SET interventionEffectiveness = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeactivateSpendingPattern = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE spending_patterns SET isActive = 0 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldUnconfirmedPatterns = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM spending_patterns WHERE detectedDate < ? AND userConfirmed != 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertSpendingPattern(final SpendingPattern spendingPattern,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfSpendingPattern.insertAndReturnId(spendingPattern);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteSpendingPattern(final SpendingPattern spendingPattern,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfSpendingPattern.handle(spendingPattern);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSpendingPattern(final SpendingPattern spendingPattern,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSpendingPattern.handle(spendingPattern);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePatternConfirmation(final long id, final boolean confirmed,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdatePatternConfirmation.acquire();
        int _argIndex = 1;
        final int _tmp = confirmed ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdatePatternConfirmation.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object recordPatternOccurrence(final long id, final LocalDateTime occurrence,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordPatternOccurrence.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(occurrence);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordPatternOccurrence.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object recordSuccessfulPrevention(final long id,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordSuccessfulPrevention.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordSuccessfulPrevention.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object recordPreventionAttempt(final long id,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfRecordPreventionAttempt.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfRecordPreventionAttempt.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateInterventionEffectiveness(final long id, final double effectiveness,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateInterventionEffectiveness.acquire();
        int _argIndex = 1;
        _stmt.bindDouble(_argIndex, effectiveness);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateInterventionEffectiveness.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deactivateSpendingPattern(final long id,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeactivateSpendingPattern.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeactivateSpendingPattern.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldUnconfirmedPatterns(final LocalDateTime cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldUnconfirmedPatterns.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromLocalDateTime(cutoffDate);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldUnconfirmedPatterns.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SpendingPattern>> getAllActiveSpendingPatterns() {
    final String _sql = "SELECT `spending_patterns`.`id` AS `id`, `spending_patterns`.`patternType` AS `patternType`, `spending_patterns`.`patternName` AS `patternName`, `spending_patterns`.`description` AS `description`, `spending_patterns`.`triggerConditions` AS `triggerConditions`, `spending_patterns`.`averageAmount` AS `averageAmount`, `spending_patterns`.`frequency` AS `frequency`, `spending_patterns`.`category` AS `category`, `spending_patterns`.`emotionalTriggers` AS `emotionalTriggers`, `spending_patterns`.`timePatterns` AS `timePatterns`, `spending_patterns`.`locationTriggers` AS `locationTriggers`, `spending_patterns`.`socialTriggers` AS `socialTriggers`, `spending_patterns`.`detectedDate` AS `detectedDate`, `spending_patterns`.`lastOccurrence` AS `lastOccurrence`, `spending_patterns`.`occurrenceCount` AS `occurrenceCount`, `spending_patterns`.`confidenceScore` AS `confidenceScore`, `spending_patterns`.`isActive` AS `isActive`, `spending_patterns`.`userConfirmed` AS `userConfirmed`, `spending_patterns`.`interventionStrategy` AS `interventionStrategy`, `spending_patterns`.`interventionEffectiveness` AS `interventionEffectiveness`, `spending_patterns`.`notes` AS `notes`, `spending_patterns`.`severity` AS `severity`, `spending_patterns`.`impactOnBudget` AS `impactOnBudget`, `spending_patterns`.`relatedPatterns` AS `relatedPatterns`, `spending_patterns`.`seasonalFactor` AS `seasonalFactor`, `spending_patterns`.`stressLevel` AS `stressLevel`, `spending_patterns`.`preventionSuccess` AS `preventionSuccess`, `spending_patterns`.`preventionAttempts` AS `preventionAttempts` FROM spending_patterns WHERE isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfPatternType = 1;
          final int _cursorIndexOfPatternName = 2;
          final int _cursorIndexOfDescription = 3;
          final int _cursorIndexOfTriggerConditions = 4;
          final int _cursorIndexOfAverageAmount = 5;
          final int _cursorIndexOfFrequency = 6;
          final int _cursorIndexOfCategory = 7;
          final int _cursorIndexOfEmotionalTriggers = 8;
          final int _cursorIndexOfTimePatterns = 9;
          final int _cursorIndexOfLocationTriggers = 10;
          final int _cursorIndexOfSocialTriggers = 11;
          final int _cursorIndexOfDetectedDate = 12;
          final int _cursorIndexOfLastOccurrence = 13;
          final int _cursorIndexOfOccurrenceCount = 14;
          final int _cursorIndexOfConfidenceScore = 15;
          final int _cursorIndexOfIsActive = 16;
          final int _cursorIndexOfUserConfirmed = 17;
          final int _cursorIndexOfInterventionStrategy = 18;
          final int _cursorIndexOfInterventionEffectiveness = 19;
          final int _cursorIndexOfNotes = 20;
          final int _cursorIndexOfSeverity = 21;
          final int _cursorIndexOfImpactOnBudget = 22;
          final int _cursorIndexOfRelatedPatterns = 23;
          final int _cursorIndexOfSeasonalFactor = 24;
          final int _cursorIndexOfStressLevel = 25;
          final int _cursorIndexOfPreventionSuccess = 26;
          final int _cursorIndexOfPreventionAttempts = 27;
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingPattern>> getSpendingPatternsByType(final String patternType) {
    final String _sql = "SELECT * FROM spending_patterns WHERE patternType = ? AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (patternType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, patternType);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPatternName = CursorUtil.getColumnIndexOrThrow(_cursor, "patternName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfTriggerConditions = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerConditions");
          final int _cursorIndexOfAverageAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAmount");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfEmotionalTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalTriggers");
          final int _cursorIndexOfTimePatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "timePatterns");
          final int _cursorIndexOfLocationTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "locationTriggers");
          final int _cursorIndexOfSocialTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "socialTriggers");
          final int _cursorIndexOfDetectedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "detectedDate");
          final int _cursorIndexOfLastOccurrence = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOccurrence");
          final int _cursorIndexOfOccurrenceCount = CursorUtil.getColumnIndexOrThrow(_cursor, "occurrenceCount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfUserConfirmed = CursorUtil.getColumnIndexOrThrow(_cursor, "userConfirmed");
          final int _cursorIndexOfInterventionStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionStrategy");
          final int _cursorIndexOfInterventionEffectiveness = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionEffectiveness");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfImpactOnBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "impactOnBudget");
          final int _cursorIndexOfRelatedPatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedPatterns");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfStressLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "stressLevel");
          final int _cursorIndexOfPreventionSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionSuccess");
          final int _cursorIndexOfPreventionAttempts = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionAttempts");
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingPattern>> getSpendingPatternsByCategory(final String category) {
    final String _sql = "SELECT * FROM spending_patterns WHERE category = ? AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPatternName = CursorUtil.getColumnIndexOrThrow(_cursor, "patternName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfTriggerConditions = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerConditions");
          final int _cursorIndexOfAverageAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAmount");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfEmotionalTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalTriggers");
          final int _cursorIndexOfTimePatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "timePatterns");
          final int _cursorIndexOfLocationTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "locationTriggers");
          final int _cursorIndexOfSocialTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "socialTriggers");
          final int _cursorIndexOfDetectedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "detectedDate");
          final int _cursorIndexOfLastOccurrence = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOccurrence");
          final int _cursorIndexOfOccurrenceCount = CursorUtil.getColumnIndexOrThrow(_cursor, "occurrenceCount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfUserConfirmed = CursorUtil.getColumnIndexOrThrow(_cursor, "userConfirmed");
          final int _cursorIndexOfInterventionStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionStrategy");
          final int _cursorIndexOfInterventionEffectiveness = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionEffectiveness");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfImpactOnBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "impactOnBudget");
          final int _cursorIndexOfRelatedPatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedPatterns");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfStressLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "stressLevel");
          final int _cursorIndexOfPreventionSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionSuccess");
          final int _cursorIndexOfPreventionAttempts = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionAttempts");
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingPattern>> getPendingSpendingPatterns() {
    final String _sql = "SELECT `spending_patterns`.`id` AS `id`, `spending_patterns`.`patternType` AS `patternType`, `spending_patterns`.`patternName` AS `patternName`, `spending_patterns`.`description` AS `description`, `spending_patterns`.`triggerConditions` AS `triggerConditions`, `spending_patterns`.`averageAmount` AS `averageAmount`, `spending_patterns`.`frequency` AS `frequency`, `spending_patterns`.`category` AS `category`, `spending_patterns`.`emotionalTriggers` AS `emotionalTriggers`, `spending_patterns`.`timePatterns` AS `timePatterns`, `spending_patterns`.`locationTriggers` AS `locationTriggers`, `spending_patterns`.`socialTriggers` AS `socialTriggers`, `spending_patterns`.`detectedDate` AS `detectedDate`, `spending_patterns`.`lastOccurrence` AS `lastOccurrence`, `spending_patterns`.`occurrenceCount` AS `occurrenceCount`, `spending_patterns`.`confidenceScore` AS `confidenceScore`, `spending_patterns`.`isActive` AS `isActive`, `spending_patterns`.`userConfirmed` AS `userConfirmed`, `spending_patterns`.`interventionStrategy` AS `interventionStrategy`, `spending_patterns`.`interventionEffectiveness` AS `interventionEffectiveness`, `spending_patterns`.`notes` AS `notes`, `spending_patterns`.`severity` AS `severity`, `spending_patterns`.`impactOnBudget` AS `impactOnBudget`, `spending_patterns`.`relatedPatterns` AS `relatedPatterns`, `spending_patterns`.`seasonalFactor` AS `seasonalFactor`, `spending_patterns`.`stressLevel` AS `stressLevel`, `spending_patterns`.`preventionSuccess` AS `preventionSuccess`, `spending_patterns`.`preventionAttempts` AS `preventionAttempts` FROM spending_patterns WHERE userConfirmed IS NULL AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfPatternType = 1;
          final int _cursorIndexOfPatternName = 2;
          final int _cursorIndexOfDescription = 3;
          final int _cursorIndexOfTriggerConditions = 4;
          final int _cursorIndexOfAverageAmount = 5;
          final int _cursorIndexOfFrequency = 6;
          final int _cursorIndexOfCategory = 7;
          final int _cursorIndexOfEmotionalTriggers = 8;
          final int _cursorIndexOfTimePatterns = 9;
          final int _cursorIndexOfLocationTriggers = 10;
          final int _cursorIndexOfSocialTriggers = 11;
          final int _cursorIndexOfDetectedDate = 12;
          final int _cursorIndexOfLastOccurrence = 13;
          final int _cursorIndexOfOccurrenceCount = 14;
          final int _cursorIndexOfConfidenceScore = 15;
          final int _cursorIndexOfIsActive = 16;
          final int _cursorIndexOfUserConfirmed = 17;
          final int _cursorIndexOfInterventionStrategy = 18;
          final int _cursorIndexOfInterventionEffectiveness = 19;
          final int _cursorIndexOfNotes = 20;
          final int _cursorIndexOfSeverity = 21;
          final int _cursorIndexOfImpactOnBudget = 22;
          final int _cursorIndexOfRelatedPatterns = 23;
          final int _cursorIndexOfSeasonalFactor = 24;
          final int _cursorIndexOfStressLevel = 25;
          final int _cursorIndexOfPreventionSuccess = 26;
          final int _cursorIndexOfPreventionAttempts = 27;
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingPattern>> getConfirmedSpendingPatterns() {
    final String _sql = "SELECT `spending_patterns`.`id` AS `id`, `spending_patterns`.`patternType` AS `patternType`, `spending_patterns`.`patternName` AS `patternName`, `spending_patterns`.`description` AS `description`, `spending_patterns`.`triggerConditions` AS `triggerConditions`, `spending_patterns`.`averageAmount` AS `averageAmount`, `spending_patterns`.`frequency` AS `frequency`, `spending_patterns`.`category` AS `category`, `spending_patterns`.`emotionalTriggers` AS `emotionalTriggers`, `spending_patterns`.`timePatterns` AS `timePatterns`, `spending_patterns`.`locationTriggers` AS `locationTriggers`, `spending_patterns`.`socialTriggers` AS `socialTriggers`, `spending_patterns`.`detectedDate` AS `detectedDate`, `spending_patterns`.`lastOccurrence` AS `lastOccurrence`, `spending_patterns`.`occurrenceCount` AS `occurrenceCount`, `spending_patterns`.`confidenceScore` AS `confidenceScore`, `spending_patterns`.`isActive` AS `isActive`, `spending_patterns`.`userConfirmed` AS `userConfirmed`, `spending_patterns`.`interventionStrategy` AS `interventionStrategy`, `spending_patterns`.`interventionEffectiveness` AS `interventionEffectiveness`, `spending_patterns`.`notes` AS `notes`, `spending_patterns`.`severity` AS `severity`, `spending_patterns`.`impactOnBudget` AS `impactOnBudget`, `spending_patterns`.`relatedPatterns` AS `relatedPatterns`, `spending_patterns`.`seasonalFactor` AS `seasonalFactor`, `spending_patterns`.`stressLevel` AS `stressLevel`, `spending_patterns`.`preventionSuccess` AS `preventionSuccess`, `spending_patterns`.`preventionAttempts` AS `preventionAttempts` FROM spending_patterns WHERE userConfirmed = 1 AND isActive = 1 ORDER BY lastOccurrence DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfPatternType = 1;
          final int _cursorIndexOfPatternName = 2;
          final int _cursorIndexOfDescription = 3;
          final int _cursorIndexOfTriggerConditions = 4;
          final int _cursorIndexOfAverageAmount = 5;
          final int _cursorIndexOfFrequency = 6;
          final int _cursorIndexOfCategory = 7;
          final int _cursorIndexOfEmotionalTriggers = 8;
          final int _cursorIndexOfTimePatterns = 9;
          final int _cursorIndexOfLocationTriggers = 10;
          final int _cursorIndexOfSocialTriggers = 11;
          final int _cursorIndexOfDetectedDate = 12;
          final int _cursorIndexOfLastOccurrence = 13;
          final int _cursorIndexOfOccurrenceCount = 14;
          final int _cursorIndexOfConfidenceScore = 15;
          final int _cursorIndexOfIsActive = 16;
          final int _cursorIndexOfUserConfirmed = 17;
          final int _cursorIndexOfInterventionStrategy = 18;
          final int _cursorIndexOfInterventionEffectiveness = 19;
          final int _cursorIndexOfNotes = 20;
          final int _cursorIndexOfSeverity = 21;
          final int _cursorIndexOfImpactOnBudget = 22;
          final int _cursorIndexOfRelatedPatterns = 23;
          final int _cursorIndexOfSeasonalFactor = 24;
          final int _cursorIndexOfStressLevel = 25;
          final int _cursorIndexOfPreventionSuccess = 26;
          final int _cursorIndexOfPreventionAttempts = 27;
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingPattern>> getSpendingPatternsBySeverity(final String severity) {
    final String _sql = "SELECT * FROM spending_patterns WHERE severity = ? AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (severity == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, severity);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPatternName = CursorUtil.getColumnIndexOrThrow(_cursor, "patternName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfTriggerConditions = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerConditions");
          final int _cursorIndexOfAverageAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAmount");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfEmotionalTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalTriggers");
          final int _cursorIndexOfTimePatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "timePatterns");
          final int _cursorIndexOfLocationTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "locationTriggers");
          final int _cursorIndexOfSocialTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "socialTriggers");
          final int _cursorIndexOfDetectedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "detectedDate");
          final int _cursorIndexOfLastOccurrence = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOccurrence");
          final int _cursorIndexOfOccurrenceCount = CursorUtil.getColumnIndexOrThrow(_cursor, "occurrenceCount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfUserConfirmed = CursorUtil.getColumnIndexOrThrow(_cursor, "userConfirmed");
          final int _cursorIndexOfInterventionStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionStrategy");
          final int _cursorIndexOfInterventionEffectiveness = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionEffectiveness");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfImpactOnBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "impactOnBudget");
          final int _cursorIndexOfRelatedPatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedPatterns");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfStressLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "stressLevel");
          final int _cursorIndexOfPreventionSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionSuccess");
          final int _cursorIndexOfPreventionAttempts = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionAttempts");
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSpendingPatternById(final long id,
      final Continuation<? super SpendingPattern> $completion) {
    final String _sql = "SELECT * FROM spending_patterns WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SpendingPattern>() {
      @Override
      @Nullable
      public SpendingPattern call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPatternName = CursorUtil.getColumnIndexOrThrow(_cursor, "patternName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfTriggerConditions = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerConditions");
          final int _cursorIndexOfAverageAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAmount");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfEmotionalTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalTriggers");
          final int _cursorIndexOfTimePatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "timePatterns");
          final int _cursorIndexOfLocationTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "locationTriggers");
          final int _cursorIndexOfSocialTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "socialTriggers");
          final int _cursorIndexOfDetectedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "detectedDate");
          final int _cursorIndexOfLastOccurrence = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOccurrence");
          final int _cursorIndexOfOccurrenceCount = CursorUtil.getColumnIndexOrThrow(_cursor, "occurrenceCount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfUserConfirmed = CursorUtil.getColumnIndexOrThrow(_cursor, "userConfirmed");
          final int _cursorIndexOfInterventionStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionStrategy");
          final int _cursorIndexOfInterventionEffectiveness = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionEffectiveness");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfImpactOnBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "impactOnBudget");
          final int _cursorIndexOfRelatedPatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedPatterns");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfStressLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "stressLevel");
          final int _cursorIndexOfPreventionSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionSuccess");
          final int _cursorIndexOfPreventionAttempts = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionAttempts");
          final SpendingPattern _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _result = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SpendingPattern>> getHighConfidencePatterns(final double minConfidence) {
    final String _sql = "SELECT * FROM spending_patterns WHERE confidenceScore >= ? AND isActive = 1 ORDER BY confidenceScore DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minConfidence);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPatternName = CursorUtil.getColumnIndexOrThrow(_cursor, "patternName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfTriggerConditions = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerConditions");
          final int _cursorIndexOfAverageAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAmount");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfEmotionalTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalTriggers");
          final int _cursorIndexOfTimePatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "timePatterns");
          final int _cursorIndexOfLocationTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "locationTriggers");
          final int _cursorIndexOfSocialTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "socialTriggers");
          final int _cursorIndexOfDetectedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "detectedDate");
          final int _cursorIndexOfLastOccurrence = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOccurrence");
          final int _cursorIndexOfOccurrenceCount = CursorUtil.getColumnIndexOrThrow(_cursor, "occurrenceCount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfUserConfirmed = CursorUtil.getColumnIndexOrThrow(_cursor, "userConfirmed");
          final int _cursorIndexOfInterventionStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionStrategy");
          final int _cursorIndexOfInterventionEffectiveness = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionEffectiveness");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfImpactOnBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "impactOnBudget");
          final int _cursorIndexOfRelatedPatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedPatterns");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfStressLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "stressLevel");
          final int _cursorIndexOfPreventionSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionSuccess");
          final int _cursorIndexOfPreventionAttempts = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionAttempts");
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SpendingPattern>> getRecentSpendingPatterns(final LocalDateTime recentDate) {
    final String _sql = "SELECT * FROM spending_patterns WHERE lastOccurrence >= ? AND isActive = 1 ORDER BY lastOccurrence DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromLocalDateTime(recentDate);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"spending_patterns"}, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPatternName = CursorUtil.getColumnIndexOrThrow(_cursor, "patternName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfTriggerConditions = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerConditions");
          final int _cursorIndexOfAverageAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAmount");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfEmotionalTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalTriggers");
          final int _cursorIndexOfTimePatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "timePatterns");
          final int _cursorIndexOfLocationTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "locationTriggers");
          final int _cursorIndexOfSocialTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "socialTriggers");
          final int _cursorIndexOfDetectedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "detectedDate");
          final int _cursorIndexOfLastOccurrence = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOccurrence");
          final int _cursorIndexOfOccurrenceCount = CursorUtil.getColumnIndexOrThrow(_cursor, "occurrenceCount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfUserConfirmed = CursorUtil.getColumnIndexOrThrow(_cursor, "userConfirmed");
          final int _cursorIndexOfInterventionStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionStrategy");
          final int _cursorIndexOfInterventionEffectiveness = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionEffectiveness");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfImpactOnBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "impactOnBudget");
          final int _cursorIndexOfRelatedPatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedPatterns");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfStressLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "stressLevel");
          final int _cursorIndexOfPreventionSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionSuccess");
          final int _cursorIndexOfPreventionAttempts = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionAttempts");
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp_1);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_2);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_3 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_4;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_4 = null;
            } else {
              _tmp_4 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_4 == null ? null : _tmp_4 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getPendingPatternCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM spending_patterns WHERE userConfirmed IS NULL AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getConfirmedPatternCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM spending_patterns WHERE userConfirmed = 1 AND isActive = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageConfidenceScore(final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(confidenceScore) FROM spending_patterns WHERE userConfirmed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getDouble(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPatternCountByType(
      final Continuation<? super List<PatternTypeCount>> $completion) {
    final String _sql = "SELECT patternType, COUNT(*) as count FROM spending_patterns WHERE isActive = 1 GROUP BY patternType ORDER BY count DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PatternTypeCount>>() {
      @Override
      @NonNull
      public List<PatternTypeCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPatternType = 0;
          final int _cursorIndexOfCount = 1;
          final List<PatternTypeCount> _result = new ArrayList<PatternTypeCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PatternTypeCount _item;
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new PatternTypeCount(_tmpPatternType,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPatternCountByCategory(
      final Continuation<? super List<PatternCategoryCount>> $completion) {
    final String _sql = "SELECT category, COUNT(*) as count FROM spending_patterns WHERE isActive = 1 GROUP BY category ORDER BY count DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PatternCategoryCount>>() {
      @Override
      @NonNull
      public List<PatternCategoryCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCategory = 0;
          final int _cursorIndexOfCount = 1;
          final List<PatternCategoryCount> _result = new ArrayList<PatternCategoryCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PatternCategoryCount _item;
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new PatternCategoryCount(_tmpCategory,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getMostEffectiveInterventions(final int limit,
      final Continuation<? super List<SpendingPattern>> $completion) {
    final String _sql = "SELECT * FROM spending_patterns WHERE interventionEffectiveness IS NOT NULL ORDER BY interventionEffectiveness DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SpendingPattern>>() {
      @Override
      @NonNull
      public List<SpendingPattern> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPatternName = CursorUtil.getColumnIndexOrThrow(_cursor, "patternName");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfTriggerConditions = CursorUtil.getColumnIndexOrThrow(_cursor, "triggerConditions");
          final int _cursorIndexOfAverageAmount = CursorUtil.getColumnIndexOrThrow(_cursor, "averageAmount");
          final int _cursorIndexOfFrequency = CursorUtil.getColumnIndexOrThrow(_cursor, "frequency");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfEmotionalTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalTriggers");
          final int _cursorIndexOfTimePatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "timePatterns");
          final int _cursorIndexOfLocationTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "locationTriggers");
          final int _cursorIndexOfSocialTriggers = CursorUtil.getColumnIndexOrThrow(_cursor, "socialTriggers");
          final int _cursorIndexOfDetectedDate = CursorUtil.getColumnIndexOrThrow(_cursor, "detectedDate");
          final int _cursorIndexOfLastOccurrence = CursorUtil.getColumnIndexOrThrow(_cursor, "lastOccurrence");
          final int _cursorIndexOfOccurrenceCount = CursorUtil.getColumnIndexOrThrow(_cursor, "occurrenceCount");
          final int _cursorIndexOfConfidenceScore = CursorUtil.getColumnIndexOrThrow(_cursor, "confidenceScore");
          final int _cursorIndexOfIsActive = CursorUtil.getColumnIndexOrThrow(_cursor, "isActive");
          final int _cursorIndexOfUserConfirmed = CursorUtil.getColumnIndexOrThrow(_cursor, "userConfirmed");
          final int _cursorIndexOfInterventionStrategy = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionStrategy");
          final int _cursorIndexOfInterventionEffectiveness = CursorUtil.getColumnIndexOrThrow(_cursor, "interventionEffectiveness");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfSeverity = CursorUtil.getColumnIndexOrThrow(_cursor, "severity");
          final int _cursorIndexOfImpactOnBudget = CursorUtil.getColumnIndexOrThrow(_cursor, "impactOnBudget");
          final int _cursorIndexOfRelatedPatterns = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedPatterns");
          final int _cursorIndexOfSeasonalFactor = CursorUtil.getColumnIndexOrThrow(_cursor, "seasonalFactor");
          final int _cursorIndexOfStressLevel = CursorUtil.getColumnIndexOrThrow(_cursor, "stressLevel");
          final int _cursorIndexOfPreventionSuccess = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionSuccess");
          final int _cursorIndexOfPreventionAttempts = CursorUtil.getColumnIndexOrThrow(_cursor, "preventionAttempts");
          final List<SpendingPattern> _result = new ArrayList<SpendingPattern>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SpendingPattern _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final String _tmpPatternName;
            if (_cursor.isNull(_cursorIndexOfPatternName)) {
              _tmpPatternName = null;
            } else {
              _tmpPatternName = _cursor.getString(_cursorIndexOfPatternName);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final String _tmpTriggerConditions;
            if (_cursor.isNull(_cursorIndexOfTriggerConditions)) {
              _tmpTriggerConditions = null;
            } else {
              _tmpTriggerConditions = _cursor.getString(_cursorIndexOfTriggerConditions);
            }
            final double _tmpAverageAmount;
            _tmpAverageAmount = _cursor.getDouble(_cursorIndexOfAverageAmount);
            final String _tmpFrequency;
            if (_cursor.isNull(_cursorIndexOfFrequency)) {
              _tmpFrequency = null;
            } else {
              _tmpFrequency = _cursor.getString(_cursorIndexOfFrequency);
            }
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            final String _tmpEmotionalTriggers;
            if (_cursor.isNull(_cursorIndexOfEmotionalTriggers)) {
              _tmpEmotionalTriggers = null;
            } else {
              _tmpEmotionalTriggers = _cursor.getString(_cursorIndexOfEmotionalTriggers);
            }
            final String _tmpTimePatterns;
            if (_cursor.isNull(_cursorIndexOfTimePatterns)) {
              _tmpTimePatterns = null;
            } else {
              _tmpTimePatterns = _cursor.getString(_cursorIndexOfTimePatterns);
            }
            final String _tmpLocationTriggers;
            if (_cursor.isNull(_cursorIndexOfLocationTriggers)) {
              _tmpLocationTriggers = null;
            } else {
              _tmpLocationTriggers = _cursor.getString(_cursorIndexOfLocationTriggers);
            }
            final String _tmpSocialTriggers;
            if (_cursor.isNull(_cursorIndexOfSocialTriggers)) {
              _tmpSocialTriggers = null;
            } else {
              _tmpSocialTriggers = _cursor.getString(_cursorIndexOfSocialTriggers);
            }
            final LocalDateTime _tmpDetectedDate;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfDetectedDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfDetectedDate);
            }
            _tmpDetectedDate = __converters.toLocalDateTime(_tmp);
            final LocalDateTime _tmpLastOccurrence;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfLastOccurrence)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfLastOccurrence);
            }
            _tmpLastOccurrence = __converters.toLocalDateTime(_tmp_1);
            final int _tmpOccurrenceCount;
            _tmpOccurrenceCount = _cursor.getInt(_cursorIndexOfOccurrenceCount);
            final double _tmpConfidenceScore;
            _tmpConfidenceScore = _cursor.getDouble(_cursorIndexOfConfidenceScore);
            final boolean _tmpIsActive;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsActive);
            _tmpIsActive = _tmp_2 != 0;
            final Boolean _tmpUserConfirmed;
            final Integer _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUserConfirmed)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getInt(_cursorIndexOfUserConfirmed);
            }
            _tmpUserConfirmed = _tmp_3 == null ? null : _tmp_3 != 0;
            final String _tmpInterventionStrategy;
            if (_cursor.isNull(_cursorIndexOfInterventionStrategy)) {
              _tmpInterventionStrategy = null;
            } else {
              _tmpInterventionStrategy = _cursor.getString(_cursorIndexOfInterventionStrategy);
            }
            final Double _tmpInterventionEffectiveness;
            if (_cursor.isNull(_cursorIndexOfInterventionEffectiveness)) {
              _tmpInterventionEffectiveness = null;
            } else {
              _tmpInterventionEffectiveness = _cursor.getDouble(_cursorIndexOfInterventionEffectiveness);
            }
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            final String _tmpSeverity;
            if (_cursor.isNull(_cursorIndexOfSeverity)) {
              _tmpSeverity = null;
            } else {
              _tmpSeverity = _cursor.getString(_cursorIndexOfSeverity);
            }
            final String _tmpImpactOnBudget;
            if (_cursor.isNull(_cursorIndexOfImpactOnBudget)) {
              _tmpImpactOnBudget = null;
            } else {
              _tmpImpactOnBudget = _cursor.getString(_cursorIndexOfImpactOnBudget);
            }
            final String _tmpRelatedPatterns;
            if (_cursor.isNull(_cursorIndexOfRelatedPatterns)) {
              _tmpRelatedPatterns = null;
            } else {
              _tmpRelatedPatterns = _cursor.getString(_cursorIndexOfRelatedPatterns);
            }
            final double _tmpSeasonalFactor;
            _tmpSeasonalFactor = _cursor.getDouble(_cursorIndexOfSeasonalFactor);
            final Integer _tmpStressLevel;
            if (_cursor.isNull(_cursorIndexOfStressLevel)) {
              _tmpStressLevel = null;
            } else {
              _tmpStressLevel = _cursor.getInt(_cursorIndexOfStressLevel);
            }
            final int _tmpPreventionSuccess;
            _tmpPreventionSuccess = _cursor.getInt(_cursorIndexOfPreventionSuccess);
            final int _tmpPreventionAttempts;
            _tmpPreventionAttempts = _cursor.getInt(_cursorIndexOfPreventionAttempts);
            _item = new SpendingPattern(_tmpId,_tmpPatternType,_tmpPatternName,_tmpDescription,_tmpTriggerConditions,_tmpAverageAmount,_tmpFrequency,_tmpCategory,_tmpEmotionalTriggers,_tmpTimePatterns,_tmpLocationTriggers,_tmpSocialTriggers,_tmpDetectedDate,_tmpLastOccurrence,_tmpOccurrenceCount,_tmpConfidenceScore,_tmpIsActive,_tmpUserConfirmed,_tmpInterventionStrategy,_tmpInterventionEffectiveness,_tmpNotes,_tmpSeverity,_tmpImpactOnBudget,_tmpRelatedPatterns,_tmpSeasonalFactor,_tmpStressLevel,_tmpPreventionSuccess,_tmpPreventionAttempts);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPatternsWithSuccessfulPrevention(
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM spending_patterns WHERE preventionSuccess > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalPreventionSuccesses(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT SUM(preventionSuccess) FROM spending_patterns";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalPreventionAttempts(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT SUM(preventionAttempts) FROM spending_patterns";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
