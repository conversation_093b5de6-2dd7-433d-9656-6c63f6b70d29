7app/src/main/java/com/focusflow/FocusFlowApplication.kt/app/src/main/java/com/focusflow/MainActivity.kt<app/src/main/java/com/focusflow/data/dao/AIInteractionDao.ktDapp/src/main/java/com/focusflow/data/dao/AccountabilityContactDao.ktAapp/src/main/java/com/focusflow/data/dao/AlternativeProductDao.kt>app/src/main/java/com/focusflow/data/dao/BudgetAnalyticsDao.kt=app/src/main/java/com/focusflow/data/dao/BudgetCategoryDao.ktCapp/src/main/java/com/focusflow/data/dao/BudgetRecommendationDao.kt9app/src/main/java/com/focusflow/data/dao/CreditCardDao.kt>app/src/main/java/com/focusflow/data/dao/DashboardWidgetDao.kt6app/src/main/java/com/focusflow/data/dao/ExpenseDao.kt;app/src/main/java/com/focusflow/data/dao/FocusSessionDao.kt;app/src/main/java/com/focusflow/data/dao/GamificationDao.kt7app/src/main/java/com/focusflow/data/dao/HabitLogDao.kt>app/src/main/java/com/focusflow/data/dao/PaymentScheduleDao.kt>app/src/main/java/com/focusflow/data/dao/PayoffMilestoneDao.kt9app/src/main/java/com/focusflow/data/dao/PayoffPlanDao.kt>app/src/main/java/com/focusflow/data/dao/SpendingPatternDao.ktAapp/src/main/java/com/focusflow/data/dao/SpendingReflectionDao.kt3app/src/main/java/com/focusflow/data/dao/TaskDao.kt>app/src/main/java/com/focusflow/data/dao/UserPreferencesDao.kt;app/src/main/java/com/focusflow/data/dao/VoiceCommandDao.kt;app/src/main/java/com/focusflow/data/dao/WishlistItemDao.kt;app/src/main/java/com/focusflow/data/database/Converters.ktBapp/src/main/java/com/focusflow/data/database/FocusFlowDatabase.kt;app/src/main/java/com/focusflow/data/model/AIInteraction.ktCapp/src/main/java/com/focusflow/data/model/AccountabilityContact.kt9app/src/main/java/com/focusflow/data/model/Achievement.kt@app/src/main/java/com/focusflow/data/model/AlternativeProduct.kt=app/src/main/java/com/focusflow/data/model/BudgetAnalytics.kt<app/src/main/java/com/focusflow/data/model/BudgetCategory.ktBapp/src/main/java/com/focusflow/data/model/BudgetRecommendation.kt8app/src/main/java/com/focusflow/data/model/CreditCard.kt=app/src/main/java/com/focusflow/data/model/DashboardWidget.kt5app/src/main/java/com/focusflow/data/model/Expense.kt:app/src/main/java/com/focusflow/data/model/FocusSession.kt6app/src/main/java/com/focusflow/data/model/HabitLog.kt4app/src/main/java/com/focusflow/data/model/Income.kt>app/src/main/java/com/focusflow/data/model/PayoffComparison.kt8app/src/main/java/com/focusflow/data/model/PayoffPlan.kt=app/src/main/java/com/focusflow/data/model/SpendingPattern.kt@app/src/main/java/com/focusflow/data/model/SpendingReflection.kt2app/src/main/java/com/focusflow/data/model/Task.kt=app/src/main/java/com/focusflow/data/model/UserPreferences.kt:app/src/main/java/com/focusflow/data/model/VoiceCommand.kt:app/src/main/java/com/focusflow/data/model/WishlistItem.ktJapp/src/main/java/com/focusflow/data/repository/AIInteractionRepository.ktLapp/src/main/java/com/focusflow/data/repository/BudgetAnalyticsRepository.ktKapp/src/main/java/com/focusflow/data/repository/BudgetCategoryRepository.ktQapp/src/main/java/com/focusflow/data/repository/BudgetRecommendationRepository.ktGapp/src/main/java/com/focusflow/data/repository/CreditCardRepository.ktDapp/src/main/java/com/focusflow/data/repository/ExpenseRepository.ktBapp/src/main/java/com/focusflow/data/repository/HabitRepository.ktIapp/src/main/java/com/focusflow/data/repository/NotificationRepository.ktMapp/src/main/java/com/focusflow/data/repository/OptimizedExpenseRepository.ktGapp/src/main/java/com/focusflow/data/repository/PayoffPlanRepository.ktOapp/src/main/java/com/focusflow/data/repository/SpendingReflectionRepository.ktAapp/src/main/java/com/focusflow/data/repository/TaskRepository.ktLapp/src/main/java/com/focusflow/data/repository/UserPreferencesRepository.ktGapp/src/main/java/com/focusflow/data/repository/VirtualPetRepository.ktEapp/src/main/java/com/focusflow/data/repository/WishlistRepository.kt.app/src/main/java/com/focusflow/di/AIModule.kt4app/src/main/java/com/focusflow/di/DatabaseModule.kt8app/src/main/java/com/focusflow/navigation/Navigation.kt9app/src/main/java/com/focusflow/receiver/AlarmReceiver.ktAapp/src/main/java/com/focusflow/security/DataProtectionService.kt;app/src/main/java/com/focusflow/security/SecurityManager.kt4app/src/main/java/com/focusflow/service/AIService.ktCapp/src/main/java/com/focusflow/service/AdvancedAnalyticsService.ktFapp/src/main/java/com/focusflow/service/BudgetRecommendationService.kt<app/src/main/java/com/focusflow/service/FocusTimerService.kt>app/src/main/java/com/focusflow/service/GamificationService.kt>app/src/main/java/com/focusflow/service/NotificationManager.kt>app/src/main/java/com/focusflow/service/NotificationService.ktIapp/src/main/java/com/focusflow/service/PerformanceOptimizationService.kt?app/src/main/java/com/focusflow/service/PurchaseDelayService.kt<app/src/main/java/com/focusflow/service/SampleDataService.kt<app/src/main/java/com/focusflow/service/VoiceInputService.ktGapp/src/main/java/com/focusflow/ui/components/ADHDFriendlyComponents.ktDapp/src/main/java/com/focusflow/ui/components/BottomNavigationBar.ktJapp/src/main/java/com/focusflow/ui/components/DecisionSupportComponents.ktGapp/src/main/java/com/focusflow/ui/components/EnhancedADHDComponents.ktIapp/src/main/java/com/focusflow/ui/components/EnhancedBudgetComponents.ktFapp/src/main/java/com/focusflow/ui/components/EnhancedExpenseDialog.ktIapp/src/main/java/com/focusflow/ui/components/EnvelopeBudgetComponents.ktHapp/src/main/java/com/focusflow/ui/components/ErrorHandlingComponents.ktEapp/src/main/java/com/focusflow/ui/components/FocusTimerComponents.ktIapp/src/main/java/com/focusflow/ui/components/ImpulseControlComponents.ktKapp/src/main/java/com/focusflow/ui/components/ImpulseControlSettingsCard.ktDapp/src/main/java/com/focusflow/ui/components/OptimizedComponents.ktHapp/src/main/java/com/focusflow/ui/components/PayoffPlannerComponents.kt@app/src/main/java/com/focusflow/ui/components/SettingsDialogs.ktHapp/src/main/java/com/focusflow/ui/components/ThemeSettingsComponents.ktJapp/src/main/java/com/focusflow/ui/components/VisualAnalyticsComponents.ktEapp/src/main/java/com/focusflow/ui/components/VoiceInputComponents.ktCapp/src/main/java/com/focusflow/ui/onboarding/OnboardingActivity.ktAapp/src/main/java/com/focusflow/ui/onboarding/OnboardingScreen.ktHapp/src/main/java/com/focusflow/ui/responsive/ResponsiveLayoutManager.kt=app/src/main/java/com/focusflow/ui/screens/DashboardScreen.kt8app/src/main/java/com/focusflow/ui/screens/DebtScreen.ktBapp/src/main/java/com/focusflow/ui/screens/EnhancedBudgetScreen.ktAapp/src/main/java/com/focusflow/ui/screens/PayoffPlannerScreen.kt@app/src/main/java/com/focusflow/ui/screens/PlaceholderScreens.kt<app/src/main/java/com/focusflow/ui/screens/SettingsScreen.kt1app/src/main/java/com/focusflow/ui/theme/Color.kt1app/src/main/java/com/focusflow/ui/theme/Shape.kt1app/src/main/java/com/focusflow/ui/theme/Theme.kt0app/src/main/java/com/focusflow/ui/theme/Type.ktBapp/src/main/java/com/focusflow/ui/validation/ADHDDesignAuditor.ktAapp/src/main/java/com/focusflow/ui/validation/ADHDDesignReport.kt@app/src/main/java/com/focusflow/ui/viewmodel/AICoachViewModel.kt?app/src/main/java/com/focusflow/ui/viewmodel/BudgetViewModel.ktBapp/src/main/java/com/focusflow/ui/viewmodel/DashboardViewModel.kt=app/src/main/java/com/focusflow/ui/viewmodel/DebtViewModel.ktGapp/src/main/java/com/focusflow/ui/viewmodel/EnhancedBudgetViewModel.kt@app/src/main/java/com/focusflow/ui/viewmodel/ExpenseViewModel.kt>app/src/main/java/com/focusflow/ui/viewmodel/HabitViewModel.ktGapp/src/main/java/com/focusflow/ui/viewmodel/ImpulseControlViewModel.kt=app/src/main/java/com/focusflow/ui/viewmodel/MainViewModel.ktCapp/src/main/java/com/focusflow/ui/viewmodel/OnboardingViewModel.ktFapp/src/main/java/com/focusflow/ui/viewmodel/PayoffPlannerViewModel.ktAapp/src/main/java/com/focusflow/ui/viewmodel/SettingsViewModel.kt=app/src/main/java/com/focusflow/ui/viewmodel/TaskViewModel.ktCapp/src/main/java/com/focusflow/ui/viewmodel/VirtualPetViewModel.ktCapp/src/main/java/com/focusflow/ui/viewmodel/ZeroBudgetViewModel.kt<app/src/main/java/com/focusflow/utils/ADHDDesignValidator.ktAapp/src/main/java/com/focusflow/utils/DatabaseTransactionUtils.kt6app/src/main/java/com/focusflow/utils/ErrorHandling.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        