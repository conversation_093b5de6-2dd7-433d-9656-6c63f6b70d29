package com.focusflow.accessibility

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import androidx.test.uiautomator.UiDevice
import com.focusflow.MainActivity
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Comprehensive accessibility tests specifically designed for ADHD users
 * Tests cognitive load reduction, visual clarity, and interaction patterns
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class ADHDAccessibilityTest {

    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)

    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    private lateinit var device: UiDevice

    @Before
    fun setup() {
        hiltRule.inject()
        device = UiDevice.getInstance(InstrumentationRegistry.getInstrumentation())
    }

    @Test
    fun visualHierarchy_reducescognitiveLoad() {
        composeTestRule.apply {
            // Test that most important information is visually prominent
            val safeToSpendNode = onNodeWithText("Safe to Spend")
            safeToSpendNode.assertIsDisplayed()
            
            // Verify visual hierarchy through semantic properties
            onNode(hasTestTag("primary_content"))
                .assertIsDisplayed()
            
            onNode(hasTestTag("secondary_content"))
                .assertIsDisplayed()
            
            // Primary content should be more prominent than secondary
            // This would be verified through layout bounds and styling
        }
    }

    @Test
    fun colorContrast_meetsAccessibilityStandards() {
        composeTestRule.apply {
            // All text should have sufficient contrast
            // This test would use accessibility testing tools to verify
            // WCAG AA compliance (4.5:1 for normal text, 3:1 for large text)
            
            onAllNodes(hasText("", substring = true))
                .assertAll(isDisplayed())
            
            // Test specific color combinations used in the app
            onNode(hasTestTag("high_contrast_text"))
                .assertIsDisplayed()
            
            onNode(hasTestTag("warning_text"))
                .assertIsDisplayed()
            
            onNode(hasTestTag("success_text"))
                .assertIsDisplayed()
        }
    }

    @Test
    fun touchTargets_meetMinimumSizeRequirements() {
        composeTestRule.apply {
            // All interactive elements should be at least 48dp x 48dp
            onAllNodes(hasClickAction())
                .assertAll(hasMinimumTouchTargetSize())
            
            // Verify specific critical buttons
            onNodeWithText("Add Expense")
                .assertHeightIsAtLeast(48.dp)
                .assertWidthIsAtLeast(48.dp)
            
            onNodeWithText("Pay Bills")
                .assertHeightIsAtLeast(48.dp)
                .assertWidthIsAtLeast(48.dp)
            
            // Navigation items should also meet size requirements
            onAllNodes(hasTestTag("nav_item"))
                .assertAll(hasMinimumTouchTargetSize())
        }
    }

    @Test
    fun screenReader_providesComprehensiveInformation() {
        composeTestRule.apply {
            // All interactive elements should have meaningful content descriptions
            onNodeWithText("Add Expense")
                .assertContentDescriptionContains("Add new expense")
            
            onNode(hasTestTag("safe_to_spend_amount"))
                .assertContentDescriptionContains("Safe to spend amount")
            
            onNode(hasTestTag("virtual_pet"))
                .assertContentDescriptionContains("Virtual pet")
            
            // Complex UI elements should have comprehensive descriptions
            onNode(hasTestTag("credit_card_summary"))
                .assertContentDescriptionContains("Credit card summary")
        }
    }

    @Test
    fun focusManagement_supportsKeyboardNavigation() {
        composeTestRule.apply {
            // Test tab navigation order
            onNodeWithText("Add Expense")
                .requestFocus()
                .assertIsFocused()
            
            // Verify focus moves logically through the interface
            // This would test the tab order for keyboard users
            
            // Focus should be clearly visible
            onFocusedNode()
                .assertIsDisplayed()
        }
    }

    @Test
    fun textScaling_worksWithSystemSettings() {
        composeTestRule.apply {
            // Test that text scales properly with system font size settings
            // This would involve changing system settings and verifying layout
            
            // All text should remain readable at different scales
            onAllNodes(hasText("", substring = true))
                .assertAll(isDisplayed())
            
            // Layout should not break with larger text
            onNodeWithText("Safe to Spend")
                .assertIsDisplayed()
        }
    }

    @Test
    fun reducedMotion_respectsUserPreferences() {
        composeTestRule.apply {
            // Test that animations can be disabled for users who need it
            // This would check system accessibility settings
            
            // Critical information should be available without animations
            onNodeWithText("Safe to Spend")
                .assertIsDisplayed()
            
            // Transitions should not be essential for understanding
        }
    }

    @Test
    fun cognitiveLoad_isMinimized() {
        composeTestRule.apply {
            // Test that information is presented in digestible chunks
            
            // Primary information should be immediately visible
            onNodeWithText("Safe to Spend").assertIsDisplayed()
            
            // Secondary information should be accessible but not overwhelming
            onNodeWithText("View Details")
                .assertIsDisplayed()
                .performClick()
            
            // Detailed information should be well-organized
            onNode(hasTestTag("detailed_view"))
                .assertIsDisplayed()
            
            // Should be easy to return to main view
            onNodeWithContentDescription("Back")
                .assertIsDisplayed()
                .assertHasClickAction()
        }
    }

    @Test
    fun errorMessages_areADHDFriendly() {
        composeTestRule.apply {
            // Error messages should be clear and actionable
            // This test would trigger error states and verify messaging
            
            // Errors should not be overwhelming or shame-based
            onNode(hasTestTag("error_message"))
                .assertTextContains("Let's try that again")
            
            // Should provide clear next steps
            onNode(hasTestTag("error_action"))
                .assertIsDisplayed()
                .assertHasClickAction()
        }
    }

    @Test
    fun progressIndicators_provideAppropriatefeedback() {
        composeTestRule.apply {
            // Loading states should be clear but not anxiety-inducing
            onNode(hasTestTag("loading_indicator"))
                .assertIsDisplayed()
            
            // Progress should be communicated clearly
            onNode(hasTestTag("progress_text"))
                .assertIsDisplayed()
            
            // Should not block critical functionality
            onNodeWithText("Dashboard")
                .assertIsDisplayed()
        }
    }

    @Test
    fun formInputs_supportADHDUsers() {
        composeTestRule.apply {
            // Navigate to a form (e.g., add expense)
            onNodeWithText("Add Expense")
                .performClick()
            
            // Form fields should have clear labels
            onNodeWithText("Amount")
                .assertIsDisplayed()
            
            onNodeWithText("Description")
                .assertIsDisplayed()
            
            // Should provide input validation feedback
            onNode(hasTestTag("amount_input"))
                .performTextInput("invalid")
            
            onNode(hasTestTag("validation_message"))
                .assertIsDisplayed()
                .assertTextContains("Please enter a valid amount")
            
            // Validation should be helpful, not critical
        }
    }

    @Test
    fun notifications_areNonIntrusive() {
        composeTestRule.apply {
            // Test that notifications don't overwhelm users
            // This would test notification behavior and timing
            
            // Success messages should be encouraging
            onNode(hasTestTag("success_message"))
                .assertTextContains("Great job!")
            
            // Should auto-dismiss after reasonable time
            // This would test timing behavior
        }
    }

    @Test
    fun contextSwitching_isMinimized() {
        composeTestRule.apply {
            // Test that users don't need to remember information across screens
            
            // Important context should be maintained
            onNodeWithText("Add Expense")
                .performClick()
            
            // Previous context should be available if needed
            onNode(hasTestTag("context_hint"))
                .assertIsDisplayed()
            
            // Navigation should be predictable
            onNodeWithContentDescription("Back")
                .assertIsDisplayed()
                .performClick()
            
            // Should return to expected state
            onNodeWithText("Dashboard")
                .assertIsDisplayed()
        }
    }

    @Test
    fun visualClutter_isMinimized() {
        composeTestRule.apply {
            // Test that screens are not overwhelming
            
            // Should use progressive disclosure
            onNodeWithText("View Details")
                .assertIsDisplayed()
                .performClick()
            
            // Detailed view should be well-organized
            onNode(hasTestTag("organized_content"))
                .assertIsDisplayed()
            
            // Should be easy to focus on one thing at a time
            onNode(hasTestTag("focused_content"))
                .assertIsDisplayed()
        }
    }

    @Test
    fun motivationalElements_arePositive() {
        composeTestRule.apply {
            // Test that gamification elements are encouraging
            
            // Achievement messages should be positive
            onNode(hasTestTag("achievement_message"))
                .assertTextContains("You're doing great!")
            
            // Progress indicators should be motivating
            onNode(hasTestTag("progress_encouragement"))
                .assertIsDisplayed()
            
            // Should avoid shame-based messaging
            onAllNodes(hasText("failed", substring = true, ignoreCase = true))
                .assertCountEquals(0)
        }
    }

    @Test
    fun timeAwareness_isSupported() {
        composeTestRule.apply {
            // Test that time-sensitive information is clear
            
            // Due dates should be prominently displayed
            onNode(hasTestTag("due_date"))
                .assertIsDisplayed()
            
            // Time remaining should be clear
            onNode(hasTestTag("time_remaining"))
                .assertIsDisplayed()
            
            // Should provide gentle reminders, not stress-inducing alerts
            onNode(hasTestTag("gentle_reminder"))
                .assertIsDisplayed()
        }
    }

    @Test
    fun customization_supportsIndividualNeeds() {
        composeTestRule.apply {
            // Test that users can customize their experience
            
            // Navigate to settings
            onNodeWithText("Settings")
                .performClick()
            
            // Should have ADHD-specific customization options
            onNodeWithText("ADHD Accommodations")
                .assertIsDisplayed()
                .performClick()
            
            // Should offer relevant customizations
            onNodeWithText("Reduce animations")
                .assertIsDisplayed()
            
            onNodeWithText("Increase text size")
                .assertIsDisplayed()
            
            onNodeWithText("Simplify interface")
                .assertIsDisplayed()
        }
    }
}
